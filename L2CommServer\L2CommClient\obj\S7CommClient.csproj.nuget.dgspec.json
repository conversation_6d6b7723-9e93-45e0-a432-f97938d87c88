{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\L2CommServer\\S7CommClient\\S7CommClient.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\L2CommServer\\S7CommClient\\S7CommClient.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\L2CommServer\\S7CommClient\\S7CommClient.csproj", "projectName": "S7CommClient", "projectPath": "C:\\Users\\<USER>\\source\\repos\\L2CommServer\\S7CommClient\\S7CommClient.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\L2CommServer\\S7CommClient\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0-windows7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0-windows7.0": {"targetAlias": "net5.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net5.0-windows7.0": {"targetAlias": "net5.0-windows", "dependencies": {"FontAwesome6.Fonts": {"target": "Package", "version": "[1.0.1, )"}, "HandyControl": {"target": "Package", "version": "[3.3.0, )"}, "NetCoreServer": {"target": "Package", "version": "[5.1.0, )"}, "Serilog": {"target": "Package", "version": "[2.10.0, )"}, "Serilog.Sinks.RichTextBox.Wpf": {"target": "Package", "version": "[1.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.406\\RuntimeIdentifierGraph.json"}}}}}