import { defineConfig } from 'vite';
import { resolve } from 'path';
import fs from 'fs';

// Get all plugin files
function getPluginEntries() {
  const pluginsDir = resolve(__dirname, 'plugins');
  const entries = {};
  
  if (fs.existsSync(pluginsDir)) {
    const files = fs.readdirSync(pluginsDir);
    files.forEach(file => {
      if (file.endsWith('.js') && !file.startsWith('_')) {
        const name = file.replace('.js', '');
        entries[name] = resolve(pluginsDir, file);
      }
    });
  }
  
  return entries;
}

export default defineConfig({
  build: {
    lib: {
      entry: getPluginEntries(),
      formats: ['cjs'], // CommonJS for Node.js compatibility
      fileName: (format, entryName) => `${entryName}.js`
    },
    outDir: 'dist',
    rollupOptions: {
      external: ['../plugin-base'], // Don't bundle plugin-base
      output: {
        globals: {
          '../plugin-base': 'PluginBase'
        }
      }
    },
    target: 'node14', // Target Node.js 14+
    minify: false, // Keep readable for debugging
    sourcemap: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
      '@plugins': resolve(__dirname, 'plugins')
    }
  }
});
