﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:L2CommServer">

    <Style x:Key="Flat" TargetType="Button">
        <Setter Property="Background" Value="{x:Null}" />
        <Setter Property="BorderBrush" Value="DarkGray" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="Border" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" RecognizesAccessKey="True" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                        </Trigger>
                        <Trigger Property="IsDefaulted" Value="true">
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                        </Trigger>
                        <Trigger Property="ToggleButton.IsChecked" Value="true">
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="LightSkyBlue" TargetName="Border"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="FontWeight" Value="Normal" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>

        </Style.Triggers>
    </Style>
</ResourceDictionary>