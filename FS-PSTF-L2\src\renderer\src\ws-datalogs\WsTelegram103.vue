<template>
    <div v-show="isSelected" class="panel">
        <n-space vertical>
            <!--
            <n-card bordered embedded>
                <n-grid :cols="4">
                    <n-gi>
                        <n-statistic label="记录数量">
                            {{ totalRecords }}
                        </n-statistic>  
                    </n-gi>
                </n-grid>
            </n-card>
        -->
            <n-space justify="space-between">
                <div style="width: 460px">
                    <n-form
                        :show-require-mark="false"
                        :show-feedback="false"
                        label-width="90px"
                        label-placement="left"
                        size="small"
                    >
                        <n-space>
                            <n-form-item
                                label="查找日期"
                                path="date"
                                style="width: 240px"
                            >
                                <n-date-picker
                                    v-model:value="inputFilter.date"
                                    @update:value="
                                        (value) => updateFilter('date', value)
                                    "
                                    type="date"
                                    placeholder="日期"
                                    data-testid="input-filter-date"
                                    :clearable="true"
                                />
                            </n-form-item>
                        </n-space>
                    </n-form>
                </div>
                <AutoRefresh
                    :default-refresh="true"
                    :default-interval="30"
                    @refresh="refreshData"
                />
            </n-space>
            <n-data-table
                remote
                :row-key="(row) => row.id"
                :columns="columns"
                :data="telegrams"
                :loading="loading"
                :pagination="paginationRef"
                :on-update:page="handlePageChange"
                :single-line="false"
                titleAlign="center"
                striped
                size="small"
            />
        </n-space>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { DataTableColumns, NSpace } from 'naive-ui'
import { dbTelegram103, Telegram103, Tele103FilterOptions } from '@/models'
import { useDataTable, useFilter } from '@/composables'
// UI components
import { AutoRefresh } from './components'

// 接收父组件传递过来的值
defineProps({
    tabUid: String,
    isSelected: Boolean,
})

const fetchData = async (start: number, pageSize: number) => {
    return await dbTelegram103.fetch(start, pageSize, 'desc', inputFilter.value)
}

const fetchCount = async () => {
    return await dbTelegram103.count(inputFilter.value)
}

const mapTelegram = (tele: Telegram103) => {
    return {
        ...tele,
        isEdit: false,
        isDeletable: false,
    }
}

const {
    telegrams,
    loading,
    pagination: paginationRef,
    handlePageChange,
    refreshData,
    renderTableTitle,
} = useDataTable<Telegram103>(fetchData, fetchCount, mapTelegram, 15)

// setup filter
const { filter: inputFilter, updateFilter } =
    useFilter<Tele103FilterOptions>(refreshData)

const createColumns = (): DataTableColumns<Telegram103> => {
    const columns = [
        {
            title: renderTableTitle('#'),
            key: 'id',
            width: 60,
            className: 'center',
        },
        {
            title: renderTableTitle('步进距离'),
            key: 'distance',
            width: 80,
            className: 'center',
        },
        {
            title: renderTableTitle('发送日期'),
            key: 'create_time',
            width: 200,
            className: 'center',
            render: (rowData: Telegram103, _rowIndex: number) => {
                return rowData.record_time.toLocaleString('zh-CN', {
                    timeZone: 'UTC',
                })
            },
        },
    ]
    // no buttons
    // define buttons column
    // const buttonsCol = {
    //     key: 'options',
    //     width: 100,
    //     title: renderTableTitle('选项'),
    // }

    return [...columns] as unknown as DataTableColumns<Telegram103>
}

// create columns
const columns = ref<DataTableColumns<Telegram103>>()
columns.value = createColumns()

onMounted(async () => {
    refreshData()
})
</script>

<style lang="scss" scoped>
.panel {
    padding: 0;
    margin: 15px;
    height: calc(100vh - 70px - 64px);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    overflow: auto;

    :deep(.n-data-table-td) {
        padding: 6px 6px;
    }

    :deep(.deleting td) {
        background-color: rgba(var(--warning-color), 0.1) !important;
        font-weight: bold;
    }

    :deep(.center) {
        text-align: center;
    }
}
</style>
