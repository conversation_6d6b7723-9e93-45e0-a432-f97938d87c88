USE [FSPSTF-L2]
GO

/****** Object:  Table [dbo].[TelegramHistory]    Script Date: 2024/9/7 16:46:45 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[TelegramHistory](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[direction] [char](1) NOT NULL,
	[tel_no] [smallint] NOT NULL,
	[tel_len] [smallint] NOT NULL,
	[send_id] [char](2) NOT NULL,
	[rec_id] [char](2) NOT NULL,
	[tel_counter] [smallint] NOT NULL,
	[record_time] [datetime] NOT NULL,
 CONSTRAINT [PK_TelegramHistory] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[TelegramHistory] ADD  CONSTRAINT [DF__TelegramH__creat__24927208]  DEFAULT (getdate()) FOR [record_time]
GO


