<template>
    <div>
        <n-scrollbar trigger="none" x-scrollable>
            <n-table striped size="small" :single-line="false">
                <tbody>
                    <tr>
                        <td width="160px">坯料ID</td>
                        <td>{{ billet.Billet_ID }}</td>
                    </tr>
                    <tr>
                        <td>轧制计划号</td>
                        <td>{{ billet.Roll_No }}</td>
                    </tr>
                    <tr>
                        <td>炉批号</td>
                        <td>{{ billet.Batch_No }}</td>
                    </tr>
                    <tr>
                        <td>钢种</td>
                        <td>{{ billet.Steel_Grade }}</td>
                    </tr>
                    <tr>
                        <td>坯料尺寸(长*宽*高) mm</td>
                        <td>
                            {{ billet.Billet_Length }} x
                            {{ billet.Billet_Width }} x 120
                        </td>
                    </tr>
                    <tr>
                        <td>坯料长度 mm</td>
                        <td>{{ billet.Billet_Length }}</td>
                    </tr>
                    <tr>
                        <td>坯料长度 mm</td>
                        <td>{{ billet.Billet_Length }}</td>
                    </tr>
                </tbody>
            </n-table>
        </n-scrollbar>
    </div>
</template>

<script setup lang="ts">
import { BilletData } from '@common/interfaces/tcData'

// define props
defineProps({
    // billet
    billet: {
        type: Object as () => BilletData,
        required: true,
    },
})
</script>

<style lang="scss" scoped>
// 注意: deep的对象必须在一个根节点才能使用
:deep(.n-scrollbar) {
    .n-scrollbar-container {
        max-height: calc(100vh - var(--excluding-size) - 520px);
        max-width: calc(100% - 16px);
    }
    .n-scrollbar-rail {
        --n-scrollbar-width: 8px;
        --n-scrollbar-height: 8px;
        --n-scrollbar-border-radius: 6px;
    }
}
</style>
