import EventEmitter from 'events';
import log from 'electron-log';
import { TcpServerService } from './TcpServer.js';
import { TcpClientManager } from './TcpClient.js';
import { PluginManager } from './PluginManager.js';
import { TelegramProcessor } from './TelegramProcessor.js';
import { EventTypes } from '../../common/interfaces.js';

/**
 * Main Server Service - coordinates all server components
 */
class ServerService extends EventEmitter {
    constructor() {
        super();
        this.tcpServer = new TcpServerService();
        this.tcpClientManager = new TcpClientManager();
        this.pluginManager = new PluginManager();
        this.isRunning = false;
        this.config = null;
        this.statistics = {
            startTime: null,
            connections: 0,
            telegramsReceived: 0,
            telegramsSent: 0,
        };

        this.setupEventHandlers();
    }

    setupEventHandlers() {
        // TCP Server events
        this.tcpServer.on('serverStarted', (data) => {
            log.info('TCP Server started successfully');
            this.emit(EventTypes.SERVER_STARTED, data);
        });

        this.tcpServer.on('serverStopped', () => {
            log.info('TCP Server stopped');
            this.emit(EventTypes.SERVER_STOPPED);
        });

        this.tcpServer.on('connectionChanged', (data) => {
            this.updateConnectionStatistics(data);
            this.emit(EventTypes.CONNECTION_CHANGED, data);
        });

        this.tcpServer.on('telegramProcessed', (data) => {
            this.updateTelegramStatistics(data);
            if (data.type === 'received') {
                this.emit(EventTypes.TELEGRAM_RECEIVED, data);
            } else if (data.type === 'sent') {
                this.emit(EventTypes.TELEGRAM_SENT, data);
            }
        });

        this.tcpServer.on('serverError', (error) => {
            log.error('TCP Server error:', error);
            this.emit('serverError', error);
        });

        // TCP Client Manager events
        this.tcpClientManager.on('clientConnected', (data) => {
            this.updateConnectionStatistics({ connected: true, type: 'A' });
            this.emit(EventTypes.CONNECTION_CHANGED, {
                connected: true,
                sessionId: data.clientId,
                peerIp: data.address,
                type: 'A', // Active connection
            });
        });

        this.tcpClientManager.on('clientDisconnected', (data) => {
            this.updateConnectionStatistics({ connected: false, type: 'A' });
            this.emit(EventTypes.CONNECTION_CHANGED, {
                connected: false,
                sessionId: data.clientId,
                peerIp: data.address,
                type: 'A',
            });
        });

        this.tcpClientManager.on('telegramProcessed', (data) => {
            this.updateTelegramStatistics(data);
            if (data.type === 'received') {
                this.emit(EventTypes.TELEGRAM_RECEIVED, data);
            } else if (data.type === 'sent') {
                this.emit(EventTypes.TELEGRAM_SENT, data);
            }
        });

        this.tcpClientManager.on('clientError', (data) => {
            log.error('TCP Client error:', data);
            this.emit('clientError', data);
        });

        // Plugin Manager events
        this.pluginManager.on('pluginLoaded', (data) => {
            log.info(`Plugin loaded: ${data.pluginName} (${data.type})`);
            this.emit('pluginLoaded', data);
        });

        this.pluginManager.on('pluginReloaded', (data) => {
            log.info(`Plugin reloaded: ${data.filePath}`);
            this.emit('pluginReloaded', data);
        });

        this.pluginManager.on('pluginError', (data) => {
            log.error(`Plugin error: ${data.filePath} - ${data.error}`);
            this.emit('pluginError', data);
        });
    }

    updateConnectionStatistics(data) {
        if (data.connected) {
            this.statistics.connections++;
        } else {
            this.statistics.connections = Math.max(
                0,
                this.statistics.connections - 1
            );
        }
    }

    updateTelegramStatistics(data) {
        if (data.type === 'received') {
            this.statistics.telegramsReceived += data.count || 1;
        } else if (data.type === 'sent') {
            this.statistics.telegramsSent += data.count || 1;
        }
    }

    async start(config) {
        if (this.isRunning) {
            throw new Error('Server is already running');
        }

        try {
            this.config = config;
            this.statistics.startTime = new Date();

            // Initialize plugin manager
            await this.pluginManager.initialize();

            // Set plugin manager in TCP server
            this.tcpServer.setPluginManager(this.pluginManager);

            // Start TCP server
            await this.tcpServer.start(config.server.ip, config.server.port);

            // Set up active client connections
            if (config.clients && config.clients.length > 0) {
                for (const clientConfig of config.clients) {
                    try {
                        this.tcpClientManager.addClient(
                            clientConfig.ip,
                            clientConfig.port,
                            true
                        );
                        log.info(
                            `Added active client connection to ${clientConfig.ip}:${clientConfig.port}`
                        );
                    } catch (error) {
                        log.error(
                            `Failed to add client connection to ${clientConfig.ip}:${clientConfig.port}:`,
                            error
                        );
                    }
                }
            }

            // TODO: Set up cyclic send operations (will be implemented later)
            if (config.cyclicSends && config.cyclicSends.length > 0) {
                log.info(
                    `${config.cyclicSends.length} cyclic send configurations found (not yet implemented)`
                );
            }

            this.isRunning = true;
            log.info('L2CommServer started successfully');
        } catch (error) {
            log.error('Failed to start server:', error);
            await this.stop(); // Clean up on failure
            throw error;
        }
    }

    async stop() {
        if (!this.isRunning) {
            return;
        }

        try {
            log.info('Stopping L2CommServer...');

            // Stop TCP server
            await this.tcpServer.stop();

            // Disconnect all clients
            await this.tcpClientManager.disconnectAll();

            // Cleanup plugin manager
            this.pluginManager.cleanup();

            this.isRunning = false;
            this.statistics.startTime = null;

            log.info('L2CommServer stopped successfully');
        } catch (error) {
            log.error('Error stopping server:', error);
            throw error;
        }
    }

    getStatus() {
        const serverStatus = this.tcpServer.getStatus();
        const clientStats = this.tcpClientManager.getStatistics();

        return {
            isRunning: this.isRunning,
            config: this.config,
            server: serverStatus,
            clients: {
                total: clientStats.totalConnections,
                active: clientStats.activeConnections,
                list: this.tcpClientManager
                    .getAllClients()
                    .map((client) => client.getStatus()),
            },
            statistics: {
                ...this.statistics,
                uptime: this.statistics.startTime
                    ? Math.floor(
                          (Date.now() - this.statistics.startTime.getTime()) /
                              1000
                      )
                    : 0,
                totalConnections:
                    serverStatus.statistics.connections +
                    clientStats.activeConnections,
            },
        };
    }

    async sendTelegram(sessionId, telegramData) {
        // Try to send via server session first
        try {
            this.tcpServer.sendToSession(sessionId, telegramData);
            return;
        } catch (error) {
            // Session not found in server, might be a client
        }

        // Try to find client by ID
        const clients = this.tcpClientManager.getAllClients();
        const client = clients.find((c) => c.id === sessionId);

        if (client && client.isConnected) {
            await client.sendAsync(telegramData);
        } else {
            throw new Error(
                `Session or client ${sessionId} not found or not connected`
            );
        }
    }

    async sendToAllSessions(telegramData) {
        // Send to all server sessions
        this.tcpServer.sendToAll(telegramData);

        // Send to all connected clients
        await this.tcpClientManager.sendToAllClients(telegramData);
    }

    getActiveSessions() {
        const serverSessions = this.tcpServer.getStatus().activeSessions || [];
        const clientSessions = this.tcpClientManager
            .getAllClients()
            .filter((client) => client.isConnected)
            .map((client) => ({
                id: client.id,
                peerIp: client.address,
                peerPort: client.port,
                connectionTime: client.connectionTime,
                type: 'A', // Active
            }));

        return [...serverSessions, ...clientSessions];
    }

    addClient(ip, port) {
        return this.tcpClientManager.addClient(ip, port, true);
    }

    removeClient(ip, port) {
        this.tcpClientManager.removeClient(ip, port);
    }

    getServerStatistics() {
        const status = this.getStatus();
        return {
            connections: status.statistics.totalConnections,
            telegramsReceived: status.statistics.telegramsReceived,
            telegramsSent: status.statistics.telegramsSent,
            uptime: status.statistics.uptime,
            startTime: status.statistics.startTime,
            serverSessions: status.server.statistics.connections,
            clientSessions: status.clients.active,
        };
    }

    /**
     * Get all loaded plugins
     * @returns {Object} - Plugin information
     */
    getAllPlugins() {
        return this.pluginManager.getAllPlugins();
    }

    /**
     * Reload all plugins
     */
    async reloadAllPlugins() {
        await this.pluginManager.reloadAllPlugins();
    }

    /**
     * Get plugin statistics
     * @returns {Object} - Plugin statistics
     */
    getPluginStatistics() {
        return this.pluginManager.getStatistics();
    }

    /**
     * Compose and send telegram using plugin
     * @param {number} telegramNo - Telegram number
     * @param {string} targetIp - Target IP address (optional)
     * @returns {boolean} - Success status
     */
    async sendTelegramWithPlugin(telegramNo, targetIp = null) {
        try {
            // Create a temporary telegram processor to compose the telegram
            const processor = new TelegramProcessor(this.pluginManager);

            const telegram = await processor.composeWithPlugin(telegramNo);

            if (!telegram) {
                return false;
            }

            if (targetIp) {
                // Send to specific target
                const clients = this.tcpClientManager.getAllClients();
                const targetClient = clients.find(
                    (client) => client.address === targetIp
                );

                if (targetClient && targetClient.isConnected) {
                    await targetClient.sendAsync(telegram);
                } else {
                    // Try to send via server sessions
                    const sessions = this.tcpServer.getSessions();
                    const targetSession = sessions.find(
                        (session) => session.peerIp === targetIp
                    );

                    if (targetSession) {
                        targetSession.onSendRequest(telegram);
                    } else {
                        log.warn(
                            `No connection found for target IP: ${targetIp}`
                        );
                        return false;
                    }
                }
            } else {
                // Send to all connections
                await this.sendToAllSessions(telegram);
            }

            return true;
        } catch (error) {
            log.error(`Failed to send telegram ${telegramNo}:`, error);
            return false;
        }
    }
}

export { ServerService };
