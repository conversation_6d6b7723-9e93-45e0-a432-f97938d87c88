<template>
    <n-space :size="6" :style="isEditing ? '{background: red}' : ''">
        <!-- 编辑 -->
        <template v-if="isViewable">
            <n-popover
                placement="top"
                trigger="hover"
                style="background-color: rgb(170, 231, 190)"
            >
                <template #trigger>
                    <n-button
                        text
                        style="font-size: 20px"
                        data-testid="button-view"
                        @click="emits('view-row')"
                    >
                        <div class="i-mdi:magnify text-20px" />
                    </n-button>
                </template>
                <span> 查看 </span>
            </n-popover>
        </template>
        <template v-if="isEditable">
            <n-popover
                placement="top"
                trigger="hover"
                style="background-color: rgb(170, 231, 190)"
            >
                <template #trigger>
                    <n-button
                        text
                        style="font-size: 20px"
                        data-testid="button-edit"
                        @click="emits('edit-row')"
                    >
                        <div class="i-mdi:table-edit text-20px" />
                    </n-button>
                </template>
                <span> 编辑 </span>
            </n-popover>
        </template>
        <!-- 删除 -->
        <template v-if="isDeletable">
            <n-popover
                placement="top"
                trigger="hover"
                style="background-color: rgb(170, 231, 190)"
            >
                <template #trigger>
                    <n-button
                        text
                        style="font-size: 20px"
                        data-testid="button-delete"
                        @click="(e) => deleteConfirm(e)"
                    >
                        <div class="i-mdi:trash-can-outline text-20px" />
                    </n-button>
                </template>
                <span> 删除 </span>
            </n-popover>
        </template>
    </n-space>
    <!-- delete confirmation -->
    <n-popconfirm
        trigger="manual"
        placement="top"
        :show="showDelCfm"
        :x="popconfirmX"
        :y="popconfirmY"
        :data-testid="`confirm-delete`"
        @positive-click="handleConfirm"
        @negative-click="cancelDelete"
        @clickoutside="showDelCfm = false"
    >
        <template #trigger></template>
        确认要删除此行?
    </n-popconfirm>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 接收父组件传递过来的值
defineProps({
    // true if current row is being edited
    isEditing: {
        type: Boolean,
        required: false,
        default: false,
    },
    // true if current row can be viewed in full
    isViewable: {
        type: Boolean,
        required: false,
        default: false,
    },
    // true if current row can be edited
    isEditable: {
        type: Boolean,
        required: false,
        default: false,
    },
    // true if current row can be deleted
    isDeletable: {
        type: Boolean,
        required: false,
        default: false,
    },
})

// define emits
const emits = defineEmits([
    'view-row',
    'edit-row',
    'delete-warning',
    'delete-cancel',
    'delete-row',
])

const showDelCfm = ref(false)
const popconfirmX = ref(0)
const popconfirmY = ref(0)

// show delete item confirmation dialog
const deleteConfirm = (e) => {
    // show confirm box
    showDelCfm.value = true
    // position
    popconfirmX.value = e.clientX
    popconfirmY.value = e.clientY
    // emit singal to parent to highlight
    emits('delete-warning')
}

// confirm delete
const handleConfirm = () => {
    // hide confirm box
    showDelCfm.value = false
    // emit event
    emits('delete-row')
}

// cancel delete
const cancelDelete = () => {
    // hide confirm box
    showDelCfm.value = false
    // emit event
    emits('delete-cancel')
}
</script>

<style lang="scss" scoped>
.n-space {
    margin-top: 5px;
}
</style>
