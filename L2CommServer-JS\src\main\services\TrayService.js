import { app, Tray, Menu, nativeImage, BrowserWindow } from 'electron';
import path from 'path';
import log from 'electron-log';

/**
 * System Tray Service
 * Manages system tray icon and context menu
 */
class TrayService {
    constructor() {
        this.tray = null;
        this.mainWindow = null;
        this.isQuitting = false;
    }

    /**
     * Initialize system tray
     * @param {BrowserWindow} mainWindow - Main application window
     */
    initialize(mainWindow) {
        this.mainWindow = mainWindow;

        try {
            // Create tray icon
            const iconPath = this.getIconPath();
            const icon = nativeImage.createFromPath(iconPath);

            // Resize icon for better display
            const resizedIcon = icon.resize({ width: 16, height: 16 });

            this.tray = new Tray(resizedIcon);

            // Set tooltip
            this.tray.setToolTip(
                'L2 Communication Server - 双击显示窗口，右键菜单'
            );

            // Create context menu
            this.createContextMenu();

            // Handle double click to show window
            this.tray.on('double-click', () => {
                this.showWindow();
            });

            // Handle click events (Windows)
            this.tray.on('click', () => {
                if (process.platform === 'win32') {
                    this.showWindow();
                }
            });

            log.info('System tray initialized successfully');
        } catch (error) {
            log.error('Failed to initialize system tray:', error);
        }
    }

    /**
     * Get icon path based on platform
     * @returns {string} - Icon file path
     */
    getIconPath() {
        const isDev = process.env.NODE_ENV === 'development';

        if (isDev) {
            // Development mode - icon in resources folder
            return path.join(__dirname, '../../../resources/server.ico');
        } else {
            // Production mode - icon in app resources
            return path.join(process.resourcesPath, 'server.ico');
        }
    }

    /**
     * Create context menu for tray icon
     */
    createContextMenu() {
        const contextMenu = Menu.buildFromTemplate([
            {
                label: '显示窗口',
                click: () => {
                    this.showWindow();
                },
                enabled: !this.isWindowVisible(),
            },
            {
                label: '隐藏窗口',
                click: () => {
                    this.hideWindow();
                },
                enabled: this.isWindowVisible(),
            },
            {
                type: 'separator',
            },
            {
                label: '重新加载插件',
                click: () => {
                    this.reloadPlugins();
                },
            },
            {
                label: '服务器状态',
                submenu: [
                    {
                        label: '启动服务器',
                        click: () => {
                            this.startServer();
                        },
                    },
                    {
                        label: '停止服务器',
                        click: () => {
                            this.stopServer();
                        },
                    },
                    {
                        type: 'separator',
                    },
                    {
                        label: '查看连接状态',
                        click: () => {
                            this.showConnectionStatus();
                        },
                    },
                ],
            },
            {
                type: 'separator',
            },
            {
                label: '关于',
                click: () => {
                    this.showAbout();
                },
            },
            {
                label: '退出',
                click: () => {
                    this.exitApplication();
                },
            },
        ]);

        this.tray.setContextMenu(contextMenu);
    }

    /**
     * Show main window
     */
    showWindow() {
        if (this.mainWindow) {
            if (this.mainWindow.isMinimized()) {
                this.mainWindow.restore();
            }
            this.mainWindow.show();
            this.mainWindow.focus();

            // Update context menu
            this.createContextMenu();

            log.info('Main window shown from tray');
        }
    }

    /**
     * Hide main window
     */
    hideWindow() {
        if (this.mainWindow) {
            this.mainWindow.hide();

            // Update context menu
            this.createContextMenu();

            log.info('Main window hidden to tray');
        }
    }

    /**
     * Check if main window is visible
     * @returns {boolean} - True if window is visible
     */
    isWindowVisible() {
        return (
            this.mainWindow &&
            this.mainWindow.isVisible() &&
            !this.mainWindow.isMinimized()
        );
    }

    /**
     * Reload plugins
     */
    reloadPlugins() {
        if (this.mainWindow) {
            // Send IPC message to renderer to reload plugins
            this.mainWindow.webContents.send('tray:reload-plugins');
            log.info('Plugin reload requested from tray');
        }
    }

    /**
     * Start server
     */
    startServer() {
        if (this.mainWindow) {
            // Send IPC message to renderer to start server
            this.mainWindow.webContents.send('tray:start-server');
            log.info('Server start requested from tray');
        }
    }

    /**
     * Stop server
     */
    stopServer() {
        if (this.mainWindow) {
            // Send IPC message to renderer to stop server
            this.mainWindow.webContents.send('tray:stop-server');
            log.info('Server stop requested from tray');
        }
    }

    /**
     * Show connection status
     */
    showConnectionStatus() {
        if (this.mainWindow) {
            // Show window and navigate to connections view
            this.showWindow();
            this.mainWindow.webContents.send('tray:show-connections');
            log.info('Connection status view requested from tray');
        }
    }

    /**
     * Show about dialog
     */
    showAbout() {
        if (this.mainWindow) {
            // Send IPC message to renderer to show about dialog
            this.mainWindow.webContents.send('tray:show-about');
            log.info('About dialog requested from tray');
        }
    }

    /**
     * Exit application
     */
    exitApplication() {
        log.info('Application exit requested from tray');
        this.isQuitting = true;

        // Destroy tray
        if (this.tray) {
            this.tray.destroy();
            this.tray = null;
        }

        // Quit application
        app.quit();
    }

    /**
     * Handle window close event
     * @param {Event} event - Close event
     */
    handleWindowClose(event) {
        if (!this.isQuitting) {
            // Prevent window from closing, hide to tray instead
            event.preventDefault();
            this.hideWindow();

            // Show notification on first hide
            if (this.tray && !this.hasShownHideNotification) {
                this.tray.displayBalloon({
                    title: 'L2 Communication Server',
                    content:
                        '应用程序已最小化到系统托盘。双击托盘图标可重新显示窗口。',
                    icon: nativeImage.createFromPath(this.getIconPath()),
                });
                this.hasShownHideNotification = true;
            }
        }
    }

    /**
     * Update tray menu based on application state
     * @param {Object} state - Application state
     */
    updateTrayMenu(state = {}) {
        if (!this.tray) return;

        const contextMenu = Menu.buildFromTemplate([
            {
                label: '显示窗口',
                click: () => {
                    this.showWindow();
                },
                enabled: !this.isWindowVisible(),
            },
            {
                label: '隐藏窗口',
                click: () => {
                    this.hideWindow();
                },
                enabled: this.isWindowVisible(),
            },
            {
                type: 'separator',
            },
            {
                label: '重新加载插件',
                click: () => {
                    this.reloadPlugins();
                },
            },
            {
                label: '服务器状态',
                submenu: [
                    {
                        label: state.serverRunning
                            ? '停止服务器'
                            : '启动服务器',
                        click: () => {
                            if (state.serverRunning) {
                                this.stopServer();
                            } else {
                                this.startServer();
                            }
                        },
                    },
                    {
                        type: 'separator',
                    },
                    {
                        label: `连接数: ${state.connectionCount || 0}`,
                        enabled: false,
                    },
                    {
                        label: '查看连接状态',
                        click: () => {
                            this.showConnectionStatus();
                        },
                    },
                ],
            },
            {
                type: 'separator',
            },
            {
                label: '关于',
                click: () => {
                    this.showAbout();
                },
            },
            {
                label: '退出',
                click: () => {
                    this.exitApplication();
                },
            },
        ]);

        this.tray.setContextMenu(contextMenu);
    }

    /**
     * Cleanup tray resources
     */
    destroy() {
        if (this.tray) {
            this.tray.destroy();
            this.tray = null;
        }
    }
}

export { TrayService };
