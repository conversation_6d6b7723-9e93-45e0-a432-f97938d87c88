<template>
    <n-drawer
        v-model:show="isVisible"
        :width="480"
        placement="right"
        class="my-36px"
    >
        <n-drawer-content>
            <template #header>
                <div class="flex justify-between items-center w-full">
                    <span class="mode-indicator">{{ modeText }}</span>
                    <n-button @click="closeDrawer"> 关闭 </n-button>
                </div>
            </template>
            <n-form
                ref="formRef"
                :rules="rules"
                :model="pdi"
                :show-require-mark="false"
                label-placement="left"
                label-width="auto"
                size="medium"
            >
                <n-form-item label="轧制计划号" path="prg_no">
                    <n-input v-model:value="pdi.prg_no" data-testid="prg_no" />
                </n-form-item>

                <n-form-item label="轧制顺序号" path="seq_no">
                    <n-input-number
                        v-model:value="pdi.seq_no"
                        placeholder="1"
                        data-testid="seq_no"
                    >
                    </n-input-number>
                </n-form-item>

                <n-form-item label="板坯号" path="mat_no">
                    <n-input
                        v-model:value="pdi.mat_no"
                        placeholder=""
                        data-testid="mat_no"
                    >
                    </n-input>
                </n-form-item>

                <n-form-item label="钢种" path="steel_grade">
                    <n-input
                        v-model:value="pdi.steel_grade"
                        placeholder="sa"
                        data-testid="steel_grade"
                    >
                    </n-input>
                </n-form-item>

                <n-form-item label="板坯宽度 (mm)" path="slab_width">
                    <n-input-number
                        v-model:value="pdi.slab_width"
                        placeholder="1"
                        :show-button="false"
                        data-testid="slab_width"
                    >
                    </n-input-number>
                </n-form-item>
                <n-form-item label="板坯厚度 (mm)" path="slab_thick">
                    <n-input-number
                        v-model:value="pdi.slab_thick"
                        placeholder="1"
                        :show-button="false"
                        data-testid="slab_thick"
                    >
                    </n-input-number>
                </n-form-item>
                <n-form-item label="板坯长度 (mm)" path="slab_length">
                    <n-input-number
                        v-model:value="pdi.slab_length"
                        placeholder="1"
                        :show-button="false"
                        data-testid="slab_length"
                    >
                    </n-input-number>
                </n-form-item>
                <n-form-item label="板坯重量 (t)" path="slab_weight">
                    <n-input-number
                        v-model:value="pdi.slab_weight"
                        placeholder="1"
                        :show-button="false"
                        data-testid="slab_weight"
                    >
                    </n-input-number>
                </n-form-item>
            </n-form>
            <div class="footer">
                <n-button secondary type="primary" @click="handleSave">
                    保存
                </n-button>
            </div>
        </n-drawer-content>
    </n-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { NDrawer, NDrawerContent, NButton, FormInst } from 'naive-ui'
import { Pdi, dbPdi } from '@/models'

// 接收父组件传递过来的值
const props = defineProps({
    mode: {
        type: String,
        required: true,
        validator: (value: string) => ['add', 'edit', 'hidden'].includes(value),
    },
    editingId: Number,
})

const emits = defineEmits(['handle-add', 'handle-edit', 'update:mode'])

const isVisible = computed({
    get: () => props.mode !== 'hidden',
    set: (value) => emits('update:mode', value ? props.mode : 'hidden'),
})

const modeText = computed(() => {
    if (props.mode === 'add') return '新增数据'
    if (props.mode === 'edit') return '编辑数据'
    return ''
})

const closeDrawer = () => {
    emits('update:mode', 'hidden')
}

const formRef = ref<FormInst | null>(null)
const pdi = ref(<Pdi>{})

watch(
    () => [props.mode, props.editingId],
    async ([newMode, newEditingId]) => {
        if (newMode === 'edit' && newEditingId && Number(newEditingId) > 0) {
            try {
                pdi.value = (await dbPdi.fetchById(Number(newEditingId))) as Pdi
            } catch (error) {
                window.electron.ipcRenderer.invoke(
                    'add-log',
                    'error',
                    'Failed to fetch PDI data:',
                    error,
                )
            }
        } else if (newMode === 'add') {
            // Keep existing data but clear the ID for new record
            pdi.value = { ...pdi.value, id: null }
        }
    },
    { immediate: true },
)

// form rules
const rules = {
    prg_no: {
        type: 'string',
        required: true,
        trigger: ['blur', 'change'],
        message: '请输入轧制计划号',
    },
    seq_no: {
        type: 'number',
        required: true,
        trigger: ['blur', 'change'],
        message: '请输入轧制顺序号',
    },
    mat_no: {
        type: 'string',
        required: true,
        trigger: ['blur', 'change'],
        message: '请输入板坯号',
    },
}

const handleSave = async (_e: MouseEvent) => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'verbose',
        `${props.mode === 'add' ? 'Add' : 'Update'} PDI`,
    )

    if (props.mode === 'add') {
        emits('handle-add', pdi.value)
    } else if (props.mode === 'edit') {
        emits('handle-edit', pdi.value)
    }
}
</script>

<style scoped>
:deep(.n-drawer-header__main) {
    display: flex;
    justify-content: flex-end;
    width: 100%;
}
</style>
