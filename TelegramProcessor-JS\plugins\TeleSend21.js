const { SendPluginBase, exportPlugin, S7DataTypes } = require('../plugin-base');

/**
 * TeleSend21 - System status telegram sender
 * Composes and sends telegram 21 containing system status information
 */
class TeleSend21 extends SendPluginBase {
    constructor() {
        super();
        this.telegramNo = 21;
        this.pluginName = 'TeleSend21';
        this.version = '1.0.0';
        this.description =
            'Send telegram 21 - System status and operational data';
        this.author = 'L2CommServer Team';

        // Set sender and receiver IDs
        this.sendId = 'L2';
        this.recId = 'PL';

        // Plugin-specific properties
        this.systemStatus = 1; // 1 = OK, 0 = Error, 2 = Warning
        this.temperature = 25.0; // System temperature in Celsius
        this.pressure = 1013.25; // System pressure in hPa
        this.operationMode = 1; // 1 = Auto, 2 = Manual, 0 = Stopped
        this.alarmStatus = 0; // Bit field for various alarms
        this.cycleCount = 0; // Number of operation cycles
        this.lastMaintenanceHours = 0; // Hours since last maintenance

        // Update timestamp
        this.lastUpdateTime = new Date();
    }

    /**
     * Compose message body
     * Message format (32 bytes total):
     * - System Status (4 bytes, DInt)
     * - Temperature (4 bytes, Real)
     * - Pressure (4 bytes, Real)
     * - Operation Mode (4 bytes, DInt)
     * - Alarm Status (4 bytes, DInt)
     * - Cycle Count (4 bytes, DInt)
     * - Maintenance Hours (4 bytes, DInt)
     * - Reserved (4 bytes)
     *
     * @returns {Buffer} - Message body
     */
    composeBody() {
        try {
            const body = Buffer.alloc(32);
            let offset = 0;

            // System Status (4 bytes)
            S7DataTypes.dIntToBuffer(this.systemStatus).copy(body, offset);
            offset += 4;

            // Temperature (4 bytes)
            S7DataTypes.realToBuffer(this.temperature).copy(body, offset);
            offset += 4;

            // Pressure (4 bytes)
            S7DataTypes.realToBuffer(this.pressure).copy(body, offset);
            offset += 4;

            // Operation Mode (4 bytes)
            S7DataTypes.dIntToBuffer(this.operationMode).copy(body, offset);
            offset += 4;

            // Alarm Status (4 bytes)
            S7DataTypes.dIntToBuffer(this.alarmStatus).copy(body, offset);
            offset += 4;

            // Cycle Count (4 bytes)
            S7DataTypes.dIntToBuffer(this.cycleCount).copy(body, offset);
            offset += 4;

            // Maintenance Hours (4 bytes)
            S7DataTypes.dIntToBuffer(this.lastMaintenanceHours).copy(
                body,
                offset
            );
            offset += 4;

            // Reserved (4 bytes) - filled with zeros
            body.fill(0, offset, offset + 4);

            return body;
        } catch (error) {
            console.error(
                `[${this.pluginName}] Error composing message body:`,
                error
            );
            throw error;
        }
    }

    /**
     * Compose complete telegram (header + body)
     * @returns {Buffer} - Complete telegram
     */
    compose() {
        try {
            const body = this.composeBody();
            const telegram = this.composeWithHeader(body);

            console.log(
                `[${this.pluginName}] Telegram composed successfully (${telegram.length} bytes)`
            );

            return telegram;
        } catch (error) {
            console.error(
                `[${this.pluginName}] Error composing telegram:`,
                error
            );
            throw error;
        }
    }

    /**
     * Debug output for the plugin
     */
    debugOutput() {
        console.log(`[${this.pluginName}] Debug Info:`);
        console.log(
            `  System Status: ${this.getStatusText(this.systemStatus)}`
        );
        console.log(`  Temperature: ${this.temperature.toFixed(1)}°C`);
        console.log(`  Pressure: ${this.pressure.toFixed(2)} hPa`);
        console.log(
            `  Operation Mode: ${this.getModeText(this.operationMode)}`
        );
        console.log(
            `  Alarm Status: 0x${this.alarmStatus
                .toString(16)
                .padStart(8, '0')}`
        );
        console.log(`  Cycle Count: ${this.cycleCount}`);
        console.log(`  Maintenance Hours: ${this.lastMaintenanceHours}`);
        console.log(`  Last Update: ${this.lastUpdateTime.toISOString()}`);
        console.log(`  Telegram Counter: ${this.telCounter}`);
    }

    /**
     * Get human-readable status text
     * @param {number} status - Status code
     * @returns {string} - Status text
     */
    getStatusText(status) {
        switch (status) {
            case 0:
                return 'ERROR';
            case 1:
                return 'OK';
            case 2:
                return 'WARNING';
            default:
                return 'UNKNOWN';
        }
    }

    /**
     * Get human-readable mode text
     * @param {number} mode - Mode code
     * @returns {string} - Mode text
     */
    getModeText(mode) {
        switch (mode) {
            case 0:
                return 'STOPPED';
            case 1:
                return 'AUTO';
            case 2:
                return 'MANUAL';
            default:
                return 'UNKNOWN';
        }
    }

    // Setter methods for updating values

    /**
     * Set system status
     * @param {number} status - Status code (0=Error, 1=OK, 2=Warning)
     */
    setSystemStatus(status) {
        this.systemStatus = status;
        this.lastUpdateTime = new Date();
        console.log(
            `[${this.pluginName}] System status set to ${this.getStatusText(
                status
            )}`
        );
    }

    /**
     * Set system temperature
     * @param {number} temperature - Temperature in Celsius
     */
    setTemperature(temperature) {
        this.temperature = temperature;
        this.lastUpdateTime = new Date();
    }

    /**
     * Set system pressure
     * @param {number} pressure - Pressure in hPa
     */
    setPressure(pressure) {
        this.pressure = pressure;
        this.lastUpdateTime = new Date();
    }

    /**
     * Set operation mode
     * @param {number} mode - Mode code (0=Stopped, 1=Auto, 2=Manual)
     */
    setOperationMode(mode) {
        this.operationMode = mode;
        this.lastUpdateTime = new Date();
        console.log(
            `[${this.pluginName}] Operation mode set to ${this.getModeText(
                mode
            )}`
        );
    }

    /**
     * Set alarm status
     * @param {number} alarmBits - Alarm status bit field
     */
    setAlarmStatus(alarmBits) {
        this.alarmStatus = alarmBits;
        this.lastUpdateTime = new Date();
    }

    /**
     * Set or clear specific alarm bit
     * @param {number} bitPosition - Bit position (0-31)
     * @param {boolean} value - True to set, false to clear
     */
    setAlarmBit(bitPosition, value) {
        if (bitPosition >= 0 && bitPosition < 32) {
            if (value) {
                this.alarmStatus |= 1 << bitPosition;
            } else {
                this.alarmStatus &= ~(1 << bitPosition);
            }
            this.lastUpdateTime = new Date();
        }
    }

    /**
     * Increment cycle count
     */
    incrementCycleCount() {
        this.cycleCount++;
        this.lastUpdateTime = new Date();
    }

    /**
     * Set maintenance hours
     * @param {number} hours - Hours since last maintenance
     */
    setMaintenanceHours(hours) {
        this.lastMaintenanceHours = hours;
        this.lastUpdateTime = new Date();
    }

    /**
     * Get current status as object
     * @returns {Object} - Current status
     */
    getCurrentStatus() {
        return {
            systemStatus: this.systemStatus,
            statusText: this.getStatusText(this.systemStatus),
            temperature: this.temperature,
            pressure: this.pressure,
            operationMode: this.operationMode,
            modeText: this.getModeText(this.operationMode),
            alarmStatus: this.alarmStatus,
            cycleCount: this.cycleCount,
            maintenanceHours: this.lastMaintenanceHours,
            lastUpdate: this.lastUpdateTime,
        };
    }

    /**
     * Update all values from external source
     * @param {Object} statusData - Status data object
     */
    updateFromData(statusData) {
        if (statusData.systemStatus !== undefined)
            this.setSystemStatus(statusData.systemStatus);
        if (statusData.temperature !== undefined)
            this.setTemperature(statusData.temperature);
        if (statusData.pressure !== undefined)
            this.setPressure(statusData.pressure);
        if (statusData.operationMode !== undefined)
            this.setOperationMode(statusData.operationMode);
        if (statusData.alarmStatus !== undefined)
            this.setAlarmStatus(statusData.alarmStatus);
        if (statusData.cycleCount !== undefined)
            this.cycleCount = statusData.cycleCount;
        if (statusData.maintenanceHours !== undefined)
            this.setMaintenanceHours(statusData.maintenanceHours);
    }
}

// Export the plugin
module.exports = exportPlugin(TeleSend21);
