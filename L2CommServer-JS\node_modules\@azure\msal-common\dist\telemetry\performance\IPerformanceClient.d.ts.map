{"version": 3, "file": "IPerformanceClient.d.ts", "sourceRoot": "", "sources": ["../../../src/telemetry/performance/IPerformanceClient.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AAEvE,MAAM,MAAM,2BAA2B,GAAG,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,IAAI,CAAC;AAE/E,MAAM,MAAM,0BAA0B,GAAG;IACrC,GAAG,EAAE,CACD,KAAK,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC,EACjC,KAAK,CAAC,EAAE,OAAO,KACd,gBAAgB,GAAG,IAAI,CAAC;IAC7B,OAAO,EAAE,MAAM,IAAI,CAAC;IACpB,GAAG,EAAE,CAAC,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE,GAAG,SAAS,CAAA;KAAE,KAAK,IAAI,CAAC;IACzD,SAAS,EAAE,CAAC,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAA;KAAE,KAAK,IAAI,CAAC;IACnE,KAAK,EAAE,gBAAgB,CAAC;IACxB;;OAEG;IACH,WAAW,EAAE,uBAAuB,CAAC;CACxC,CAAC;AAEF,MAAM,WAAW,kBAAkB;IAC/B,gBAAgB,CACZ,WAAW,EAAE,MAAM,EACnB,aAAa,CAAC,EAAE,MAAM,GACvB,0BAA0B,CAAC;IAC9B,cAAc,CAAC,KAAK,EAAE,gBAAgB,GAAG,gBAAgB,GAAG,IAAI,CAAC;IACjE,mBAAmB,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACjD,SAAS,CACL,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE,GAAG,SAAS,CAAA;KAAE,EACzC,aAAa,EAAE,MAAM,GACtB,IAAI,CAAC;IACR,eAAe,CACX,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAA;KAAE,EAC7C,aAAa,EAAE,MAAM,GACtB,IAAI,CAAC;IACR,yBAAyB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC;IACvD,sBAAsB,CAAC,QAAQ,EAAE,2BAA2B,GAAG,MAAM,CAAC;IACtE,UAAU,CAAC,MAAM,EAAE,gBAAgB,EAAE,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpE;;OAEG;IACH,2BAA2B,CACvB,WAAW,EAAE,MAAM,EACnB,aAAa,EAAE,MAAM,GACtB,uBAAuB,CAAC;IAC3B,UAAU,IAAI,MAAM,CAAC;IACrB,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,MAAM,CAAC;IACvE,mBAAmB,CACf,SAAS,EAAE,MAAM,EACjB,aAAa,CAAC,EAAE,MAAM,EACtB,SAAS,CAAC,EAAE,MAAM,EAClB,iBAAiB,CAAC,EAAE,OAAO,GAC5B,IAAI,CAAC;IACR,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACpE;AAED;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG;IAC3B;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;CAC/B,CAAC"}