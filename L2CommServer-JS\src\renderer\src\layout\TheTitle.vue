<template>
    <div v-if="!isLinux" id="titlebar" @dblclick="toggleFullScreen">
        <div class="titlebar">
            <div class="titlebar-appname">
                <img
                    src="@/images/logo-256.png"
                    alt="App Logo"
                    class="app-icon"
                />
                {{ appName }}
            </div>
            <div class="titlebar-title">
                {{ windowTitle }}
            </div>
            <div
                v-if="isDevelopment"
                class="titlebar-icons"
                @click="openDevTools"
            >
                <div class="i-mdi:code text-18" />
            </div>
            <div v-if="isWindows" class="w-180" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { useApplicationStore } from '@/stores/application'
import eventBus from '@common/libs/eventBus'

const process = window.electron.process
const isDevelopment = ref(process.env.NODE_ENV === 'development')
const isWindows = process.platform === 'win32'
const isLinux = process.platform === 'linux'

const { appName, appVersion } = useApplicationStore()
const windowTitle = ref(`${appName} ${appVersion}`)

const openDevTools = () => {
    window.electron.ipcRenderer.invoke('open-dev-tools')
}

const toggleFullScreen = () => {
    //window.electron.ipcRenderer.send('toggle-full-screen')
}

// 更新标题
eventBus.on('update-window-title', (title: unknown) => {
    if (typeof title === 'string') {
        windowTitle.value = title
    }
})

/*
const toggleFullScreen = () => {
    if (isMaximized.value) w.value.unmaximize()
    else w.value.maximize()
}


*/
const onResize = () => {
    // emit resize event for workspaces, etc
    eventBus.emit('main-window-resize')
}

onMounted(() => {
    window.addEventListener('resize', onResize)

    window.electron.ipcRenderer.invoke(
        'add-log',
        'verbose',
        'Titlebar mounted.',
    )
})

onUnmounted(() => {
    window.removeEventListener('resize', onResize)
})
</script>

<style lang="scss">
#titlebar {
    box-sizing: border-box;
    -webkit-app-region: drag;
    user-select: none;
    background: #f3f4f6;
    height: 36px;
    border-bottom: solid 1px #fafafa;
    display: flex;

    .titlebar {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 550;
        padding-top: 2px;

        .titlebar-appname {
            width: 250px;
            margin-left: 8px;
            display: flex;
            align-items: center;

            .app-icon {
                width: 24px;
                height: 24px;
                margin-right: 8px;
                object-fit: contain;
            }
        }

        .titlebar-title {
            width: 90%;
            text-align: center;
        }

        .titlebar-icons {
            padding: 8px 12px 2px;
            -webkit-app-region: no-drag;

            &:hover {
                background-color: #ddd;
            }
        }
    }
}
</style>
