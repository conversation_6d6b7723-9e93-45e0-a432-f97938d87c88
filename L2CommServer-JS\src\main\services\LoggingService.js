import EventEmitter from 'events';
import log from 'electron-log';
import path from 'path';
import { app } from 'electron';

/**
 * Logging Service - Centralized logging that forwards to UI
 * Based on FS-PSTF-L2 implementation
 */
class LoggingService extends EventEmitter {
    constructor() {
        super();
        this.mainWindow = null;
        this.logLevel = 'info';
        this.logBuffer = [];
        this.maxBufferSize = 1000;

        // Initialize electron-log
        this.initializeElectronLog();
    }

    /**
     * Initialize electron-log configuration
     */
    initializeElectronLog() {
        log.initialize();

        // Configure file transport
        log.transports.file.resolvePathFn = () => {
            return path.join(app.getAppPath(), 'l2commserver.log');
        };

        // Configure transport levels
        log.transports.ipc.level = this.logLevel;
        log.transports.file.level = 'debug';
        log.transports.console.level = this.logLevel;

        // Override console methods to capture all logs
        this.overrideConsoleMethods();
    }

    /**
     * Set the main window for IPC communication
     * @param {BrowserWindow} mainWindow - Main window instance
     */
    setMainWindow(mainWindow) {
        this.mainWindow = mainWindow;

        // Send buffered logs to UI
        if (this.logBuffer.length > 0) {
            this.logBuffer.forEach((entry) => {
                this.sendLogToUI(entry.level, entry.args);
            });
            this.logBuffer = [];
        }
    }

    /**
     * Override console methods to capture all console output
     */
    overrideConsoleMethods() {
        const originalConsole = {
            log: console.log,
            info: console.info,
            warn: console.warn,
            error: console.error,
            debug: console.debug,
        };

        console.log = (...args) => {
            originalConsole.log(...args);
            this.log('info', ...args);
        };

        console.info = (...args) => {
            originalConsole.info(...args);
            this.log('info', ...args);
        };

        console.warn = (...args) => {
            originalConsole.warn(...args);
            this.log('warn', ...args);
        };

        console.error = (...args) => {
            originalConsole.error(...args);
            this.log('error', ...args);
        };

        console.debug = (...args) => {
            originalConsole.debug(...args);
            this.log('debug', ...args);
        };
    }

    /**
     * Log a message
     * @param {string} level - Log level (error, warn, info, verbose, debug)
     * @param {...any} args - Log arguments
     */
    log(level, ...args) {
        // Validate log level
        const validLevels = ['error', 'warn', 'info', 'verbose', 'debug'];
        if (!validLevels.includes(level)) {
            level = 'info';
        }

        // Log to electron-log (which handles file and console)
        if (log[level] && typeof log[level] === 'function') {
            log[level](...args);
        } else {
            log.info(...args);
        }

        // Send to UI or buffer if UI not ready
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.sendLogToUI(level, args);
        } else {
            this.bufferLog(level, args);
        }

        // Emit event for other services
        this.emit('log', { level, args, timestamp: new Date() });
    }

    /**
     * Send log to UI via IPC
     * @param {string} level - Log level
     * @param {Array} args - Log arguments
     */
    sendLogToUI(level, args) {
        try {
            if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                // Send via electron-log IPC transport (compatible with FS-PSTF-L2)
                this.mainWindow.webContents.send('__ELECTRON_LOG_IPC__', {
                    level: level,
                    data: args,
                });
            }
        } catch (error) {
            // Fallback to console if IPC fails
            console.error('Failed to send log to UI:', error);
        }
    }

    /**
     * Buffer log entry when UI is not ready
     * @param {string} level - Log level
     * @param {Array} args - Log arguments
     */
    bufferLog(level, args) {
        this.logBuffer.push({ level, args, timestamp: new Date() });

        // Limit buffer size
        if (this.logBuffer.length > this.maxBufferSize) {
            this.logBuffer.shift();
        }
    }

    /**
     * Set log level
     * @param {string} level - New log level
     */
    setLogLevel(level) {
        const validLevels = ['error', 'warn', 'info', 'verbose', 'debug'];
        if (validLevels.includes(level)) {
            this.logLevel = level;
            log.transports.console.level = level;
            log.transports.ipc.level = level;
            this.log('info', `Log level changed to: ${level}`);
        } else {
            this.log('warn', `Invalid log level: ${level}`);
        }
    }

    /**
     * Get current log level
     * @returns {string} Current log level
     */
    getLogLevel() {
        return this.logLevel;
    }

    /**
     * Clear log buffer
     */
    clearBuffer() {
        this.logBuffer = [];
        this.log('info', 'Log buffer cleared');
    }

    /**
     * Get buffered logs
     * @returns {Array} Buffered log entries
     */
    getBufferedLogs() {
        return [...this.logBuffer];
    }

    // Convenience methods for different log levels

    /**
     * Log error message
     * @param {...any} args - Log arguments
     */
    error(...args) {
        this.log('error', ...args);
    }

    /**
     * Log warning message
     * @param {...any} args - Log arguments
     */
    warn(...args) {
        this.log('warn', ...args);
    }

    /**
     * Log info message
     * @param {...any} args - Log arguments
     */
    info(...args) {
        this.log('info', ...args);
    }

    /**
     * Log verbose message
     * @param {...any} args - Log arguments
     */
    verbose(...args) {
        this.log('verbose', ...args);
    }

    /**
     * Log debug message
     * @param {...any} args - Log arguments
     */
    debug(...args) {
        this.log('debug', ...args);
    }

    /**
     * Create a logger instance for a specific component
     * @param {string} component - Component name
     * @returns {Object} Logger instance
     */
    createLogger(component) {
        return {
            error: (...args) => this.error(`[${component}]`, ...args),
            warn: (...args) => this.warn(`[${component}]`, ...args),
            info: (...args) => this.info(`[${component}]`, ...args),
            verbose: (...args) => this.verbose(`[${component}]`, ...args),
            debug: (...args) => this.debug(`[${component}]`, ...args),
            log: (level, ...args) => this.log(level, `[${component}]`, ...args),
        };
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        this.removeAllListeners();
        this.logBuffer = [];
        this.mainWindow = null;
    }
}

// Create singleton instance
const loggingService = new LoggingService();

export { LoggingService, loggingService };
