<template>
    <div v-show="isSelected" class="panel">
        <n-space vertical>
            <n-space justify="space-between">
                <div class="w-480">
                    <n-form
                        :show-require-mark="false"
                        :show-feedback="false"
                        label-width="100px"
                        label-placement="left"
                        size="small"
                    >
                        <n-space>
                            <n-form-item
                                label="查找日期"
                                path="date"
                                class="w-240"
                            >
                                <n-date-picker
                                    v-model:value="inputFilter.date"
                                    @update:value="
                                        (value) => updateFilter('date', value)
                                    "
                                    type="date"
                                    placeholder="日期"
                                    data-testid="input-filter-date"
                                    :clearable="true"
                                />
                            </n-form-item>
                        </n-space>
                    </n-form>
                </div>
                <AutoRefresh
                    :default-refresh="true"
                    :default-interval="30"
                    @refresh="refreshData"
                />
            </n-space>
            <n-data-table
                remote
                :row-key="(row) => row.id"
                :columns="columns"
                :data="telegrams"
                :loading="loading"
                :pagination="paginationRef"
                :on-update:page="handlePageChange"
                :single-line="false"
                titleAlign="center"
                striped
                size="small"
            />
        </n-space>
        <!-- 查看组件 -->
        <Tele121Viewer :viewing-id="viewingRowId" @finish-view="finishView" />
    </div>
</template>

<script setup lang="ts">
import { ref, h, onMounted } from 'vue'
import { DataTableColumns, NSpace } from 'naive-ui'
import { dbTelegram121, Telegram121, Tele121FilterOptions } from '@/models'
import { useDataTable, useFilter } from '@/composables'
// UI components
import { AutoRefresh, RowButtons, Tele121Viewer } from './components'

// 接收父组件传递过来的值
defineProps({
    tabUid: String,
    isSelected: Boolean,
})

// viewer
const viewingRowId = ref(0)

const fetchData = async (start: number, pageSize: number) => {
    return await dbTelegram121.fetch(start, pageSize, 'desc', inputFilter.value)
}

const fetchCount = async () => {
    return await dbTelegram121.count(inputFilter.value)
}

const mapTelegram = (tele: Telegram121) => {
    return {
        ...tele,
        isEdit: false,
        isDeletable: false,
    }
}

const {
    telegrams,
    loading,
    pagination: paginationRef,
    handlePageChange,
    refreshData,
    renderTableTitle,
} = useDataTable<Telegram121>(fetchData, fetchCount, mapTelegram, 15)

// setup filter
const { filter: inputFilter, updateFilter } =
    useFilter<Tele121FilterOptions>(refreshData)

const createColumns = (): DataTableColumns<Telegram121> => {
    const columns = [
        {
            title: renderTableTitle('#'),
            key: 'id',
            width: 60,
            className: 'center',
            render: (rowData: Telegram121, _rowIndex: number) => {
                return rowData.id
            },
        },
        {
            title: renderTableTitle('Z1温度'),
            key: 'z1_temp_av',
            width: 60,
            className: 'center',
            render: (rowData: Telegram121, _rowIndex: number) => {
                return rowData.z1_temp_av.toFixed(2)
            },
        },
        {
            title: renderTableTitle('Z2温度'),
            key: 'z2_temp_av',
            width: 60,
            className: 'center',
            render: (rowData: Telegram121, _rowIndex: number) => {
                return rowData.z2_temp_av.toFixed(2)
            },
        },
        {
            title: renderTableTitle('Z3温度'),
            key: 'z3_temp_av',
            width: 60,
            className: 'center',
            render: (rowData: Telegram121, _rowIndex: number) => {
                return rowData.z3_temp_av.toFixed(2)
            },
        },
        {
            title: renderTableTitle('Z4温度'),
            key: 'z4_temp_av',
            width: 60,
            className: 'center',
            render: (rowData: Telegram121, _rowIndex: number) => {
                return rowData.z4_temp_av.toFixed(2)
            },
        },
        {
            title: renderTableTitle('Z5温度'),
            key: 'z5_temp_av',
            width: 60,
            className: 'center',
            render: (rowData: Telegram121, _rowIndex: number) => {
                return rowData.z5_temp_av.toFixed(2)
            },
        },
        {
            title: renderTableTitle('Z6温度'),
            key: 'z6_temp_av',
            width: 60,
            className: 'center',
            render: (rowData: Telegram121, _rowIndex: number) => {
                return rowData.z6_temp_av.toFixed(2)
            },
        },
        {
            title: renderTableTitle('报文日期'),
            key: 'record_time',
            width: 150,
            className: 'center',
            render: (rowData: Telegram121, _rowIndex: number) => {
                return rowData.record_time.toLocaleString('zh-CN', {
                    timeZone: 'UTC',
                })
            },
        },
    ]
    // define buttons
    // define buttons column
    const buttonsCol = {
        key: 'options',
        width: 60,
        title: renderTableTitle('选项'),
        className: 'center',
        render: (rowData: Telegram121, _rowIndex: number) => {
            // return group of buttons
            return h(RowButtons, {
                isViewable: true,
                isDeletable: false,
                // view button click
                onViewRow() {
                    viewingRowId.value = rowData.id as number
                },
            })
        },
    }

    return [...columns, buttonsCol] as unknown as DataTableColumns<Telegram121>
}

// create columns
const columns = ref<DataTableColumns<Telegram121>>()
columns.value = createColumns()

// finish view
const finishView = () => {
    // clear viewer info
    viewingRowId.value = 0
}

onMounted(async () => {
    refreshData()
})
</script>

<style lang="scss" scoped>
.panel {
    padding: 0;
    margin: 15px;
    height: calc(100vh - 70px - var(--excluding-size));
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;

    :deep(.n-data-table-td) {
        padding: 2px 6px;
    }

    .editor {
        margin-top: auto;
    }

    :deep(.editing td) {
        background-color: rgba(var(--primary-color), 0.1) !important;
        font-weight: bold;
    }
    :deep(.deleting td) {
        background-color: rgba(var(--warning-color), 0.1) !important;
        font-weight: bold;
    }

    :deep(.center) {
        text-align: center;
    }
}
</style>
