import { Page } from 'playwright'
import { ElectronApplication } from 'playwright-core'
import { test, expect } from '@playwright/test'
import helpers from './helpers'
import { mainMenu } from './constants'

let appWindow: Page
let electronApp: ElectronApplication

test.beforeAll(async () => {
    const app = await helpers.launchApp()
    appWindow = app.appWindow
    electronApp = app.electronApp
})

test('launch app', async () => {
    const isPackaged = await electronApp.evaluate(({ app }) => {
        return app.isPackaged
    })
    expect(isPackaged, 'expect is unpacked').toBe(false)
})

test('main window elements visibility', async () => {
    const visibleSelectors = ['#titlebar', '#sidebar', '#workspace', '#footer']
    for (const selector of visibleSelectors) {
        expect(
            await appWindow.locator(selector).isVisible(),
            `expect element ${selector} visible`,
        ).toBe(true)
    }
})

// default is info page
test('info page shown', async () => {
    await expect(
        appWindow.locator('.version-text'),
        'expect info page version string',
    ).toHaveText(mainMenu.Info.match)
})

test.afterAll(async () => {
    await electronApp.close()
})
