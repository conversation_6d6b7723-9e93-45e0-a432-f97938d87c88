<template>
    <div v-show="isSelected" class="panel">
        <n-space vertical>
            <n-space justify="space-between">
                <div style="width: 700px">
                    <n-form
                        :show-require-mark="false"
                        :show-feedback="false"
                        label-width="100px"
                        label-placement="left"
                        size="small"
                    >
                        <n-space>
                            <n-form-item
                                label="查找坯料号"
                                path="billet_semicode"
                                style="width: 220px"
                            >
                                <n-input-number
                                    v-model:value="inputFilter.billet_semicode"
                                    @update:value="
                                        (value) =>
                                            updateFilter(
                                                'billet_semicode',
                                                value,
                                            )
                                    "
                                    :show-button="false"
                                    placeholder="坯料号"
                                    :clearable="true"
                                    data-testid="input-filter-billet-semicode"
                                />
                            </n-form-item>
                            <n-form-item
                                label="查找轧制计划"
                                path="roll_no"
                                style="width: 220px"
                            >
                                <n-input
                                    v-model:value="inputFilter.roll_no"
                                    @update:value="
                                        (value) =>
                                            updateFilter(
                                                'roll_no',
                                                value.toUpperCase(),
                                            )
                                    "
                                    :show-button="false"
                                    placeholder="轧制计划号"
                                    :clearable="true"
                                    data-testid="input-filter-roll-id"
                                    @input="
                                        (value) =>
                                            (inputFilter.roll_no =
                                                value.toUpperCase())
                                    "
                                />
                            </n-form-item>
                            <n-form-item
                                label="查找日期"
                                path="date"
                                style="width: 220px"
                            >
                                <n-date-picker
                                    v-model:value="inputFilter.date"
                                    @update:value="
                                        (value) => updateFilter('date', value)
                                    "
                                    type="date"
                                    placeholder="日期"
                                    data-testid="input-filter-date"
                                    :clearable="true"
                                />
                            </n-form-item>
                        </n-space>
                    </n-form>
                </div>
                <AutoRefresh
                    :default-refresh="true"
                    :default-interval="30"
                    @refresh="refreshData"
                />
            </n-space>
            <n-data-table
                remote
                :row-key="(row) => row.id"
                :columns="columns"
                :data="telegrams"
                :loading="loading"
                :pagination="paginationRef"
                :on-update:page="handlePageChange"
                :single-line="false"
                titleAlign="center"
                striped
                size="small"
            />
        </n-space>
        <!-- 查看组件 -->
        <Tele111Viewer :viewing-id="viewingRowId" @finish-view="finishView" />
    </div>
</template>

<script setup lang="ts">
import { ref, h, onMounted } from 'vue'
import eventBus from '@common/libs/eventBus'
import { DataTableColumns, NSpace, useMessage } from 'naive-ui'
import { dbTelegram111, Telegram111, Tele111FilterOptions } from '@/models'
import { useDataTable, useFilter } from '@/composables'
// UI components
import { AutoRefresh, RowButtons, Tele111Viewer } from './components'

// 接收父组件传递过来的值
defineProps({
    tabUid: String,
    isSelected: Boolean,
})

const message = useMessage()

// viewer
const viewingRowId = ref(0)
// to be deleted row ID, 0 means not in deleting mode
const deletingRowId = ref(0)

const fetchData = async (start: number, pageSize: number) => {
    return await dbTelegram111.fetch(start, pageSize, 'desc', inputFilter.value)
}

const fetchCount = async () => {
    return await dbTelegram111.count(inputFilter.value)
}

const mapTelegram = (tele: Telegram111) => {
    return {
        ...tele,
        isEdit: false,
    }
}

const {
    telegrams, // mapped telegrams
    loading,
    pagination: paginationRef,
    handlePageChange,
    refreshData,
    renderTableTitle,
} = useDataTable<Telegram111>(fetchData, fetchCount, mapTelegram, 15)

// setup filter
const { filter: inputFilter, updateFilter } =
    useFilter<Tele111FilterOptions>(refreshData)

// delete row
const deleteRow = async (id: number) => {
    // find row by id
    const row = telegrams.value.find((row) => row.id === id)
    // log
    window.electron.ipcRenderer.invoke(
        'add-log',
        'info',
        'Delete from Telegram111, row id',
        row?.id,
    )

    // delete
    if (await dbTelegram111.del(<number>row?.id)) {
        message.success('删除成功')
        // clear delete id
        deletingRowId.value = 0
        // 刷新列表
        refreshData()
        eventBus.emit('updateDatalogExploreBar')
    } else {
        message.error('删除失败')
    }
}

const createColumns = (): DataTableColumns<Telegram111> => {
    const columns = [
        {
            title: renderTableTitle('#'),
            key: 'id',
            width: 60,
            className: 'center',
        },
        {
            title: renderTableTitle('坯料号'),
            key: 'billet_semicode',
            width: 120,
            className: 'center',
        },
        {
            title: renderTableTitle('轧制计划号'),
            key: 'roll_no',
            width: 120,
            className: 'center',
        },
        {
            title: renderTableTitle('炉批号'),
            key: 'batch_no',
            width: 120,
            className: 'center',
        },
        {
            title: renderTableTitle('总数'),
            key: 'batch_total',
            width: 60,
            className: 'center',
        },
        {
            title: renderTableTitle('支号'),
            key: 'billet_no',
            width: 60,
            className: 'center',
        },
        {
            title: renderTableTitle('长度'),
            key: 'billet_length',
            width: 60,
            className: 'center',
        },
        {
            title: renderTableTitle('宽度'),
            key: 'billet_width',
            width: 60,
            className: 'center',
        },
        {
            title: renderTableTitle('厚度'),
            key: 'billet_thickness',
            width: 70,
            className: 'center',
        },
        {
            title: renderTableTitle('报文日期'),
            key: 'record_time',
            width: 150,
            className: 'center',
            render: (rowData: Telegram111, _rowIndex: number) => {
                return rowData.record_time.toLocaleString('zh-CN', {
                    timeZone: 'UTC',
                })
            },
        },
    ]
    // define buttons
    // define buttons column
    const buttonsCol = {
        key: 'options',
        width: 80,
        title: renderTableTitle('选项'),
        className: 'center',
        render: (rowData: Telegram111, _rowIndex: number) => {
            // return group of buttons
            return h(RowButtons, {
                isViewable: true,
                isDeletable: true,
                // view button click
                onViewRow() {
                    viewingRowId.value = rowData.id as number
                },
                // delete button click but not confirmed
                onDeleteWarning() {
                    deletingRowId.value = rowData.id as number
                },
                // delete cancelled
                onDeleteCancel() {
                    deletingRowId.value = 0
                },
                // delete confirmed
                onDeleteRow() {
                    deleteRow(rowData.id as number)
                },
            })
        },
    }

    return [...columns, buttonsCol] as unknown as DataTableColumns<Telegram111>
}

// create columns
const columns = ref<DataTableColumns<Telegram111>>()
columns.value = createColumns()

// finish view
const finishView = () => {
    // clear viewer info
    viewingRowId.value = 0
}

onMounted(async () => {
    refreshData()
})
</script>

<style lang="scss" scoped>
.panel {
    padding: 0;
    margin: 15px;
    height: calc(100vh - 70px - 64px);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;

    :deep(.n-data-table-td) {
        padding: 2px 6px;
    }

    :deep(.deleting td) {
        background-color: rgba(var(--warning-color), 0.1) !important;
        font-weight: bold;
    }

    :deep(.center) {
        text-align: center;
    }
}
</style>
