<template>
    <div>
        <n-space style="align-items: center">
            <div>
                <n-switch
                    v-model:value="autoRefresh"
                    size="small"
                    data-testid="auto-refresh"
                />
                <span>&nbsp; 自动刷新</span>
            </div>
            <div>
                <n-select
                    size="small"
                    v-model:value="refreshInterval"
                    :options="intervalOptions"
                    style="width: 100px"
                    :disabled="!autoRefresh"
                    data-testid="refresh-interval"
                />
            </div>
            <div>
                <n-button
                    type="primary"
                    size="small"
                    @click="emits('refresh')"
                    :disabled="autoRefresh"
                >
                    刷新
                </n-button>
            </div>
        </n-space>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref, Ref, watch } from 'vue'

// define props
const props = defineProps({
    // autorefresh
    defaultRefresh: {
        type: Boolean,
        required: true,
        default: true,
    },
    // refresh interval
    defaultInterval: {
        type: Number,
        required: true,
        default: 0,
    },
})
// autorefresh
const autoRefresh = ref(true)
// autorefresh interval (seconds)
const refreshInterval = ref(30)
// refresh handle
const refreshHandle: Ref<NodeJS.Timeout | null> = ref(null)

// define emits
const emits = defineEmits(['refresh'])

// interval options
const intervalOptions = [
    { label: '5秒', value: 5 },
    { label: '15秒', value: 15 },
    { label: '30秒', value: 30 },
    { label: '60秒', value: 60 },
]

watch(
    () => refreshInterval.value,
    () => {
        if (refreshHandle.value) {
            clearInterval(refreshHandle.value)
        }
        refreshHandle.value = setInterval(() => {
            if (autoRefresh.value) {
                emits('refresh')
            }
        }, refreshInterval.value * 1000)
    },
)

onMounted(() => {
    // autorefresh default
    autoRefresh.value = props.defaultRefresh
    // autorefresh interval default (seconds)
    refreshInterval.value = props.defaultInterval

    // set auto refresh, default is 30s
    refreshHandle.value = setInterval(() => {
        if (autoRefresh.value) {
            emits('refresh')
        }
    }, refreshInterval.value * 1000)
})
</script>

<style lang="scss" scoped></style>
