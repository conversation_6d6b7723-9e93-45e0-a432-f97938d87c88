import { Knex, knex } from 'knex'
import { DbSettings } from '@common/interfaces/settings'
import log from 'electron-log/renderer'

export class DatabaseService {
    private knex: Knex | null = null
    private heartbeatInterval: NodeJS.Timeout | null = null
    private isConnected = false

    async initialize(dbSettings: DbSettings): Promise<void> {
        this.knex = knex({
            client: 'mssql',
            connection: {
                server: dbSettings.serverAddress || 'localhost',
                port: dbSettings.serverPort || 1433,
                database: dbSettings.database || 'L2',
                user: dbSettings.username || 'sa',
                password: dbSettings.password || '',
                connectionTimeout: 5000,
                options: {
                    encrypt: false,
                    trustServerCertificate: true,
                },
            },
        })

        // check db connection
        if (this.knex?.client?.pool) {
            log.verbose('Knex initialized.')

            // log all queries
            //this.knex.on('query', log.debug)

            // 添加心跳检查
            this.startHeartbeat()
        } else {
            log.error('Knex连接失败. 请检查数据库连接设置.')
        }
    }

    getKnex(): Knex {
        if (!this.knex) {
            throw new Error('数据库未初始化')
        }
        return this.knex
    }

    async close(): Promise<void> {
        if (this.knex) {
            await this.knex.destroy()
            this.knex = null
        }
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval)
            this.heartbeatInterval = null
        }
    }

    getConnectionStatus(): boolean {
        return this.isConnected
    }

    private startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval)
        }

        this.heartbeatInterval = setInterval(async () => {
            if (this.knex) {
                try {
                    await this.knex.raw('SELECT 1')
                    this.isConnected = true
                } catch (error) {
                    this.isConnected = false
                    log.error('数据库连接异常:', error)
                }
            }
        }, 5000) // 每5秒检查一次
    }
}

export const databaseService = new DatabaseService()
