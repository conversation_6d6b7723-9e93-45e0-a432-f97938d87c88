import { executeQuery } from '@/models/dbUtils'

export interface Telegram121 {
    id: number | null

    z1_temp_sp: number
    z2_temp_sp: number
    z3_temp_sp: number
    z4_temp_sp: number
    z5_temp_sp: number
    z6_temp_sp: number
    z1_temp_av: number
    z2_temp_av: number
    z3_temp_av: number
    z4_temp_av: number
    z5_temp_av: number
    z6_temp_av: number
    ng_consumption: number
    record_time: Date // 创建时间

    // extra keys
    isEdit: boolean
    isDeletable: boolean
}

export interface Tele121FilterOptions {
    date: number | null
}
// extra keys that does not belong to table
//const extraKeys: (keyof Telegram121)[] = [
//    <keyof Telegram121>'isEdit',
//    <keyof Telegram121>'isDeletable',
//`]

const TableName = 'Tele121_Fnc_Temp'

// log function stub
async function fetchById(id: number): Promise<Telegram121 | undefined> {
    if (id > 0) {
        try {
            const result = await executeQuery(
                'SELECT * FROM ' + TableName + ' WHERE id = ? ',
                [id],
            )
            return result[0] as Telegram121
        } catch (err) {
            window.electron.ipcRenderer.invoke('add-log', 'error', err)
            return undefined
        }
    } else {
        window.electron.ipcRenderer.invoke(
            'add-log',
            'warn',
            'Fetch Telegram121 by id, but id is 0.',
        )

        return undefined
    }
}

// fetch all Telegram121s, and their quotes
async function fetch(
    pageStart: number,
    pageSize: number,
    order: string,
    filter: Tele121FilterOptions,
): Promise<Telegram121[]> {
    try {
        let query = 'SELECT * FROM ' + TableName + ' WHERE 1=1'
        const params: (string | number)[] = []

        // 2023-01-01 = 1672502400000
        if (typeof filter?.date === 'number' && filter.date >= 1672502400000) {
            query += ` AND DATEDIFF(day, [record_time], '${new Date(
                filter.date,
            ).toLocaleDateString('zh-CN')}')=0`
        }

        query += ` ORDER BY id ${order} OFFSET ? ROWS FETCH NEXT ? ROWS ONLY`
        params.push(pageStart, pageSize)

        const telegrams = await executeQuery(query, params)
        return telegrams as Telegram121[]
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return []
    }
}

// get total record count
async function count(filter?: Tele121FilterOptions): Promise<number> {
    try {
        let query = 'SELECT COUNT(*) as count FROM ' + TableName + ' WHERE 1=1'
        const params: string[] = []

        // 2023-01-01 = 1672502400000
        if (typeof filter?.date === 'number' && filter.date >= 1672502400000) {
            query += ` AND DATEDIFF(day, [record_time], '${new Date(
                filter.date,
            ).toLocaleDateString('zh-CN')}')=0`
        }

        const result = await executeQuery(query, params)
        return result[0].count as number
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// delete a Telegram121
async function del(id: number): Promise<number> {
    try {
        const result = await executeQuery(
            'DELETE FROM ' + TableName + ' WHERE id = ?',
            [id],
        )
        return result.affectedRows
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// export functions
export default {
    fetchById,
    fetch,
    count,
    del,
}
