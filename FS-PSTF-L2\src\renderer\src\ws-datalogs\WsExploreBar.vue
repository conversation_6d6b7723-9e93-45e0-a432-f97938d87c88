<template>
    <div
        ref="explorebar"
        class="workspace-explorebar"
        :style="{ width: localWidth + 'px' }"
        tabindex="0"
    >
        <div ref="resizer" class="workspace-explorebar-resizer" />
        <n-el tag="div" class="workspace-explorebar-header">
            <span class="workspace-explorebar-title">数据记录</span>
        </n-el>
        <n-tree
            block-line
            expand-on-click
            :data="treeOptions"
            :selected-keys="selectedKeys"
            :expanded-keys="expandedKeys"
            :show-irrelevant-nodes="false"
            :node-props="nodeProps"
            :render-label="renderNodeLabel"
            :render-prefix="renderNodePrefix"
            :render-suffix="renderNodeSuffix"
        />
    </div>
</template>

<script setup lang="ts">
import { onMounted, Ref, ref, watch, h, onBeforeUnmount } from 'vue'
import { storeToRefs } from 'pinia'
import eventBus from '@common/libs/eventBus'
import {
    dbTelegramHistory,
    dbPdi,
    dbTelegram103,
    dbTelegram111,
    dbTelegram121,
} from '@/models'
import { useSettingsStore } from '@/stores/settings'
import { NTag, TreeOption, useThemeVars } from 'naive-ui'

// settings
const settingsStore = useSettingsStore()
const { appSettings } = storeToRefs(settingsStore)
const { setDatalogExplorebarSize } = settingsStore

const localWidth = ref(0)
const explorebar: Ref<HTMLInputElement> = ref(<HTMLInputElement>(<unknown>null))
const resizer: Ref<HTMLInputElement> = ref(<HTMLInputElement>(<unknown>null))
const explorebarWidthInterval: Ref<NodeJS.Timeout> = ref(
    <NodeJS.Timeout>(<unknown>null),
)

// define emits
const emits = defineEmits([
    'menu-item-click',
    'menu-item-dblclick',
    'tab-rename',
    'tab-remove',
])

// 启用侦听
eventBus.on('updateDatalogExploreBar', () => {
    updateTree.value = true
})
// 在组件卸载之前移除侦听
onBeforeUnmount(() => {
    eventBus.off('updateDatalogExploreBar')
})

const selectedKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const updateTree = ref(true)

// create tree
const treeOptions = ref<TreeOption[]>([
    {
        label: '报文历史',
        key: 'telegram-history',
        count: 0,
    },
    {
        label: '板坯信息',
        key: 'pdi',
        count: 0,
    },
    {
        label: '报文103 (堆垛信息)',
        key: 'telegram-103',
        count: 0,
    },
    {
        label: '报文111 (出料)',
        key: 'telegram-111',
        count: 0,
    },
    {
        label: '报文121 (炉温)',
        key: 'telegram-121',
        count: 0,
    },
])

const updateCounter = async () => {
    const teleHistoryCount = await dbTelegramHistory.count()
    const pdiCount = await dbPdi.count()
    const tele103Count = await dbTelegram103.count()
    const tele111Count = await dbTelegram111.count()
    const tele121Count = await dbTelegram121.count()
    treeOptions.value[0].count = teleHistoryCount
    treeOptions.value[1].count = pdiCount
    treeOptions.value[2].count = tele103Count
    treeOptions.value[3].count = tele111Count
    treeOptions.value[4].count = tele121Count
}

watch(
    updateTree,
    () => {
        if (updateTree.value) {
            updateCounter()
            updateTree.value = false
        }
    },
    { immediate: true }, // create tree immediately on initial render
)

// tree: change default label render, to add edit-in-place function
const renderNodeLabel = ({
    option,
}: {
    option: TreeOption
    selected: boolean
}) => {
    return h('div', { class: 'n-tree-node-content__text' }, option.label)
}

// tree: render node prefix
const renderNodePrefix = ({}: { option: TreeOption; selected: boolean }) => {
    // return icon
    return h('div', { class: 'i-mdi:table text-20' })
}

// tree: render node suffix
const renderNodeSuffix = ({
    option,
}: {
    option: TreeOption
    selected: boolean
}) => {
    //  suffix VNODE
    return h(
        NTag,
        { size: 'small', color: { color: 'white' } },
        { default: () => (option.count as number).toLocaleString('en-US') },
    )
}

// tree props
const nodeProps = ({ option }: { option: TreeOption }) => {
    // Deal with duplicated single click triggered before double click
    let dblClickTimer
    return {
        onClick() {
            clearTimeout(dblClickTimer)
            dblClickTimer = setTimeout(() => {
                // set node selected
                selectedKeys.value = [<string>option.key]
                // emit event to parent
                emits('menu-item-click', option.key, option.label)
            }, 250)
        },
        ondblclick() {
            clearTimeout(dblClickTimer)
            // set node selected
            selectedKeys.value = [<string>option.key]
            // emit event to parent
            emits('menu-item-dblclick', option.key, option.label)
        },
    }
}

// on mounted function
onMounted(() => {
    resizer.value.addEventListener('mousedown', (e: MouseEvent) => {
        e.preventDefault()

        window.addEventListener('mousemove', resize)
        window.addEventListener('mouseup', stopResize)
    })
})

// write change into settings store
watch(localWidth, (val: number) => {
    clearTimeout(explorebarWidthInterval.value)

    explorebarWidthInterval.value = setTimeout(() => {
        setDatalogExplorebarSize(val)
    }, 500)
})

const minWidth = 200
const maxWidth = 300
// resize function
const resize = (e: MouseEvent) => {
    const el = explorebar.value

    let explorebarWidth = e.pageX - el.getBoundingClientRect().left
    //let explorebarWidth = e.pageX
    if (explorebarWidth > maxWidth) explorebarWidth = maxWidth
    if (explorebarWidth < minWidth) explorebarWidth = minWidth
    localWidth.value = explorebarWidth
}

const stopResize = () => {
    window.removeEventListener('mousemove', resize)
}

localWidth.value = appSettings.value.datalogExplorebarSize
if (localWidth.value < minWidth) {
    localWidth.value = minWidth
}

const themeVars = useThemeVars()
</script>

<style lang="scss" scoped>
.workspace-explorebar-resizer {
    position: absolute;
    width: 4px;
    right: -2px;
    top: 0;
    height: calc(100vh - 64px);
    cursor: ew-resize;
    z-index: 99;
    transition: background 0.2s;

    &:hover {
        background: v-bind('themeVars.primaryColor');
        opacity: 0.5;
    }
}

.workspace-explorebar {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    border-right: solid 1px #ccc;
    height: 100%;
    overflow: hidden;

    &:focus {
        outline: none;
    }

    .workspace-explorebar-header {
        width: 100%;
        display: flex;
        align-items: center;
        background-color: #f0f0f0;

        .workspace-explorebar-title {
            //width: 80%;
            font-size: 16px;
            font-weight: 700;
            padding: 6px;
            color: v-bind('themeVars.primaryColor');
        }
    }

    ::v-deep(.n-tree) {
        width: 100%;

        .n-tree-node-wrapper {
            padding: 0;
        }

        .n-tree-node-content {
            .n-tree-node-content__prefix {
                margin-right: 4px;
            }
            .n-tree-node-content__text {
                padding-top: 1px;
            }
        }

        .n-tree-node {
            padding: 6px 0;
            margin-left: -16px;
        }
    }

    .workspace-explorebar-body {
        width: 100%;
        height: calc(100vh - 38px - 64px);
        overflow: overlay;
        padding: 0.4rem;
    }
}
</style>
