{"version": 3, "file": "ServerTelemetryManager.mjs", "sources": ["../../../src/telemetry/server/ServerTelemetryManager.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAgBH,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAC9B,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAU9B,SAAS,kBAAkB,CAAC,MAAiB,EAAA;AACzC,IAAA,MAAM,EACF,IAAI,EACJ,WAAW,EACX,cAAc,EACd,aAAa,EACb,gBAAgB,GACnB,GAAG,MAAM,CAAC;AACX,IAAA,MAAM,MAAM,GAAwC,IAAI,GAAG,CAAC;AACxD,QAAA,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AAClC,QAAA,CAAC,CAAC,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACzC,KAAA,CAAC,CAAC;IACH,IAAI,MAAM,GAAa,EAAE,CAAC;IAE1B,IAAI,IAAI,EAAE,MAAM,EAAE;AACd,QAAA,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;;AAGvC,QAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AACnB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AACJ,KAAA;AAAM,SAAA;AACH,QAAA,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,iBAAiB,CAAC,CAAC;AAC/D,KAAA;IAED,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;AAC1B,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE;AAC5D,YAAA,MAAM,CAAC;gBACH,MAAM;AACN,gBAAA,KAAK,EAAE,GAAG;AACV,gBAAA,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,gBAAA,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;AACvB,aAAA,CAAC,CAAC;AACN,SAAA;AACL,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,MAAM,CAAC,MAKf,EAAA;IACG,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;AACtD,IAAA,IAAI,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE;QACxB,OAAO;AACV,KAAA;AACD,IAAA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAClE,CAAC;AAED;MACa,sBAAsB,CAAA;IAY/B,WACI,CAAA,gBAAwC,EACxC,YAA0B,EAAA;AAJtB,QAAA,IAAA,CAAA,YAAY,GAAiB,YAAY,CAAC,cAAc,CAAC;AAM7D,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC;AACpC,QAAA,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC;QACpD,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,UAAU,IAAI,SAAS,CAAC,YAAY,CAAC;QACxE,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,UAAU,IAAI,SAAS,CAAC,YAAY,CAAC;AAExE,QAAA,IAAI,CAAC,iBAAiB;AAClB,YAAA,sBAAsB,CAAC,SAAS;AAChC,gBAAA,UAAU,CAAC,mBAAmB;gBAC9B,gBAAgB,CAAC,QAAQ,CAAC;KACjC;AAED;;AAEG;IACH,iCAAiC,GAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,CAAG,EAAA,IAAI,CAAC,KAAK,CAAA,EAAG,sBAAsB,CAAC,eAAe,CAAG,EAAA,IAAI,CAAC,YAAY,EAAE,CAAC;QAC7F,MAAM,iBAAiB,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC7D,QAAA,MAAM,qBAAqB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC9D,IAAI,qBAAqB,EAAE,MAAM,EAAE;AAC/B,YAAA,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,qBAAqB,CAAA,CAAE,CAAC,CAAC;AACnE,SAAA;QACD,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CACzC,sBAAsB,CAAC,eAAe,CACzC,CAAC;AACF,QAAA,MAAM,qBAAqB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAC9D,QAAA,MAAM,gCAAgC,GAAG;YACrC,OAAO;YACP,qBAAqB;AACxB,SAAA,CAAC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;QAE/C,OAAO;AACH,YAAA,sBAAsB,CAAC,cAAc;YACrC,gCAAgC;YAChC,cAAc;AACjB,SAAA,CAAC,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;KACrD;AAED;;AAEG;IACH,8BAA8B,GAAA;AAC1B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,MAAM,SAAS,GAAG,sBAAsB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;AACvE,QAAA,MAAM,cAAc,GAAG,YAAY,CAAC,cAAc;AAC7C,aAAA,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;AACvB,aAAA,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;AAClD,QAAA,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM;AAC7B,aAAA,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;AACnB,aAAA,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;AAClD,QAAA,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;;AAG9C,QAAA,MAAM,QAAQ,GACV,SAAS,GAAG,UAAU;cAChB,sBAAsB,CAAC,aAAa;AACtC,cAAE,sBAAsB,CAAC,cAAc,CAAC;AAChD,QAAA,MAAM,cAAc,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,CAC9C,sBAAsB,CAAC,eAAe,CACzC,CAAC;QAEF,OAAO;AACH,YAAA,sBAAsB,CAAC,cAAc;AACrC,YAAA,YAAY,CAAC,SAAS;YACtB,cAAc;YACd,MAAM;YACN,cAAc;AACjB,SAAA,CAAC,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;KACrD;AAED;;;AAGG;AACH,IAAA,kBAAkB,CAAC,KAAc,EAAA;AAC7B,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC5C,QAAA,IACI,YAAY,CAAC,MAAM,CAAC,MAAM;YAC1B,sBAAsB,CAAC,iBAAiB,EAC1C;;AAEE,YAAA,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AACpC,YAAA,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AACpC,YAAA,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AAC/B,SAAA;AAED,QAAA,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAEjE,QAAA,IAAI,KAAK,YAAY,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAE;YACvD,IAAI,KAAK,YAAY,SAAS,EAAE;gBAC5B,IAAI,KAAK,CAAC,QAAQ,EAAE;oBAChB,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC5C,iBAAA;qBAAM,IAAI,KAAK,CAAC,SAAS,EAAE;oBACxB,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC7C,iBAAA;AAAM,qBAAA;oBACH,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9C,iBAAA;AACJ,aAAA;AAAM,iBAAA;gBACH,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9C,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;AAClE,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAChC,IAAI,CAAC,iBAAiB,EACtB,YAAY,EACZ,IAAI,CAAC,aAAa,CACrB,CAAC;QAEF,OAAO;KACV;AAED;;AAEG;IACH,kBAAkB,GAAA;AACd,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC5C,QAAA,YAAY,CAAC,SAAS,IAAI,CAAC,CAAC;AAE5B,QAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAChC,IAAI,CAAC,iBAAiB,EACtB,YAAY,EACZ,IAAI,CAAC,aAAa,CACrB,CAAC;QACF,OAAO,YAAY,CAAC,SAAS,CAAC;KACjC;AAED;;AAEG;IACH,eAAe,GAAA;AACX,QAAA,MAAM,YAAY,GAA0B;AACxC,YAAA,cAAc,EAAE,EAAE;AAClB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,SAAS,EAAE,CAAC;SACf,CAAC;AACF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CACrD,IAAI,CAAC,iBAAiB,CACA,CAAC;QAE3B,OAAO,YAAY,IAAI,YAAY,CAAC;KACvC;AAED;;AAEG;IACH,mBAAmB,GAAA;AACf,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,MAAM,gBAAgB,GAClB,sBAAsB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;AACzD,QAAA,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;QAC9C,IAAI,gBAAgB,KAAK,UAAU,EAAE;;AAEjC,YAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CACxB,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC;AACL,SAAA;AAAM,aAAA;;AAEH,YAAA,MAAM,iBAAiB,GAA0B;gBAC7C,cAAc,EAAE,YAAY,CAAC,cAAc,CAAC,KAAK,CAC7C,gBAAgB,GAAG,CAAC,CACvB;gBACD,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC;AACnD,gBAAA,SAAS,EAAE,CAAC;aACf,CAAC;AAEF,YAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAChC,IAAI,CAAC,iBAAiB,EACtB,iBAAiB,EACjB,IAAI,CAAC,aAAa,CACrB,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;IACH,OAAO,eAAe,CAClB,qBAA4C,EAAA;AAE5C,QAAA,IAAI,CAAC,CAAC;QACN,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,QAAQ,GAAG,CAAC,CAAC;AACjB,QAAA,MAAM,UAAU,GAAG,qBAAqB,CAAC,MAAM,CAAC,MAAM,CAAC;QACvD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;;YAE7B,MAAM,KAAK,GACP,qBAAqB,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC3C,SAAS,CAAC,YAAY,CAAC;YAC3B,MAAM,aAAa,GACf,qBAAqB,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC/C,SAAS,CAAC,YAAY,CAAC;AAC3B,YAAA,MAAM,SAAS,GACX,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,YAAY,CAAC;;YAG9D,QAAQ;AACJ,gBAAA,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM;AACvB,oBAAA,aAAa,CAAC,QAAQ,EAAE,CAAC,MAAM;AAC/B,oBAAA,SAAS,CAAC,MAAM;AAChB,oBAAA,CAAC,CAAC;AAEN,YAAA,IAAI,QAAQ,GAAG,sBAAsB,CAAC,qBAAqB,EAAE;;gBAEzD,SAAS,IAAI,CAAC,CAAC;AAClB,aAAA;AAAM,iBAAA;gBACH,MAAM;AACT,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,SAAS,CAAC;KACpB;AAED;;;;AAIG;IACH,wBAAwB,GAAA;QACpB,MAAM,qBAAqB,GAAa,EAAE,CAAC;QAE3C,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;QACtE,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;QACxE,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,aAAa,IAAI,SAAS,CAAC,YAAY,CAC/C,CAAC;AAEF,QAAA,OAAO,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC1C;AAED;;;;;AAKG;AACH,IAAA,6BAA6B,CACzB,uBAAgD,EAAA;AAEhD,QAAA,IAAI,CAAC,UAAU,GAAG,uBAAuB,CAAC,WAAW,CAAC;AACtD,QAAA,IAAI,CAAC,YAAY,GAAG,uBAAuB,CAAC,aAAa,CAAC;AAC1D,QAAA,IAAI,CAAC,aAAa,GAAG,uBAAuB,CAAC,cAAc,CAAC;KAC/D;AAED;;AAEG;AACH,IAAA,eAAe,CAAC,YAA0B,EAAA;AACtC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KACpC;AAED,IAAA,wBAAwB,CAAC,SAAiB,EAAA;AACtC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC5C,QAAA,YAAY,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC/C,QAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAChC,IAAI,CAAC,iBAAiB,EACtB,YAAY,EACZ,IAAI,CAAC,aAAa,CACrB,CAAC;KACL;IAED,wBAAwB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,qBAAqB,CAAC;KACvD;IAED,0BAA0B,GAAA;AACtB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,OAAO,YAAY,CAAC,qBAAqB,CAAC;AAC1C,QAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAChC,IAAI,CAAC,iBAAiB,EACtB,YAAY,EACZ,IAAI,CAAC,aAAa,CACrB,CAAC;KACL;IAED,OAAO,kBAAkB,CAAC,MAAiB,EAAA;AACvC,QAAA,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;KACrC;AACJ;;;;"}