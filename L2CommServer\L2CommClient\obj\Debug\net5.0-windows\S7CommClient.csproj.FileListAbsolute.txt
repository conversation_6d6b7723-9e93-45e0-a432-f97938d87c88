C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\S7CommClient.exe
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\S7CommClient.deps.json
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\S7CommClient.runtimeconfig.json
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\S7CommClient.runtimeconfig.dev.json
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\S7CommClient.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\ref\S7CommClient.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\S7CommClient.pdb
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\FontAwesome6.Core.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\FontAwesome6.Fonts.Net.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\FontAwesome6.Shared.Net.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\NetCoreServer.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\Serilog.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\Serilog.Sinks.RichTextBox.Wpf.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\S7.Net.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\Telegrams.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\Telegrams.pdb
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\S7.Net.pdb
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\S7.Net.xml
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\MainWindow.g.cs
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\App.g.cs
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient_MarkupCompile.cache
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient_MarkupCompile.lref
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\MainWindow.baml
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\Resources\StyleResources.baml
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.g.resources
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.AssemblyInfo.cs
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.csproj.CopyComplete
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\ref\S7CommClient.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.pdb
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\S7CommClient.genruntimeconfig.cache
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\obj\Debug\net5.0-windows\App.baml
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\Dapper.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\HandyControl.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\Microsoft.Data.Sqlite.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\SQLitePCLRaw.batteries_v2.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\SQLitePCLRaw.core.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\alpine-x64\native\libe_sqlite3.so
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\win-arm\native\e_sqlite3.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\win-x64\native\e_sqlite3.dll
C:\Users\<USER>\source\repos\S7CommServer\S7CommClient\bin\Debug\net5.0-windows\runtimes\win-x86\native\e_sqlite3.dll
