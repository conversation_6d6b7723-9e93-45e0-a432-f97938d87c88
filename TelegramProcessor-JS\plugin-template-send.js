const { SendPluginBase, exportPlugin, S7DataTypes } = require('../plugin-base');

/**
 * Template for creating send plugins
 * Copy this file and modify it for your specific telegram composition needs
 */
class TeleSendTemplate extends SendPluginBase {
    constructor() {
        super();

        // REQUIRED: Set these properties for your plugin
        this.telegramNo = 998; // Change to your telegram number
        this.pluginName = 'TeleSendTemplate'; // Change to your plugin name
        this.version = '1.0.0';
        this.description = 'Template for send plugins';
        this.author = 'Your Name';

        // Set sender and receiver IDs
        this.sendId = 'L2'; // Sender ID (2 characters max)
        this.recId = 'PL'; // Receiver ID (2 characters max)

        // Add your plugin-specific properties here
        // Example:
        // this.value1 = 0;
        // this.value2 = 0.0;
        // this.status = false;
        // this.timestamp = new Date();
    }

    /**
     * Compose message body
     * REQUIRED: Implement this method to create your telegram format
     * @returns {Buffer} - Message body
     */
    composeBody() {
        try {
            // Define your message format and size
            // Example: 12 bytes total (4 + 4 + 4)
            const bodySize = 12;
            const body = Buffer.alloc(bodySize);
            let offset = 0;

            // Compose your message format here
            // Example for different data types:

            // Write 32-bit integer at offset 0
            // DataTypes.dIntToBuffer(this.value1).copy(body, offset);
            // offset += 4;

            // Write 32-bit float at offset 4
            // DataTypes.realToBuffer(this.value2).copy(body, offset);
            // offset += 4;

            // Write boolean as 32-bit integer at offset 8
            // DataTypes.dIntToBuffer(this.status ? 1 : 0).copy(body, offset);
            // offset += 4;

            // Add your composition logic here
            console.log(
                `[${this.pluginName}] Composing telegram ${this.telegramNo} - implement your logic here`
            );

            // For template, just fill with zeros
            body.fill(0);

            return body;
        } catch (error) {
            console.error(
                `[${this.pluginName}] Error composing message body:`,
                error
            );
            throw error;
        }
    }

    /**
     * Compose complete telegram (header + body)
     * OPTIONAL: Override this if you need custom header handling
     * @returns {Buffer} - Complete telegram
     */
    compose() {
        try {
            const body = this.composeBody();
            const telegram = this.composeWithHeader(body);

            console.log(
                `[${this.pluginName}] Telegram composed successfully (${telegram.length} bytes)`
            );

            return telegram;
        } catch (error) {
            console.error(
                `[${this.pluginName}] Error composing telegram:`,
                error
            );
            throw error;
        }
    }

    /**
     * Debug output for the plugin
     * OPTIONAL: Override this method to provide debug information
     */
    debugOutput() {
        console.log(`[${this.pluginName}] Debug Info:`);
        console.log(`  Telegram Number: ${this.telegramNo}`);
        console.log(`  Sender ID: ${this.sendId}`);
        console.log(`  Receiver ID: ${this.recId}`);
        console.log(`  Counter: ${this.telCounter}`);
        // Add your debug output here
        // Example:
        // console.log(`  Value1: ${this.value1}`);
        // console.log(`  Value2: ${this.value2.toFixed(2)}`);
        // console.log(`  Status: ${this.status}`);
        // console.log(`  Timestamp: ${this.timestamp.toISOString()}`);
    }

    // Add setter methods for your data
    // Example methods:

    /**
     * Set value1
     * @param {number} value - New value
     */
    // setValue1(value) {
    //     this.value1 = value;
    //     this.timestamp = new Date();
    //     console.log(`[${this.pluginName}] Value1 set to ${value}`);
    // }

    /**
     * Set value2
     * @param {number} value - New value
     */
    // setValue2(value) {
    //     this.value2 = value;
    //     this.timestamp = new Date();
    // }

    /**
     * Set status
     * @param {boolean} status - New status
     */
    // setStatus(status) {
    //     this.status = status;
    //     this.timestamp = new Date();
    //     console.log(`[${this.pluginName}] Status set to ${status}`);
    // }

    /**
     * Update all values from external source
     * @param {Object} data - Data object with values to update
     */
    // updateFromData(data) {
    //     if (data.value1 !== undefined) this.setValue1(data.value1);
    //     if (data.value2 !== undefined) this.setValue2(data.value2);
    //     if (data.status !== undefined) this.setStatus(data.status);
    // }

    /**
     * Get current data as object
     * @returns {Object} - Current data
     */
    // getCurrentData() {
    //     return {
    //         telegramNo: this.telegramNo,
    //         value1: this.value1,
    //         value2: this.value2,
    //         status: this.status,
    //         timestamp: this.timestamp,
    //         counter: this.telCounter
    //     };
    // }

    /**
     * Reset all values to defaults
     */
    // reset() {
    //     this.value1 = 0;
    //     this.value2 = 0.0;
    //     this.status = false;
    //     this.timestamp = new Date();
    //     console.log(`[${this.pluginName}] Values reset to defaults`);
    // }

    /**
     * Validate data before sending
     * @returns {boolean} - True if data is valid
     */
    // validateData() {
    //     // Add your validation logic here
    //     // Example:
    //     // if (this.value1 < 0 || this.value1 > 1000) {
    //     //     console.error(`[${this.pluginName}] Value1 out of range: ${this.value1}`);
    //     //     return false;
    //     // }
    //     //
    //     // if (isNaN(this.value2)) {
    //     //     console.error(`[${this.pluginName}] Value2 is not a number: ${this.value2}`);
    //     //     return false;
    //     // }
    //
    //     return true;
    // }
}

// Export the plugin
module.exports = exportPlugin(TeleSendTemplate);
