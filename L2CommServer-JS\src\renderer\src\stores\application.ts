import { defineStore } from 'pinia'
import { useElectronStore } from '@/composables'
import { naiveThemeOverrides } from '../themes/themeSettings'

const persistentStore = useElectronStore('application')

export const useApplicationStore = defineStore('application', {
    state: () => ({
        appName:
            window.electron.ipcRenderer.sendSync('get-app-name') || '加热炉L2',
        appVersion:
            window.electron.ipcRenderer.sendSync('get-app-version') || '0',
        cachedVersion: persistentStore.get('cached_version', '0') as string,
        isLoading: false,
        selectedSettingTab: 'general',
        selectedPage: 'INFO',
        updateStatus: 'noupdate', // 'noupdate' | 'available' | 'checking' | 'nocheck' | 'downloading' | 'downloaded' | 'disabled'
        downloadProgress: 0,
        naiveThemeOverrides,
    }),
    getters: {
        getSelectedPage: (state) => state.selectedPage,
        getDownloadProgress: (state) =>
            Number(state.downloadProgress.toFixed(1)),
    },
    actions: {
        checkVersionUpdate() {
            if (this.appVersion !== this.cachedVersion) {
                this.cachedVersion = this.appVersion
                persistentStore.set('cached_version', this.cachedVersion)
            }
        },
        setLoadingStatus(payload: boolean) {
            this.isLoading = payload
        },
        // Pages
        // default page is INFO
        selectPage(name: string) {
            if (!name) this.selectedPage = 'INFO'
            else this.selectedPage = name
        },
    },
})
