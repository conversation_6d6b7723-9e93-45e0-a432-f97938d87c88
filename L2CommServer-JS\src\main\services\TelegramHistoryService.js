import { loggingService } from './LoggingService.js';

/**
 * Telegram History Service
 * Handles telegram-specific database operations
 */
class TelegramHistoryService {
    constructor(databaseService) {
        this.db = databaseService;
        this.logger = loggingService.createLogger('TelegramHistoryService');
    }

    /**
     * Initialize telegram history tables
     */
    async initializeTables() {
        try {
            // Create telegram history table if it doesn't exist
            const hasTable = await this.db.hasTable('telegram_history');
            if (!hasTable) {
                await this.db.createTable('telegram_history', (table) => {
                    table.increments('id').primary();
                    table.integer('telegram_no').notNullable();
                    table.integer('telegram_length').notNullable();
                    table.string('header_type', 20).notNullable(); // 'TelegramHeader20' or 'TelegramHeader8'
                    table.string('sender_id', 2).nullable(); // Only for TelegramHeader20
                    table.string('receiver_id', 2).nullable(); // Only for TelegramHeader20
                    table.datetime('create_time').nullable(); // Only for TelegramHeader20
                    table.integer('telegram_counter').notNullable();
                    table.integer('spare').nullable(); // Spare bytes
                    table.string('peer_ip', 45).notNullable();
                    table.string('direction', 1).notNullable(); // 'R' or 'S'
                    table.binary('telegram_data');
                    table.datetime('logged_at').defaultTo(this.db.fn.now());

                    // Indexes
                    table.index('telegram_no');
                    table.index('header_type');
                    table.index('peer_ip');
                    table.index('direction');
                    table.index('logged_at');
                });

                this.logger.info('Created telegram_history table');
            }

            // Create application logs table if it doesn't exist
            const hasLogsTable = await this.db.hasTable('application_logs');
            if (!hasLogsTable) {
                await this.db.createTable('application_logs', (table) => {
                    table.increments('id').primary();
                    table.string('level', 10).notNullable();
                    table.text('message').notNullable();
                    table.string('component', 50);
                    table.datetime('timestamp').defaultTo(this.db.fn.now());

                    // Indexes
                    table.index('level');
                    table.index('component');
                    table.index('timestamp');
                });

                this.logger.info('Created application_logs table');
            }
        } catch (error) {
            this.logger.error(
                'Failed to initialize telegram history tables:',
                error
            );
            throw error;
        }
    }

    /**
     * Save telegram to database
     * @param {Object} telegramHeader - Telegram header information
     * @param {Buffer} telegramData - Complete telegram data
     * @returns {number} - Inserted record ID
     */
    async saveTelegram(telegramHeader, telegramData) {
        try {
            if (!this.db.isConnected()) {
                throw new Error('Database not connected');
            }

            const telegramRecord = {
                telegram_no: telegramHeader.telNo,
                telegram_length: telegramHeader.telLen,
                header_type: telegramHeader.headerType,
                telegram_counter: telegramHeader.telCounter,
                peer_ip: telegramHeader.peerIp,
                direction: telegramHeader.direction,
                telegram_data: telegramData,
            };

            // Add fields specific to TelegramHeader20
            if (telegramHeader.headerType === 'TelegramHeader20') {
                telegramRecord.sender_id = telegramHeader.sendId;
                telegramRecord.receiver_id = telegramHeader.recId;
                telegramRecord.create_time = telegramHeader.createTime;
            }

            // Add spare field if available
            if (telegramHeader.spare !== undefined) {
                telegramRecord.spare = telegramHeader.spare;
            }

            const [id] = await this.db.insert(
                'telegram_history',
                telegramRecord
            );

            this.logger.debug(
                `Saved telegram ${telegramHeader.telNo} to database with ID ${id}`
            );
            return id;
        } catch (error) {
            this.logger.error('Failed to save telegram to database:', error);
            throw error;
        }
    }

    /**
     * Get telegram history
     * @param {Object} options - Query options
     * @param {number} options.limit - Maximum number of records
     * @param {number} options.offset - Offset for pagination
     * @param {string} options.direction - Filter by direction ('R' or 'S')
     * @param {number} options.telegramNo - Filter by telegram number
     * @param {string} options.peerIp - Filter by peer IP
     * @returns {Array} - Telegram history records
     */
    async getTelegramHistory(options = {}) {
        try {
            if (!this.db.isConnected()) {
                throw new Error('Database not connected');
            }

            let query = this.db
                .select('*')
                .from('telegram_history')
                .orderBy('logged_at', 'desc');

            // Apply filters
            if (options.direction) {
                query = query.where('direction', options.direction);
            }

            if (options.telegramNo) {
                query = query.where('telegram_no', options.telegramNo);
            }

            if (options.peerIp) {
                query = query.where('peer_ip', options.peerIp);
            }

            // Apply pagination
            if (options.limit) {
                query = query.limit(options.limit);
            }

            if (options.offset) {
                query = query.offset(options.offset);
            }

            const results = await query;
            return results;
        } catch (error) {
            this.logger.error('Failed to get telegram history:', error);
            throw error;
        }
    }

    /**
     * Get telegram statistics
     * @returns {Object} - Statistics object
     */
    async getTelegramStatistics() {
        try {
            if (!this.db.isConnected()) {
                throw new Error('Database not connected');
            }

            const [stats] = await this.db
                .select(
                    this.db.raw('COUNT(*) as total_telegrams'),
                    this.db.raw(
                        "COUNT(CASE WHEN direction = 'R' THEN 1 END) as received_telegrams"
                    ),
                    this.db.raw(
                        "COUNT(CASE WHEN direction = 'S' THEN 1 END) as sent_telegrams"
                    ),
                    this.db.raw('COUNT(DISTINCT peer_ip) as unique_peers'),
                    this.db.raw(
                        'COUNT(DISTINCT telegram_no) as unique_telegram_types'
                    )
                )
                .from('telegram_history');

            return {
                totalTelegrams: parseInt(stats.total_telegrams) || 0,
                receivedTelegrams: parseInt(stats.received_telegrams) || 0,
                sentTelegrams: parseInt(stats.sent_telegrams) || 0,
                uniquePeers: parseInt(stats.unique_peers) || 0,
                uniqueTelegramTypes: parseInt(stats.unique_telegram_types) || 0,
            };
        } catch (error) {
            this.logger.error('Failed to get telegram statistics:', error);
            throw error;
        }
    }

    /**
     * Get telegram by ID
     * @param {number} id - Telegram ID
     * @returns {Object} - Telegram record
     */
    async getTelegramById(id) {
        try {
            if (!this.db.isConnected()) {
                throw new Error('Database not connected');
            }

            const telegram = await this.db
                .select('*')
                .from('telegram_history')
                .where('id', id)
                .first();

            return telegram;
        } catch (error) {
            this.logger.error('Failed to get telegram by ID:', error);
            throw error;
        }
    }

    /**
     * Delete old telegram records
     * @param {number} daysToKeep - Number of days to keep
     * @returns {number} - Number of deleted records
     */
    async cleanupOldTelegrams(daysToKeep = 30) {
        try {
            if (!this.db.isConnected()) {
                throw new Error('Database not connected');
            }

            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            const deletedCount = await this.db
                .delete()
                .from('telegram_history')
                .where('logged_at', '<', cutoffDate);

            this.logger.info(
                `Cleaned up ${deletedCount} old telegram records (older than ${daysToKeep} days)`
            );
            return deletedCount;
        } catch (error) {
            this.logger.error('Failed to cleanup old telegrams:', error);
            throw error;
        }
    }

    /**
     * Save application log to database
     * @param {string} level - Log level
     * @param {string} message - Log message
     * @param {string} component - Component name
     * @returns {number} - Inserted record ID
     */
    async saveLog(level, message, component = null) {
        try {
            if (!this.db.isConnected()) {
                return null; // Don't throw error for logging
            }

            const [id] = await this.db.insert('application_logs', {
                level: level,
                message: message,
                component: component,
            });

            return id;
        } catch (error) {
            // Don't log database errors for logging to avoid recursion
            console.error('Failed to save log to database:', error);
            return null;
        }
    }

    /**
     * Get application logs
     * @param {Object} options - Query options
     * @returns {Array} - Log records
     */
    async getApplicationLogs(options = {}) {
        try {
            if (!this.db.isConnected()) {
                throw new Error('Database not connected');
            }

            let query = this.db
                .select('*')
                .from('application_logs')
                .orderBy('timestamp', 'desc');

            // Apply filters
            if (options.level) {
                query = query.where('level', options.level);
            }

            if (options.component) {
                query = query.where('component', options.component);
            }

            // Apply pagination
            if (options.limit) {
                query = query.limit(options.limit);
            }

            if (options.offset) {
                query = query.offset(options.offset);
            }

            const results = await query;
            return results;
        } catch (error) {
            this.logger.error('Failed to get application logs:', error);
            throw error;
        }
    }
}

export { TelegramHistoryService };
