import { app, shell, BrowserWindow, nativeImage, ipcMain } from 'electron';
import path from 'path';
import { electronApp, optimizer, is } from '@electron-toolkit/utils';
import Store from 'electron-store';
import { ServerService } from './services/ServerService.js';
import { loggingService } from './services/LoggingService.js';
import { TrayService } from './services/TrayService.js';
import initializeIpcHandlers from './ipc-handlers/index.js';

Store.initRenderer();

const isDevelopment = !app.isPackaged;
const isMacOS = process.platform === 'darwin';
const isLinux = process.platform === 'linux';
const isWindows = process.platform === 'win32';
const gotTheLock = app.requestSingleInstanceLock();

// Initialize services
let serverService = null;
let trayService = null;

function createWindow() {
    // Create the browser window.
    const mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 1024,
        minHeight: 768,
        show: !isWindows,
        title: 'L2 Communication Server',
        webPreferences: {
            preload: path.join(__dirname, '../preload/index.js'),
            sandbox: false,
            nodeIntegration: false,
            contextIsolation: true,
        },
        autoHideMenuBar: true,
        titleBarStyle: isLinux ? 'default' : 'hidden',
        titleBarOverlay: isWindows
            ? {
                  color: '#f3f4f6',
                  symbolColor: '#333',
                  height: 36,
              }
            : false,
        trafficLightPosition: isMacOS ? { x: 10, y: 8 } : undefined,
        backgroundColor: '#fff',
    });

    mainWindow.on('ready-to-show', () => {
        mainWindow.show();
    });

    mainWindow.webContents.setWindowOpenHandler((details) => {
        shell.openExternal(details.url);
        return { action: 'deny' };
    });

    // Handle window close event - minimize to tray instead of closing
    mainWindow.on('close', (event) => {
        if (trayService) {
            trayService.handleWindowClose(event);
        }
    });

    // HMR for renderer base on electron-vite cli.
    // Load the remote URL for development or the local html file for production.
    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
        mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL']);
    } else {
        mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    loggingService.info('L2CommServer-JS is ready');

    return mainWindow;
}

let mainWindow;
if (!gotTheLock) app.quit();
else {
    // This method will be called when Electron has finished
    // initialization and is ready to create browser windows.
    app.whenReady().then(() => {
        // Set app user model id for windows
        electronApp.setAppUserModelId('com.l2commserver.js');

        // Default open or close DevTools by F12 in development
        app.on('browser-window-created', (_, window) => {
            optimizer.watchWindowShortcuts(window);
        });

        // set dock image for macos
        if (isMacOS && app.dock) {
            // app.dock.setIcon(nativeImage.createFromDataURL(icon));
        }

        mainWindow = createWindow();

        // Initialize IPC handlers (including logging)
        initializeIpcHandlers(mainWindow);

        // Initialize server service
        serverService = new ServerService();
        setupIpcHandlers(mainWindow, serverService);

        // Initialize system tray
        trayService = new TrayService();
        trayService.initialize(mainWindow);

        if (isDevelopment) {
            // mainWindow.webContents.openDevTools();
        }
    });

    // Quit when all windows are closed, except on macOS.
    // With tray, we don't quit when windows are closed unless explicitly requested
    app.on('window-all-closed', () => {
        // Don't quit the app when all windows are closed if we have a tray
        // The app will continue running in the background
        if (process.platform !== 'darwin' && !trayService) {
            app.quit();
        }
    });

    app.on('activate', function () {
        // On macOS it's common to re-create a window in the app when the
        // dock icon is clicked and there are no other windows open.
        if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });

    // Cleanup before quit
    app.on('before-quit', () => {
        if (trayService) {
            trayService.destroy();
        }
        if (serverService) {
            serverService.stop();
        }
    });
}

/**
 * Setup IPC handlers for communication with renderer process
 */
function setupIpcHandlers(mainWindow, serverService) {
    // Server control handlers
    ipcMain.handle('server:start', async () => {
        try {
            // TODO: Load configuration from file (will be implemented in configuration task)
            const defaultConfig = {
                server: {
                    ip: '127.0.0.1',
                    port: 1111,
                    title: 'L2 Communication Server',
                },
                clients: [],
                cyclicSends: [],
            };

            await serverService.start(defaultConfig);
            return { success: true };
        } catch (error) {
            log.error('Failed to start server:', error);
            return { success: false, error: error.message };
        }
    });

    ipcMain.handle('server:stop', async () => {
        try {
            await serverService.stop();
            return { success: true };
        } catch (error) {
            log.error('Failed to stop server:', error);
            return { success: false, error: error.message };
        }
    });

    ipcMain.handle('server:status', () => {
        return serverService.getStatus();
    });

    // Statistics handlers
    ipcMain.handle('db:getStatistics', () => {
        return serverService.getServerStatistics();
    });

    // Plugin management handlers
    ipcMain.handle('plugins:load', () => {
        try {
            return serverService.getAllPlugins();
        } catch (error) {
            log.error('Failed to get plugins:', error);
            return { error: error.message };
        }
    });

    ipcMain.handle('plugins:reload', async () => {
        try {
            await serverService.reloadAllPlugins();
            return {
                success: true,
                message: 'All plugins reloaded successfully',
            };
        } catch (error) {
            log.error('Failed to reload plugins:', error);
            return { success: false, error: error.message };
        }
    });

    ipcMain.handle('plugins:list', () => {
        try {
            return serverService.getAllPlugins();
        } catch (error) {
            log.error('Failed to get plugin list:', error);
            return { receive: [], send: [], total: 0 };
        }
    });

    ipcMain.handle('plugins:statistics', () => {
        try {
            return serverService.getPluginStatistics();
        } catch (error) {
            log.error('Failed to get plugin statistics:', error);
            return { receivePlugins: 0, sendPlugins: 0, totalPlugins: 0 };
        }
    });

    ipcMain.handle(
        'plugins:sendTelegram',
        async (event, telegramNo, targetIp) => {
            try {
                const success = await serverService.sendTelegramWithPlugin(
                    telegramNo,
                    targetIp
                );
                return {
                    success,
                    message: success
                        ? 'Telegram sent successfully'
                        : 'Failed to send telegram',
                };
            } catch (error) {
                log.error('Failed to send telegram:', error);
                return { success: false, error: error.message };
            }
        }
    );

    // Placeholder handlers for future implementation
    ipcMain.handle('config:load', () => {
        return { message: 'Configuration loading not yet implemented' };
    });

    ipcMain.handle('config:save', (event, config) => {
        return { message: 'Configuration saving not yet implemented' };
    });

    ipcMain.handle('db:getTelegramHistory', (event, limit) => {
        return { message: 'Telegram history not yet implemented' };
    });

    // Tray service handlers
    ipcMain.handle('tray:updateMenu', (event, state) => {
        if (trayService) {
            trayService.updateTrayMenu(state);
        }
        return { success: true };
    });

    // Set up server event forwarding to renderer
    serverService.on('server:started', (data) => {
        mainWindow.webContents.send('server:event', {
            status: 'running',
            data,
        });
        // Update tray menu to reflect server status
        if (trayService) {
            trayService.updateTrayMenu({
                serverRunning: true,
                connectionCount: 0,
            });
        }
    });

    serverService.on('server:stopped', () => {
        mainWindow.webContents.send('server:event', { status: 'stopped' });
        // Update tray menu to reflect server status
        if (trayService) {
            trayService.updateTrayMenu({
                serverRunning: false,
                connectionCount: 0,
            });
        }
    });

    serverService.on('connection:changed', (data) => {
        mainWindow.webContents.send('connection:changed', data);
        // Update tray menu with connection count
        if (trayService) {
            trayService.updateTrayMenu({
                serverRunning: serverService.isRunning(),
                connectionCount: data.connectionCount || 0,
            });
        }
    });

    serverService.on('telegram:received', (data) => {
        mainWindow.webContents.send('telegram:received', data);
    });

    serverService.on('telegram:sent', (data) => {
        mainWindow.webContents.send('telegram:sent', data);
    });

    // Log forwarding is now handled by LoggingService
}
