<template>
    <div v-show="isSelected" class="panel">
        <n-space vertical>
            <n-space justify="space-between">
                <div>
                    <n-form
                        :show-require-mark="false"
                        :show-feedback="false"
                        label-width="90px"
                        label-placement="left"
                        size="small"
                    >
                        <n-space>
                            <n-form-item label="查找" />
                            <n-form-item
                                label="轧制计划号"
                                path="prg_no"
                                style="width: 240px"
                            >
                                <n-input
                                    v-model:value="inputFilter.prg_no"
                                    @update:value="
                                        (value) => updateFilter('prg_no', value)
                                    "
                                    data-testid="input-filter-prg_no"
                                    :clearable="true"
                                />
                            </n-form-item>

                            <n-form-item
                                label="钢种"
                                path="steel_grade"
                                style="width: 240px"
                            >
                                <n-input
                                    v-model:value="inputFilter.steel_grade"
                                    @update:value="
                                        (value) =>
                                            updateFilter('steel_grade', value)
                                    "
                                    data-testid="input-filter-steel_grade"
                                    :clearable="true"
                                />
                            </n-form-item>
                        </n-space>
                    </n-form>
                </div>
                <AutoRefresh
                    :default-refresh="true"
                    :default-interval="30"
                    @refresh="refreshData"
                />

                <n-button
                    type="primary"
                    size="small"
                    data-testid="button-add"
                    @click="showAddEdit"
                >
                    新增
                </n-button>
            </n-space>
            <n-data-table
                remote
                :row-key="(row) => row.id"
                :columns="columns"
                :data="telegrams"
                :loading="loading"
                :pagination="paginationRef"
                :on-update:page="handlePageChange"
                :single-line="false"
                titleAlign="center"
                striped
                size="small"
            />
        </n-space>
        <!-- 新增数据组件 -->
        <PdiAddEdit
            :mode="drawerMode"
            :editing-id="editRowId"
            @handle-add="handleAdd"
            @handle-edit="handleEdit"
            @update:mode="drawerMode = $event"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, h, onMounted } from 'vue'
import { DataTableColumns, NSpace, useMessage } from 'naive-ui'
import eventBus from '@common/libs/eventBus'
import { dbPdi, Pdi, PdiFilterOptions } from '@/models'
import { useDataTable, useFilter } from '@/composables'
// UI components
import { AutoRefresh, PdiAddEdit, RowButtons } from './components'

// define emits
const emits = defineEmits(['add-pdi'])

// 接收父组件传递过来的值
defineProps({
    tabUid: String,
    isSelected: Boolean,
})

const message = useMessage()

// edit
const editRowId = ref(0)
// to be deleted row ID, 0 means not in deleting mode
const deletingRowId = ref(0)
const drawerMode = ref('hidden')
const showAddEdit = () => {
    drawerMode.value = 'add'
    console.log('show Add')
}

const fetchData = async (start: number, pageSize: number) => {
    return await dbPdi.fetch(start, pageSize, 'desc', inputFilter.value)
}

const fetchCount = async () => {
    return await dbPdi.count(inputFilter.value)
}

const mapTelegram = (tele: Pdi) => {
    return {
        ...tele,
        isEdit: false,
        isDeletable: false,
    }
}

const {
    telegrams,
    loading,
    pagination: paginationRef,
    handlePageChange,
    refreshData,
    renderTableTitle,
} = useDataTable<Pdi>(fetchData, fetchCount, mapTelegram, 15)

// setup filter
const { filter: inputFilter, updateFilter } =
    useFilter<PdiFilterOptions>(refreshData)

const createColumns = (): DataTableColumns<Pdi> => {
    const columns = [
        {
            title: renderTableTitle('#'),
            key: 'id',
            width: 60,
            className: 'center',
        },
        {
            title: renderTableTitle('轧制计划号'),
            key: 'prg_no',
            width: 200,
            className: 'center',
        },
        {
            title: renderTableTitle('顺序号'),
            key: 'seq_no',
            width: 60,
            className: 'center',
        },
        {
            title: renderTableTitle('板坯号'),
            key: 'mat_no',
            width: 200,
            className: 'center',
        },
        {
            title: renderTableTitle('钢种'),
            key: 'steel_grade',
            width: 200,
            className: 'center',
        },
        {
            title: renderTableTitle('宽度 mm'),
            key: 'slab_width',
            width: 100,
            className: 'center',
        },
        {
            title: renderTableTitle('厚度 mm'),
            key: 'slab_thick',
            width: 100,
            className: 'center',
        },
        {
            title: renderTableTitle('长度 mm'),
            key: 'slab_length',
            width: 100,
            className: 'center',
        },
        {
            title: renderTableTitle('重量 t'),
            key: 'slab_weight',
            width: 100,
            className: 'center',
        },
        {
            title: renderTableTitle('创建日期'),
            key: 'create_time',
            width: 200,
            className: 'center',
            render: (rowData: Pdi, _rowIndex: number) => {
                return rowData.record_time.toLocaleString('zh-CN', {
                    timeZone: 'UTC',
                })
            },
        },
    ]
    // define buttons
    // define buttons column
    const buttonsCol = {
        key: 'options',
        width: 80,
        title: renderTableTitle('选项'),
        className: 'center',
        render: (rowData: Pdi, _rowIndex: number) => {
            // return group of buttons
            return h(RowButtons, {
                isViewable: false,
                isEditable: true,
                isDeletable: true,
                // edit button click
                onEditRow() {
                    editRowId.value = rowData.id as number
                    drawerMode.value = 'edit'
                },
                // delete button click but not confirmed
                onDeleteWarning() {
                    deletingRowId.value = rowData.id as number
                },
                // delete cancelled
                onDeleteCancel() {
                    deletingRowId.value = 0
                },
                // delete confirmed
                onDeleteRow() {
                    deleteRow(rowData.id as number)
                },
            })
        },
    }
    return [...columns, buttonsCol] as unknown as DataTableColumns<Pdi>
}

// create columns
const columns = ref<DataTableColumns<Pdi>>()
columns.value = createColumns()

onMounted(async () => {
    refreshData()
})

const handleAdd = async (pdi: Pdi) => {
    // hide drawer
    drawerMode.value = 'hidden'

    // save
    try {
        await dbPdi.add(pdi)
        message.success('保存成功')
        refreshData()
    } catch (error) {
        window.electron.ipcRenderer.invoke(
            'add-log',
            'error',
            '保存板坯数据失败:',
            error,
        )
        message.error('保存失败,请检查设置并重试')
    }
}

const handleEdit = async (pdi: Pdi) => {
    // hide drawer
    drawerMode.value = 'hidden'

    // save
    try {
        await dbPdi.update(editRowId.value, pdi)
        message.success('更新成功')
        refreshData()
    } catch (error) {
        window.electron.ipcRenderer.invoke(
            'add-log',
            'error',
            '更新数据库设置失败:',
            error,
        )
        message.error('更新失败,请检查设置并重试')
    } finally {
        editRowId.value = 0
    }
}

// delete row
const deleteRow = async (id: number) => {
    // find row by id
    const row = telegrams.value.find((row) => row.id === id)
    // log
    window.electron.ipcRenderer.invoke(
        'add-log',
        'info',
        'Delete from Telegram111, row id',
        row?.id,
    )

    // delete
    if (await dbPdi.del(<number>row?.id)) {
        message.success('删除成功')
        // clear delete id
        deletingRowId.value = 0
        // 刷新列表
        refreshData()
        eventBus.emit('updateDatalogExploreBar')
    } else {
        message.error('删除失败')
    }
}
</script>

<style lang="scss" scoped>
.panel {
    padding: 0;
    margin: 15px;
    height: calc(100vh - 70px - 64px);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    overflow: auto;

    :deep(.n-data-table-td) {
        padding: 6px 6px;
    }

    :deep(.deleting td) {
        background-color: rgba(var(--warning-color), 0.1) !important;
        font-weight: bold;
    }

    :deep(.center) {
        text-align: center;
    }
}
</style>
