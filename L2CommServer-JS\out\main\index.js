import { app, native<PERSON><PERSON>, Tray, <PERSON>u, ipc<PERSON><PERSON>, Browser<PERSON>indow, shell } from "electron";
import path from "path";
import { electronApp, optimizer, is } from "@electron-toolkit/utils";
import Store from "electron-store";
import EventEmitter from "events";
import log$1 from "electron-log";
import net from "net";
import fs from "fs";
import chokidar from "chokidar";
import __cjs_mod__ from "node:module";
const __filename = import.meta.filename;
const __dirname = import.meta.dirname;
const require2 = __cjs_mod__.createRequire(import.meta.url);
class TelegramHeader {
  constructor() {
    this.telNo = 0;
    this.telLen = 0;
    this.telCounter = 0;
    this.peerIp = "";
    this.direction = "";
    this.headerType = "";
  }
  /**
   * Get header length based on telegram number
   * @param {number} telNo - Telegram number
   * @returns {number} - Header length in bytes
   */
  static getHeaderLength(telNo) {
    return telNo < 1e3 ? 20 : 8;
  }
  /**
   * Determine header type based on telegram number
   * @param {number} telNo - Telegram number
   * @returns {string} - Header type ('TelegramHeader20' or 'TelegramHeader8')
   */
  static getHeaderType(telNo) {
    return telNo < 1e3 ? "TelegramHeader20" : "TelegramHeader8";
  }
}
class TelegramHeader20 extends TelegramHeader {
  constructor() {
    super();
    this.sendId = "";
    this.recId = "";
    this.createTime = null;
    this.spare = 0;
    this.headerType = "TelegramHeader20";
  }
  static HEADER_LENGTH = 20;
}
class TelegramHeader8 extends TelegramHeader {
  constructor() {
    super();
    this.spare = 0;
    this.headerType = "TelegramHeader8";
  }
  static HEADER_LENGTH = 8;
}
const EventTypes = {
  SERVER_STARTED: "server:started",
  SERVER_STOPPED: "server:stopped",
  CONNECTION_CHANGED: "connection:changed",
  TELEGRAM_RECEIVED: "telegram:received",
  TELEGRAM_SENT: "telegram:sent"
};
class TcpSession extends EventEmitter {
  constructor(socket, server) {
    super();
    this.socket = socket;
    this.server = server;
    this.id = this.generateSessionId();
    this.peerIp = socket.remoteAddress;
    this.peerPort = socket.remotePort;
    this.connected = true;
    this.connectionTime = /* @__PURE__ */ new Date();
    this.setupSocketEvents();
    log$1.info(
      `TCP client from ${this.peerIp}:${this.peerPort} connected. Session ID: ${this.id}`
    );
  }
  generateSessionId() {
    return Math.random().toString(36).substr(2, 9);
  }
  setupSocketEvents() {
    this.socket.on("data", (data) => {
      this.onReceived(data);
    });
    this.socket.on("error", (error) => {
      this.onError(error);
    });
    this.socket.on("close", () => {
      this.onDisconnected();
    });
    this.socket.on("timeout", () => {
      log$1.warn(`TCP session ${this.id} timed out`);
      this.socket.destroy();
    });
  }
  onReceived(buffer) {
    try {
      setImmediate(() => {
        this.processReceivedData(buffer);
      });
    } catch (error) {
      log$1.error(
        `Error processing received data in session ${this.id}:`,
        error
      );
    }
  }
  async processReceivedData(buffer) {
    try {
      const { TelegramProcessor: TelegramProcessor2 } = require2("./TelegramProcessor");
      const processor = new TelegramProcessor2(this.server.pluginManager);
      processor.setSendRequestCallback((responseData) => {
        this.onSendRequest(responseData);
      });
      const processedCount = await processor.processReceived(
        buffer,
        0,
        buffer.length,
        this.peerIp
      );
      this.emit("telegramProcessed", {
        type: "received",
        count: processedCount,
        sessionId: this.id
      });
    } catch (error) {
      log$1.error(
        `Error in processReceivedData for session ${this.id}:`,
        error
      );
    }
  }
  sendAsync(data) {
    return new Promise((resolve, reject) => {
      if (!this.connected || this.socket.destroyed) {
        reject(new Error("Socket is not connected"));
        return;
      }
      this.socket.write(data, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }
  onSendRequest(telegramData) {
    this.sendAsync(telegramData).then(() => {
      this.emit("telegramProcessed", {
        type: "sent",
        count: 1,
        sessionId: this.id
      });
      log$1.debug(`Telegram sent to session ${this.id}`);
    }).catch((error) => {
      log$1.error(
        `Failed to send telegram to session ${this.id}:`,
        error
      );
    });
  }
  onError(error) {
    log$1.error(`TCP session ${this.id} error:`, error);
    this.emit("sessionError", {
      sessionId: this.id,
      error,
      peerIp: this.peerIp
    });
  }
  onDisconnected() {
    this.connected = false;
    log$1.info(`TCP client with session ID ${this.id} disconnected`);
    this.emit("sessionDisconnected", {
      sessionId: this.id,
      peerIp: this.peerIp,
      connectionTime: this.connectionTime
    });
  }
  disconnect() {
    if (this.socket && !this.socket.destroyed) {
      this.socket.destroy();
    }
  }
}
class TcpServerService extends EventEmitter {
  constructor() {
    super();
    this.server = null;
    this.sessions = /* @__PURE__ */ new Map();
    this.isRunning = false;
    this.pluginManager = null;
    this.config = {
      ip: "127.0.0.1",
      port: 1111
    };
    this.statistics = {
      connections: 0,
      telegramsReceived: 0,
      telegramsSent: 0,
      startTime: null
    };
  }
  /**
   * Set plugin manager
   * @param {PluginManager} pluginManager - Plugin manager instance
   */
  setPluginManager(pluginManager) {
    this.pluginManager = pluginManager;
  }
  async start(ip = this.config.ip, port = this.config.port) {
    if (this.isRunning) {
      throw new Error("Server is already running");
    }
    return new Promise((resolve, reject) => {
      this.server = net.createServer();
      this.server.on("connection", (socket) => {
        this.onConnection(socket);
      });
      this.server.on("error", (error) => {
        this.onError(error);
        reject(error);
      });
      this.server.on("listening", () => {
        this.isRunning = true;
        this.statistics.startTime = /* @__PURE__ */ new Date();
        this.config.ip = ip;
        this.config.port = port;
        log$1.info(`TCP server started on ${ip}:${port}`);
        this.emit("serverStarted", { ip, port });
        resolve();
      });
      this.server.listen(port, ip);
    });
  }
  async stop() {
    if (!this.isRunning) {
      return;
    }
    return new Promise((resolve) => {
      for (const session of this.sessions.values()) {
        session.disconnect();
      }
      this.sessions.clear();
      this.server.close(() => {
        this.isRunning = false;
        this.statistics.startTime = null;
        log$1.info("TCP server stopped");
        this.emit("serverStopped");
        resolve();
      });
    });
  }
  onConnection(socket) {
    const session = new TcpSession(socket, this);
    this.sessions.set(session.id, session);
    this.statistics.connections++;
    session.on("sessionDisconnected", (data) => {
      this.sessions.delete(data.sessionId);
      this.statistics.connections--;
      this.emit("connectionChanged", {
        connected: false,
        sessionId: data.sessionId,
        peerIp: data.peerIp,
        type: "P"
        // Passive connection
      });
    });
    session.on("sessionError", (data) => {
      this.emit("sessionError", data);
    });
    session.on("telegramProcessed", (data) => {
      if (data.type === "received") {
        this.statistics.telegramsReceived += data.count;
      } else if (data.type === "sent") {
        this.statistics.telegramsSent += data.count;
      }
      this.emit("telegramProcessed", data);
    });
    this.emit("connectionChanged", {
      connected: true,
      sessionId: session.id,
      peerIp: session.peerIp,
      type: "P"
      // Passive connection
    });
  }
  onError(error) {
    log$1.error("TCP server error:", error);
    this.emit("serverError", error);
  }
  getStatus() {
    return {
      isRunning: this.isRunning,
      config: this.config,
      statistics: {
        ...this.statistics,
        connections: this.sessions.size,
        uptime: this.statistics.startTime ? Math.floor(
          (Date.now() - this.statistics.startTime.getTime()) / 1e3
        ) : 0
      },
      activeSessions: Array.from(this.sessions.values()).map(
        (session) => ({
          id: session.id,
          peerIp: session.peerIp,
          peerPort: session.peerPort,
          connectionTime: session.connectionTime
        })
      )
    };
  }
  sendToSession(sessionId, data) {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.onSendRequest(data);
    } else {
      throw new Error(`Session ${sessionId} not found`);
    }
  }
  sendToAll(data) {
    for (const session of this.sessions.values()) {
      session.onSendRequest(data);
    }
  }
  getSessionCount() {
    return this.sessions.size;
  }
  getSessions() {
    return Array.from(this.sessions.values());
  }
}
class TcpClientService extends EventEmitter {
  constructor(address, port) {
    super();
    this.address = address;
    this.port = port;
    this.socket = null;
    this.isConnected = false;
    this.shouldStop = false;
    this.reconnectInterval = 5e3;
    this.reconnectTimer = null;
    this.id = this.generateClientId();
    this.connectionTime = null;
    this.autoReconnect = true;
  }
  generateClientId() {
    return `client_${Math.random().toString(36).substr(2, 9)}`;
  }
  async connect() {
    if (this.isConnected || this.shouldStop) {
      return;
    }
    return new Promise((resolve, reject) => {
      this.socket = new net.Socket();
      this.socket.on("connect", () => {
        this.onConnected();
        resolve();
      });
      this.socket.on("data", (data) => {
        this.onReceived(data);
      });
      this.socket.on("error", (error) => {
        this.onError(error);
        reject(error);
      });
      this.socket.on("close", () => {
        this.onDisconnected();
      });
      this.socket.on("timeout", () => {
        log$1.warn(`TCP client ${this.id} connection timed out`);
        this.socket.destroy();
      });
      this.socket.setTimeout(1e4);
      this.socket.connect(this.port, this.address);
    });
  }
  async disconnect() {
    this.shouldStop = true;
    this.autoReconnect = false;
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    if (this.socket && !this.socket.destroyed) {
      this.socket.destroy();
    }
    while (this.isConnected) {
      await new Promise((resolve) => setTimeout(resolve, 10));
    }
  }
  onConnected() {
    this.isConnected = true;
    this.connectionTime = /* @__PURE__ */ new Date();
    this.socket.setTimeout(0);
    log$1.info(
      `TCP client ${this.id} connected to ${this.address}:${this.port}`
    );
    this.emit("connected", {
      clientId: this.id,
      address: this.address,
      port: this.port,
      connectionTime: this.connectionTime
    });
  }
  onReceived(buffer) {
    try {
      setImmediate(() => {
        this.processReceivedData(buffer);
      });
    } catch (error) {
      log$1.error(
        `Error processing received data in client ${this.id}:`,
        error
      );
    }
  }
  async processReceivedData(buffer) {
    try {
      const { TelegramProcessor: TelegramProcessor2 } = require2("./TelegramProcessor");
      const processor = new TelegramProcessor2();
      const processedCount = await processor.processReceived(
        buffer,
        0,
        buffer.length,
        this.address
      );
      this.emit("telegramProcessed", {
        type: "received",
        count: processedCount,
        clientId: this.id
      });
    } catch (error) {
      log$1.error(
        `Error in processReceivedData for client ${this.id}:`,
        error
      );
    }
  }
  async sendAsync(data) {
    if (!this.isConnected || !this.socket || this.socket.destroyed) {
      throw new Error(`TCP client ${this.id} is not connected`);
    }
    return new Promise((resolve, reject) => {
      this.socket.write(data, (error) => {
        if (error) {
          reject(error);
        } else {
          this.emit("telegramProcessed", {
            type: "sent",
            count: 1,
            clientId: this.id
          });
          resolve();
        }
      });
    });
  }
  onSendRequest(telegramData) {
    this.sendAsync(telegramData).then(() => {
      log$1.debug(`Telegram sent from client ${this.id}`);
    }).catch((error) => {
      log$1.error(
        `Failed to send telegram from client ${this.id}:`,
        error
      );
    });
  }
  onError(error) {
    log$1.error(`TCP client ${this.id} error:`, error);
    this.emit("error", {
      clientId: this.id,
      error,
      address: this.address,
      port: this.port
    });
    if (error.code === "ECONNREFUSED" || error.code === "EHOSTUNREACH") {
      this.scheduleReconnect();
    }
  }
  onDisconnected() {
    const wasConnected = this.isConnected;
    this.isConnected = false;
    this.connectionTime = null;
    if (wasConnected) {
      log$1.info(
        `TCP client ${this.id} disconnected from ${this.address}:${this.port}`
      );
      this.emit("disconnected", {
        clientId: this.id,
        address: this.address,
        port: this.port
      });
    }
    if (!this.shouldStop && this.autoReconnect) {
      this.scheduleReconnect();
    }
  }
  scheduleReconnect() {
    if (this.reconnectTimer || this.shouldStop || !this.autoReconnect) {
      return;
    }
    log$1.info(
      `TCP client ${this.id} will attempt reconnection in ${this.reconnectInterval}ms`
    );
    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null;
      if (!this.shouldStop && !this.isConnected) {
        this.connect().catch((error) => {
          log$1.error(
            `TCP client ${this.id} reconnection failed:`,
            error
          );
        });
      }
    }, this.reconnectInterval);
  }
  getStatus() {
    return {
      id: this.id,
      address: this.address,
      port: this.port,
      isConnected: this.isConnected,
      connectionTime: this.connectionTime,
      autoReconnect: this.autoReconnect
    };
  }
  setAutoReconnect(enabled) {
    this.autoReconnect = enabled;
    if (!enabled && this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }
  setReconnectInterval(interval) {
    this.reconnectInterval = interval;
  }
}
class TcpClientManager extends EventEmitter {
  constructor() {
    super();
    this.clients = /* @__PURE__ */ new Map();
    this.statistics = {
      totalConnections: 0,
      activeConnections: 0,
      telegramsReceived: 0,
      telegramsSent: 0
    };
  }
  addClient(address, port, autoConnect = true) {
    const clientId = `${address}:${port}`;
    if (this.clients.has(clientId)) {
      throw new Error(`Client for ${address}:${port} already exists`);
    }
    const client = new TcpClientService(address, port);
    client.on("connected", (data) => {
      this.statistics.activeConnections++;
      this.emit("clientConnected", data);
    });
    client.on("disconnected", (data) => {
      this.statistics.activeConnections--;
      this.emit("clientDisconnected", data);
    });
    client.on("error", (data) => {
      this.emit("clientError", data);
    });
    client.on("telegramProcessed", (data) => {
      if (data.type === "received") {
        this.statistics.telegramsReceived += data.count;
      } else if (data.type === "sent") {
        this.statistics.telegramsSent += data.count;
      }
      this.emit("telegramProcessed", data);
    });
    this.clients.set(clientId, client);
    this.statistics.totalConnections++;
    if (autoConnect) {
      client.connect().catch((error) => {
        log$1.error(`Failed to connect client ${clientId}:`, error);
      });
    }
    return client;
  }
  removeClient(address, port) {
    const clientId = `${address}:${port}`;
    const client = this.clients.get(clientId);
    if (client) {
      client.disconnect();
      this.clients.delete(clientId);
      this.statistics.totalConnections--;
    }
  }
  getClient(address, port) {
    const clientId = `${address}:${port}`;
    return this.clients.get(clientId);
  }
  getAllClients() {
    return Array.from(this.clients.values());
  }
  async disconnectAll() {
    const disconnectPromises = Array.from(this.clients.values()).map(
      (client) => client.disconnect()
    );
    await Promise.all(disconnectPromises);
    this.clients.clear();
    this.statistics.totalConnections = 0;
    this.statistics.activeConnections = 0;
  }
  getStatistics() {
    return {
      ...this.statistics,
      activeConnections: Array.from(this.clients.values()).filter(
        (client) => client.isConnected
      ).length
    };
  }
  sendToClient(address, port, data) {
    const client = this.getClient(address, port);
    if (client && client.isConnected) {
      return client.sendAsync(data);
    } else {
      throw new Error(`Client ${address}:${port} is not connected`);
    }
  }
  sendToAllClients(data) {
    const sendPromises = Array.from(this.clients.values()).filter((client) => client.isConnected).map((client) => client.sendAsync(data));
    return Promise.allSettled(sendPromises);
  }
}
class PluginManager extends EventEmitter {
  constructor() {
    super();
    this.plugins = /* @__PURE__ */ new Map();
    this.pluginFiles = /* @__PURE__ */ new Map();
    this.watchers = /* @__PURE__ */ new Map();
    this.pluginDirectories = [];
    this.isWatching = false;
    this.defaultPluginDir = path.resolve(
      __dirname,
      "../../../../TelegramProcessor-JS/plugins"
    );
  }
  /**
   * Initialize plugin manager and start watching for changes
   * @param {Array} additionalDirs - Additional plugin directories to watch
   */
  async initialize(additionalDirs = []) {
    try {
      this.pluginDirectories = [this.defaultPluginDir, ...additionalDirs];
      await this.ensureDirectoriesExist();
      await this.loadAllPlugins();
      this.startWatching();
      log$1.info("Plugin Manager initialized successfully");
      this.emit("initialized");
    } catch (error) {
      log$1.error("Failed to initialize Plugin Manager:", error);
      throw error;
    }
  }
  /**
   * Ensure plugin directories exist
   */
  async ensureDirectoriesExist() {
    for (const dir of this.pluginDirectories) {
      try {
        await fs.promises.mkdir(dir, { recursive: true });
      } catch (error) {
        log$1.warn(`Failed to create plugin directory ${dir}:`, error);
      }
    }
  }
  /**
   * Load all plugins from all directories
   */
  async loadAllPlugins() {
    for (const dir of this.pluginDirectories) {
      await this.loadPluginsFromDirectory(dir);
    }
    log$1.info(`Loaded ${this.plugins.size} plugins`);
  }
  /**
   * Load plugins from a specific directory
   * @param {string} directory - Plugin directory path
   */
  async loadPluginsFromDirectory(directory) {
    try {
      if (fs.existsSync(directory)) {
        const files = await fs.promises.readdir(directory);
        const jsFiles = files.filter((file) => file.endsWith(".js"));
        for (const file of jsFiles) {
          const filePath = path.join(directory, file);
          await this.loadPlugin(filePath);
        }
      }
    } catch (error) {
      log$1.error(
        `Failed to load plugins from directory ${directory}:`,
        error
      );
    }
  }
  /**
   * Load a single plugin file
   * @param {string} filePath - Plugin file path
   */
  async loadPlugin(filePath) {
    try {
      delete require2.cache[require2.resolve(filePath)];
      const pluginModule = require2(filePath);
      if (!pluginModule || !pluginModule.PluginClass) {
        log$1.warn(`Plugin ${filePath} does not export PluginClass`);
        return null;
      }
      const pluginInstance = new pluginModule.PluginClass();
      const validationErrors = pluginInstance.validate();
      if (validationErrors.length > 0) {
        log$1.error(
          `Plugin ${filePath} validation failed:`,
          validationErrors
        );
        return null;
      }
      const telegramNo = pluginInstance.telegramNo;
      const existingPlugin = this.plugins.get(telegramNo);
      if (existingPlugin) {
        log$1.warn(
          `Plugin conflict: Telegram ${telegramNo} already handled by ${existingPlugin.pluginName}`
        );
      }
      this.plugins.set(telegramNo, pluginInstance);
      const stats = await fs.promises.stat(filePath);
      this.pluginFiles.set(filePath, {
        telegramNo,
        lastModified: stats.mtime,
        pluginName: pluginInstance.pluginName
      });
      const pluginType = pluginInstance.getPluginType();
      log$1.info(
        `Loaded ${pluginType} plugin: ${pluginInstance.pluginName} (Telegram ${telegramNo})`
      );
      this.emit("pluginLoaded", {
        type: pluginType,
        telegramNo,
        pluginName: pluginInstance.pluginName,
        filePath
      });
      return pluginInstance;
    } catch (error) {
      log$1.error(`Failed to load plugin ${filePath}:`, error);
      this.emit("pluginError", {
        filePath,
        error: error.message
      });
      return null;
    }
  }
  /**
   * Start watching plugin directories for changes
   */
  startWatching() {
    if (this.isWatching) {
      return;
    }
    for (const dir of this.pluginDirectories) {
      if (fs.existsSync(dir)) {
        const watcher = chokidar.watch(path.join(dir, "*.js"), {
          ignored: /node_modules/,
          persistent: true,
          ignoreInitial: true
        });
        watcher.on("change", (filePath) => {
          this.handleFileChange(filePath);
        });
        watcher.on("add", (filePath) => {
          this.handleFileAdd(filePath);
        });
        watcher.on("unlink", (filePath) => {
          this.handleFileRemove(filePath);
        });
        this.watchers.set(dir, watcher);
        log$1.info(`Started watching plugin directory: ${dir}`);
      }
    }
    this.isWatching = true;
  }
  /**
   * Stop watching plugin directories
   */
  stopWatching() {
    for (const [dir, watcher] of this.watchers) {
      watcher.close();
      log$1.info(`Stopped watching plugin directory: ${dir}`);
    }
    this.watchers.clear();
    this.isWatching = false;
  }
  /**
   * Handle file change event
   * @param {string} filePath - Changed file path
   */
  async handleFileChange(filePath) {
    log$1.info(`Plugin file changed: ${filePath}`);
    const fileInfo = this.pluginFiles.get(filePath);
    if (fileInfo) {
      this.plugins.delete(fileInfo.telegramNo);
    }
    await this.loadPlugin(filePath);
    this.emit("pluginReloaded", { filePath });
  }
  /**
   * Handle file add event
   * @param {string} filePath - Added file path
   */
  async handleFileAdd(filePath) {
    log$1.info(`New plugin file added: ${filePath}`);
    await this.loadPlugin(filePath);
  }
  /**
   * Handle file remove event
   * @param {string} filePath - Removed file path
   */
  handleFileRemove(filePath) {
    log$1.info(`Plugin file removed: ${filePath}`);
    const fileInfo = this.pluginFiles.get(filePath);
    if (fileInfo) {
      this.plugins.delete(fileInfo.telegramNo);
      this.pluginFiles.delete(filePath);
      this.emit("pluginUnloaded", {
        filePath,
        telegramNo: fileInfo.telegramNo
      });
    }
  }
  /**
   * Get plugin for telegram number
   * @param {number} telegramNo - Telegram number
   * @returns {Object|null} - Plugin instance or null
   */
  getPlugin(telegramNo) {
    return this.plugins.get(telegramNo) || null;
  }
  /**
   * Get receive plugin for telegram number (backward compatibility)
   * @param {number} telegramNo - Telegram number
   * @returns {Object|null} - Plugin instance or null
   */
  getReceivePlugin(telegramNo) {
    const plugin = this.plugins.get(telegramNo);
    if (plugin && (plugin.getPluginType() === "receive" || plugin.getPluginType() === "both")) {
      return plugin;
    }
    return null;
  }
  /**
   * Get send plugin for telegram number (backward compatibility)
   * @param {number} telegramNo - Telegram number
   * @returns {Object|null} - Plugin instance or null
   */
  getSendPlugin(telegramNo) {
    const plugin = this.plugins.get(telegramNo);
    if (plugin && (plugin.getPluginType() === "send" || plugin.getPluginType() === "both")) {
      return plugin;
    }
    return null;
  }
  /**
   * Get all loaded plugins
   * @returns {Object} - Object with plugin lists organized by type
   */
  getAllPlugins() {
    const allPlugins = Array.from(this.plugins.entries()).map(
      ([telNo, plugin]) => ({
        telegramNo: telNo,
        pluginName: plugin.pluginName,
        version: plugin.version,
        description: plugin.description,
        author: plugin.author,
        type: plugin.getPluginType()
      })
    );
    const receiveList = allPlugins.filter(
      (p) => p.type === "receive" || p.type === "both"
    );
    const sendList = allPlugins.filter(
      (p) => p.type === "send" || p.type === "both"
    );
    return {
      all: allPlugins,
      receive: receiveList,
      send: sendList,
      total: allPlugins.length
    };
  }
  /**
   * Reload all plugins
   */
  async reloadAllPlugins() {
    log$1.info("Reloading all plugins...");
    this.plugins.clear();
    this.pluginFiles.clear();
    await this.loadAllPlugins();
    this.emit("allPluginsReloaded");
    log$1.info("All plugins reloaded successfully");
  }
  /**
   * Get plugin statistics
   * @returns {Object} - Plugin statistics
   */
  getStatistics() {
    const allPlugins = this.getAllPlugins();
    return {
      totalPlugins: this.plugins.size,
      receivePlugins: allPlugins.receive.length,
      sendPlugins: allPlugins.send.length,
      bothPlugins: allPlugins.all.filter((p) => p.type === "both").length,
      watchedDirectories: this.pluginDirectories.length,
      isWatching: this.isWatching
    };
  }
  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopWatching();
    this.plugins.clear();
    this.pluginFiles.clear();
    this.removeAllListeners();
  }
}
class LoggingService extends EventEmitter {
  constructor() {
    super();
    this.mainWindow = null;
    this.logLevel = "info";
    this.logBuffer = [];
    this.maxBufferSize = 1e3;
    this.initializeElectronLog();
  }
  /**
   * Initialize electron-log configuration
   */
  initializeElectronLog() {
    log$1.initialize();
    log$1.transports.file.resolvePathFn = () => {
      return path.join(app.getAppPath(), "l2commserver.log");
    };
    log$1.transports.ipc.level = this.logLevel;
    log$1.transports.file.level = "debug";
    log$1.transports.console.level = this.logLevel;
    this.overrideConsoleMethods();
  }
  /**
   * Set the main window for IPC communication
   * @param {BrowserWindow} mainWindow - Main window instance
   */
  setMainWindow(mainWindow2) {
    this.mainWindow = mainWindow2;
    if (this.logBuffer.length > 0) {
      this.logBuffer.forEach((entry) => {
        this.sendLogToUI(entry.level, entry.args);
      });
      this.logBuffer = [];
    }
  }
  /**
   * Override console methods to capture all console output
   */
  overrideConsoleMethods() {
    const originalConsole = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug
    };
    console.log = (...args) => {
      originalConsole.log(...args);
      this.log("info", ...args);
    };
    console.info = (...args) => {
      originalConsole.info(...args);
      this.log("info", ...args);
    };
    console.warn = (...args) => {
      originalConsole.warn(...args);
      this.log("warn", ...args);
    };
    console.error = (...args) => {
      originalConsole.error(...args);
      this.log("error", ...args);
    };
    console.debug = (...args) => {
      originalConsole.debug(...args);
      this.log("debug", ...args);
    };
  }
  /**
   * Log a message
   * @param {string} level - Log level (error, warn, info, verbose, debug)
   * @param {...any} args - Log arguments
   */
  log(level, ...args) {
    const validLevels = ["error", "warn", "info", "verbose", "debug"];
    if (!validLevels.includes(level)) {
      level = "info";
    }
    if (log$1[level] && typeof log$1[level] === "function") {
      log$1[level](...args);
    } else {
      log$1.info(...args);
    }
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.sendLogToUI(level, args);
    } else {
      this.bufferLog(level, args);
    }
    this.emit("log", { level, args, timestamp: /* @__PURE__ */ new Date() });
  }
  /**
   * Send log to UI via IPC
   * @param {string} level - Log level
   * @param {Array} args - Log arguments
   */
  sendLogToUI(level, args) {
    try {
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send("__ELECTRON_LOG_IPC__", {
          level,
          data: args
        });
      }
    } catch (error) {
      console.error("Failed to send log to UI:", error);
    }
  }
  /**
   * Buffer log entry when UI is not ready
   * @param {string} level - Log level
   * @param {Array} args - Log arguments
   */
  bufferLog(level, args) {
    this.logBuffer.push({ level, args, timestamp: /* @__PURE__ */ new Date() });
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer.shift();
    }
  }
  /**
   * Set log level
   * @param {string} level - New log level
   */
  setLogLevel(level) {
    const validLevels = ["error", "warn", "info", "verbose", "debug"];
    if (validLevels.includes(level)) {
      this.logLevel = level;
      log$1.transports.console.level = level;
      log$1.transports.ipc.level = level;
      this.log("info", `Log level changed to: ${level}`);
    } else {
      this.log("warn", `Invalid log level: ${level}`);
    }
  }
  /**
   * Get current log level
   * @returns {string} Current log level
   */
  getLogLevel() {
    return this.logLevel;
  }
  /**
   * Clear log buffer
   */
  clearBuffer() {
    this.logBuffer = [];
    this.log("info", "Log buffer cleared");
  }
  /**
   * Get buffered logs
   * @returns {Array} Buffered log entries
   */
  getBufferedLogs() {
    return [...this.logBuffer];
  }
  // Convenience methods for different log levels
  /**
   * Log error message
   * @param {...any} args - Log arguments
   */
  error(...args) {
    this.log("error", ...args);
  }
  /**
   * Log warning message
   * @param {...any} args - Log arguments
   */
  warn(...args) {
    this.log("warn", ...args);
  }
  /**
   * Log info message
   * @param {...any} args - Log arguments
   */
  info(...args) {
    this.log("info", ...args);
  }
  /**
   * Log verbose message
   * @param {...any} args - Log arguments
   */
  verbose(...args) {
    this.log("verbose", ...args);
  }
  /**
   * Log debug message
   * @param {...any} args - Log arguments
   */
  debug(...args) {
    this.log("debug", ...args);
  }
  /**
   * Create a logger instance for a specific component
   * @param {string} component - Component name
   * @returns {Object} Logger instance
   */
  createLogger(component) {
    return {
      error: (...args) => this.error(`[${component}]`, ...args),
      warn: (...args) => this.warn(`[${component}]`, ...args),
      info: (...args) => this.info(`[${component}]`, ...args),
      verbose: (...args) => this.verbose(`[${component}]`, ...args),
      debug: (...args) => this.debug(`[${component}]`, ...args),
      log: (level, ...args) => this.log(level, `[${component}]`, ...args)
    };
  }
  /**
   * Cleanup resources
   */
  cleanup() {
    this.removeAllListeners();
    this.logBuffer = [];
    this.mainWindow = null;
  }
}
const loggingService = new LoggingService();
class S7DataTypes {
  /**
   * Convert buffer to 32-bit float (Real) - S7 big-endian format
   * @param {Buffer} buffer - 4-byte buffer
   * @returns {number} - Float value
   */
  static bufferToReal(buffer) {
    if (buffer.length < 4) {
      throw new Error("Buffer too short for Real (requires 4 bytes)");
    }
    return buffer.readFloatBE(0);
  }
  /**
   * Convert 32-bit float to buffer - S7 big-endian format
   * @param {number} value - Float value
   * @returns {Buffer} - 4-byte buffer
   */
  static realToBuffer(value) {
    const buffer = Buffer.alloc(4);
    buffer.writeFloatBE(value, 0);
    return buffer;
  }
  /**
   * Convert buffer to 64-bit double (LReal) - S7 big-endian format
   * @param {Buffer} buffer - 8-byte buffer
   * @returns {number} - Double value
   */
  static bufferToLReal(buffer) {
    if (buffer.length < 8) {
      throw new Error("Buffer too short for LReal (requires 8 bytes)");
    }
    return buffer.readDoubleBE(0);
  }
  /**
   * Convert 64-bit double to buffer - S7 big-endian format
   * @param {number} value - Double value
   * @returns {Buffer} - 8-byte buffer
   */
  static lRealToBuffer(value) {
    const buffer = Buffer.alloc(8);
    buffer.writeDoubleBE(value, 0);
    return buffer;
  }
  /**
   * Convert buffer to 16-bit signed integer (Int) - S7 big-endian format
   * @param {Buffer} buffer - 2-byte buffer
   * @returns {number} - Integer value
   */
  static bufferToInt(buffer) {
    if (buffer.length < 2) {
      throw new Error("Buffer too short for Int (requires 2 bytes)");
    }
    return buffer.readInt16BE(0);
  }
  /**
   * Convert 16-bit signed integer to buffer - S7 big-endian format
   * @param {number} value - Integer value
   * @returns {Buffer} - 2-byte buffer
   */
  static intToBuffer(value) {
    const buffer = Buffer.alloc(2);
    buffer.writeInt16BE(value, 0);
    return buffer;
  }
  /**
   * Convert buffer to 32-bit signed integer (DInt) - S7 big-endian format
   * @param {Buffer} buffer - 4-byte buffer
   * @returns {number} - Integer value
   */
  static bufferToDInt(buffer) {
    if (buffer.length < 4) {
      throw new Error("Buffer too short for DInt (requires 4 bytes)");
    }
    return buffer.readInt32BE(0);
  }
  /**
   * Convert 32-bit signed integer to buffer - S7 big-endian format
   * @param {number} value - Integer value
   * @returns {Buffer} - 4-byte buffer
   */
  static dIntToBuffer(value) {
    const buffer = Buffer.alloc(4);
    buffer.writeInt32BE(value, 0);
    return buffer;
  }
  /**
   * Convert buffer to 16-bit unsigned integer (Word) - S7 big-endian format
   * @param {Buffer} buffer - 2-byte buffer
   * @returns {number} - Unsigned integer value
   */
  static bufferToWord(buffer) {
    if (buffer.length < 2) {
      throw new Error("Buffer too short for Word (requires 2 bytes)");
    }
    return buffer.readUInt16BE(0);
  }
  /**
   * Convert 16-bit unsigned integer to buffer - S7 big-endian format
   * @param {number} value - Unsigned integer value
   * @returns {Buffer} - 2-byte buffer
   */
  static wordToBuffer(value) {
    const buffer = Buffer.alloc(2);
    buffer.writeUInt16BE(value, 0);
    return buffer;
  }
  /**
   * Convert buffer to 32-bit unsigned integer (DWord) - S7 big-endian format
   * @param {Buffer} buffer - 4-byte buffer
   * @returns {number} - Unsigned integer value
   */
  static bufferToDWord(buffer) {
    if (buffer.length < 4) {
      throw new Error("Buffer too short for DWord (requires 4 bytes)");
    }
    return buffer.readUInt32BE(0);
  }
  /**
   * Convert 32-bit unsigned integer to buffer - S7 big-endian format
   * @param {number} value - Unsigned integer value
   * @returns {Buffer} - 4-byte buffer
   */
  static dWordToBuffer(value) {
    const buffer = Buffer.alloc(4);
    buffer.writeUInt32BE(value, 0);
    return buffer;
  }
  /**
   * Convert buffer to boolean (single bit)
   * @param {Buffer} buffer - 1-byte buffer
   * @param {number} bit - Bit position (0-7)
   * @returns {boolean} - Boolean value
   */
  static bufferToBool(buffer, bit = 0) {
    if (buffer.length < 1) {
      throw new Error("Buffer too short for Bool (requires 1 byte)");
    }
    if (bit < 0 || bit > 7) {
      throw new Error("Bit position must be between 0 and 7");
    }
    return (buffer[0] & 1 << bit) !== 0;
  }
  /**
   * Convert boolean to buffer (single bit)
   * @param {boolean} value - Boolean value
   * @param {number} bit - Bit position (0-7)
   * @returns {Buffer} - 1-byte buffer
   */
  static boolToBuffer(value, bit = 0) {
    if (bit < 0 || bit > 7) {
      throw new Error("Bit position must be between 0 and 7");
    }
    const buffer = Buffer.alloc(1);
    if (value) {
      buffer[0] |= 1 << bit;
    }
    return buffer;
  }
  /**
   * Convert buffer to byte (8-bit unsigned integer)
   * @param {Buffer} buffer - 1-byte buffer
   * @returns {number} - Byte value (0-255)
   */
  static bufferToByte(buffer) {
    if (buffer.length < 1) {
      throw new Error("Buffer too short for Byte (requires 1 byte)");
    }
    return buffer.readUInt8(0);
  }
  /**
   * Convert byte to buffer
   * @param {number} value - Byte value (0-255)
   * @returns {Buffer} - 1-byte buffer
   */
  static byteToBuffer(value) {
    if (value < 0 || value > 255) {
      throw new Error("Byte value must be between 0 and 255");
    }
    const buffer = Buffer.alloc(1);
    buffer.writeUInt8(value, 0);
    return buffer;
  }
  /**
   * Convert buffer to character
   * @param {Buffer} buffer - 1-byte buffer
   * @returns {string} - Character
   */
  static bufferToChar(buffer) {
    if (buffer.length < 1) {
      throw new Error("Buffer too short for Char (requires 1 byte)");
    }
    return String.fromCharCode(buffer.readUInt8(0));
  }
  /**
   * Convert character to buffer
   * @param {string} value - Character
   * @returns {Buffer} - 1-byte buffer
   */
  static charToBuffer(value) {
    if (typeof value !== "string" || value.length !== 1) {
      throw new Error("Value must be a single character");
    }
    const buffer = Buffer.alloc(1);
    buffer.writeUInt8(value.charCodeAt(0), 0);
    return buffer;
  }
  /**
   * Convert buffer to S7 string
   * S7 string format: [max_length][actual_length][string_data...]
   * @param {Buffer} buffer - Buffer containing S7 string
   * @returns {string} - Decoded string
   */
  static bufferToS7String(buffer) {
    try {
      if (buffer.length < 2) {
        throw new Error(
          "Buffer too short for S7 string (minimum 2 bytes)"
        );
      }
      const maxLength = buffer[0];
      const actualLength = buffer[1];
      if (actualLength > maxLength) {
        throw new Error(
          `Invalid S7 string: actual length (${actualLength}) > max length (${maxLength})`
        );
      }
      if (buffer.length < 2 + actualLength) {
        throw new Error(
          `Buffer too short for S7 string data (expected ${2 + actualLength}, got ${buffer.length})`
        );
      }
      const stringData = buffer.subarray(2, 2 + actualLength);
      return stringData.toString("ascii");
    } catch (error) {
      console.error("Error decoding S7 string:", error);
      return "";
    }
  }
  /**
   * Convert string to S7 string buffer
   * @param {string} value - String value
   * @param {number} maxLength - Maximum string length (default: 254)
   * @returns {Buffer} - S7 string buffer
   */
  static s7StringToBuffer(value, maxLength = 254) {
    try {
      const stringValue = value || "";
      const actualLength = Math.min(stringValue.length, maxLength);
      const buffer = Buffer.alloc(2 + maxLength);
      buffer[0] = maxLength;
      buffer[1] = actualLength;
      if (actualLength > 0) {
        buffer.write(
          stringValue.substring(0, actualLength),
          2,
          actualLength,
          "ascii"
        );
      }
      if (actualLength < maxLength) {
        buffer.fill(0, 2 + actualLength, 2 + maxLength);
      }
      return buffer;
    } catch (error) {
      console.error("Error encoding S7 string:", error);
      const buffer = Buffer.alloc(2 + maxLength);
      buffer[0] = maxLength;
      buffer[1] = 0;
      return buffer;
    }
  }
  /**
   * Convert buffer to fixed-length string (null-terminated)
   * @param {Buffer} buffer - Buffer containing string data
   * @param {string} encoding - String encoding (default: 'ascii')
   * @returns {string} - Decoded string
   */
  static bufferToFixedString(buffer, encoding = "ascii") {
    try {
      let length = buffer.length;
      for (let i = 0; i < buffer.length; i++) {
        if (buffer[i] === 0) {
          length = i;
          break;
        }
      }
      return buffer.subarray(0, length).toString(encoding);
    } catch (error) {
      console.error("Error decoding fixed string:", error);
      return "";
    }
  }
  /**
   * Convert string to fixed-length buffer (null-terminated)
   * @param {string} value - String value
   * @param {number} length - Fixed buffer length
   * @param {string} encoding - String encoding (default: 'ascii')
   * @returns {Buffer} - Fixed-length string buffer
   */
  static fixedStringToBuffer(value, length, encoding = "ascii") {
    try {
      const buffer = Buffer.alloc(length);
      const stringValue = value || "";
      const maxStringLength = length - 1;
      if (stringValue.length > 0) {
        const truncatedValue = stringValue.substring(
          0,
          maxStringLength
        );
        buffer.write(
          truncatedValue,
          0,
          truncatedValue.length,
          encoding
        );
      }
      return buffer;
    } catch (error) {
      console.error("Error encoding fixed string:", error);
      return Buffer.alloc(length);
    }
  }
  /**
   * Convert buffer to byte array (for debugging)
   * @param {Buffer} buffer - Buffer to convert
   * @returns {Array} - Array of byte values
   */
  static bufferToByteArray(buffer) {
    return Array.from(buffer);
  }
  /**
   * Convert byte array to buffer
   * @param {Array} byteArray - Array of byte values
   * @returns {Buffer} - Buffer
   */
  static byteArrayToBuffer(byteArray) {
    return Buffer.from(byteArray);
  }
  /**
   * Get hex string representation of buffer
   * @param {Buffer} buffer - Buffer to convert
   * @param {string} separator - Separator between bytes (default: ' ')
   * @returns {string} - Hex string
   */
  static bufferToHexString(buffer, separator = " ") {
    return Array.from(buffer).map((byte) => byte.toString(16).padStart(2, "0").toUpperCase()).join(separator);
  }
  /**
   * Convert buffer to Time (S7 Time format - 32-bit milliseconds)
   * @param {Buffer} buffer - 4-byte buffer
   * @returns {number} - Time in milliseconds
   */
  static bufferToTime(buffer) {
    if (buffer.length < 4) {
      throw new Error("Buffer too short for Time (requires 4 bytes)");
    }
    return buffer.readUInt32BE(0);
  }
  /**
   * Convert time to buffer (S7 Time format)
   * @param {number} milliseconds - Time in milliseconds
   * @returns {Buffer} - 4-byte buffer
   */
  static timeToBuffer(milliseconds) {
    const buffer = Buffer.alloc(4);
    buffer.writeUInt32BE(milliseconds, 0);
    return buffer;
  }
  /**
   * Convert buffer to Date (S7 Date format)
   * @param {Buffer} buffer - 2-byte buffer (days since 1990-01-01)
   * @returns {Date} - Date object
   */
  static bufferToDate(buffer) {
    if (buffer.length < 2) {
      throw new Error("Buffer too short for Date (requires 2 bytes)");
    }
    const days = buffer.readUInt16BE(0);
    const baseDate = /* @__PURE__ */ new Date("1990-01-01");
    return new Date(baseDate.getTime() + days * 24 * 60 * 60 * 1e3);
  }
  /**
   * Convert date to buffer (S7 Date format)
   * @param {Date} date - Date object
   * @returns {Buffer} - 2-byte buffer
   */
  static dateToBuffer(date) {
    const baseDate = /* @__PURE__ */ new Date("1990-01-01");
    const diffTime = date.getTime() - baseDate.getTime();
    const days = Math.floor(diffTime / (24 * 60 * 60 * 1e3));
    const buffer = Buffer.alloc(2);
    buffer.writeUInt16BE(days, 0);
    return buffer;
  }
  /**
   * Convert BCD byte to decimal
   * @param {number} n - BCD byte
   * @returns {number} - Decimal value
   */
  static fromBCD(n) {
    return (n >> 4) * 10 + (n & 15);
  }
  /**
   * Convert decimal to BCD byte
   * @param {number} n - Decimal value
   * @returns {number} - BCD byte
   */
  static toBCD(n) {
    return n / 10 << 4 | n % 10;
  }
  /**
   * Convert buffer to S7 DateTime (8 bytes)
   * S7 DateTime format: 8 bytes in BCD format
   * Byte 0: Year (2 digits BCD, 00-99)
   * Byte 1: Month (BCD, 01-12)
   * Byte 2: Day (BCD, 01-31)
   * Byte 3: Hour (BCD, 00-23)
   * Byte 4: Minute (BCD, 00-59)
   * Byte 5: Second (BCD, 00-59)
   * Byte 6: Milliseconds tens (BCD, 00-99)
   * Byte 7: Milliseconds units + weekday (BCD)
   * @param {Buffer} buffer - 8-byte buffer
   * @param {boolean} isUTC - Whether to interpret as UTC (default: false)
   * @returns {Date} - Date object
   */
  static bufferToDateTime(buffer, isUTC = false) {
    if (buffer.length < 8) {
      throw new Error("Buffer too short for DateTime (requires 8 bytes)");
    }
    try {
      const year = this.fromBCD(buffer.readUInt8(0));
      const month = this.fromBCD(buffer.readUInt8(1));
      const day = this.fromBCD(buffer.readUInt8(2));
      const hour = this.fromBCD(buffer.readUInt8(3));
      const min = this.fromBCD(buffer.readUInt8(4));
      const sec = this.fromBCD(buffer.readUInt8(5));
      const ms_1 = this.fromBCD(buffer.readUInt8(6));
      const ms_2 = this.fromBCD(buffer.readUInt8(7) & 240);
      let date;
      if (isUTC) {
        date = new Date(
          Date.UTC(
            (year > 89 ? 1900 : 2e3) + year,
            month - 1,
            day,
            hour,
            min,
            sec,
            ms_1 * 10 + ms_2 / 10
          )
        );
      } else {
        date = new Date(
          (year > 89 ? 1900 : 2e3) + year,
          month - 1,
          day,
          hour,
          min,
          sec,
          ms_1 * 10 + ms_2 / 10
        );
      }
      return date;
    } catch (error) {
      console.error("Error parsing S7 DateTime:", error);
      return /* @__PURE__ */ new Date();
    }
  }
  /**
   * Convert Date to S7 DateTime buffer (8 bytes)
   * S7 DateTime format: 8 bytes in BCD format
   * @param {Date} date - Date object
   * @param {boolean} isUTC - Whether to encode as UTC (default: false)
   * @returns {Buffer} - 8-byte buffer
   */
  static dateTimeToBuffer(date, isUTC = false) {
    try {
      if (!(date instanceof Date)) {
        if (date > 631152e6 && date < 3786911999999) {
          date = new Date(date);
        } else {
          console.error("Unsupported value for S7 DateTime:", date);
          date = /* @__PURE__ */ new Date();
        }
      }
      const buffer = Buffer.alloc(8);
      if (isUTC) {
        buffer.writeUInt8(this.toBCD(date.getUTCFullYear() % 100), 0);
        buffer.writeUInt8(this.toBCD(date.getUTCMonth() + 1), 1);
        buffer.writeUInt8(this.toBCD(date.getUTCDate()), 2);
        buffer.writeUInt8(this.toBCD(date.getUTCHours()), 3);
        buffer.writeUInt8(this.toBCD(date.getUTCMinutes()), 4);
        buffer.writeUInt8(this.toBCD(date.getUTCSeconds()), 5);
        buffer.writeUInt8(
          this.toBCD(date.getUTCMilliseconds() / 10 >> 0),
          6
        );
        buffer.writeUInt8(
          this.toBCD(
            date.getUTCMilliseconds() % 10 * 10 + (date.getUTCDay() + 1)
          ),
          7
        );
      } else {
        buffer.writeUInt8(this.toBCD(date.getFullYear() % 100), 0);
        buffer.writeUInt8(this.toBCD(date.getMonth() + 1), 1);
        buffer.writeUInt8(this.toBCD(date.getDate()), 2);
        buffer.writeUInt8(this.toBCD(date.getHours()), 3);
        buffer.writeUInt8(this.toBCD(date.getMinutes()), 4);
        buffer.writeUInt8(this.toBCD(date.getSeconds()), 5);
        buffer.writeUInt8(
          this.toBCD(date.getMilliseconds() / 10 >> 0),
          6
        );
        buffer.writeUInt8(
          this.toBCD(
            date.getMilliseconds() % 10 * 10 + (date.getDay() + 1)
          ),
          7
        );
      }
      return buffer;
    } catch (error) {
      console.error("Error encoding S7 DateTime:", error);
      const buffer = Buffer.alloc(8);
      buffer.fill(0);
      return buffer;
    }
  }
  /**
   * Convert buffer to S7 DateTime Long (DTL) - 12 bytes
   * DTL format: Year(2) Month(1) Day(1) Weekday(1) Hour(1) Minute(1) Second(1) Nanoseconds(4)
   * @param {Buffer} buffer - 12-byte buffer
   * @param {boolean} isUTC - Whether to interpret as UTC (default: false)
   * @returns {Date} - Date object
   */
  static bufferToDateTimeLong(buffer, isUTC = false) {
    if (buffer.length < 12) {
      throw new Error(
        "Buffer too short for DateTime Long (requires 12 bytes)"
      );
    }
    try {
      const year = buffer.readUInt16BE(0);
      const month = buffer.readUInt8(2);
      const day = buffer.readUInt8(3);
      const hour = buffer.readUInt8(5);
      const min = buffer.readUInt8(6);
      const sec = buffer.readUInt8(7);
      const ns = buffer.readUInt32BE(8);
      let date;
      if (isUTC) {
        date = new Date(
          Date.UTC(year, month - 1, day, hour, min, sec, ns / 1e6)
        );
      } else {
        date = new Date(year, month - 1, day, hour, min, sec, ns / 1e6);
      }
      return date;
    } catch (error) {
      console.error("Error parsing S7 DateTime Long:", error);
      return /* @__PURE__ */ new Date();
    }
  }
  /**
   * Convert Date to S7 DateTime Long buffer (DTL) - 12 bytes
   * @param {Date} date - Date object
   * @param {boolean} isUTC - Whether to encode as UTC (default: false)
   * @returns {Buffer} - 12-byte buffer
   */
  static dateTimeLongToBuffer(date, isUTC = false) {
    try {
      if (!(date instanceof Date)) {
        if (date >= 0 && date < 9223382836854) {
          date = new Date(date);
        } else {
          console.error(
            "Unsupported value for S7 DateTime Long:",
            date
          );
          date = /* @__PURE__ */ new Date();
        }
      }
      const buffer = Buffer.alloc(12);
      if (isUTC) {
        buffer.writeUInt16BE(date.getUTCFullYear(), 0);
        buffer.writeUInt8(date.getUTCMonth() + 1, 2);
        buffer.writeUInt8(date.getUTCDate(), 3);
        buffer.writeUInt8(date.getUTCDay() + 1, 4);
        buffer.writeUInt8(date.getUTCHours(), 5);
        buffer.writeUInt8(date.getUTCMinutes(), 6);
        buffer.writeUInt8(date.getUTCSeconds(), 7);
        buffer.writeUInt32BE(date.getUTCMilliseconds() * 1e6, 8);
      } else {
        buffer.writeUInt16BE(date.getFullYear(), 0);
        buffer.writeUInt8(date.getMonth() + 1, 2);
        buffer.writeUInt8(date.getDate(), 3);
        buffer.writeUInt8(date.getDay() + 1, 4);
        buffer.writeUInt8(date.getHours(), 5);
        buffer.writeUInt8(date.getMinutes(), 6);
        buffer.writeUInt8(date.getSeconds(), 7);
        buffer.writeUInt32BE(date.getMilliseconds() * 1e6, 8);
      }
      return buffer;
    } catch (error) {
      console.error("Error encoding S7 DateTime Long:", error);
      const buffer = Buffer.alloc(12);
      buffer.fill(0);
      return buffer;
    }
  }
  /**
   * Convert buffer to Timer value (2 bytes)
   * S7 Timer format: 16-bit signed integer
   * @param {Buffer} buffer - 2-byte buffer
   * @returns {number} - Timer value
   */
  static bufferToTimer(buffer) {
    if (buffer.length < 2) {
      throw new Error("Buffer too short for Timer (requires 2 bytes)");
    }
    return buffer.readInt16BE(0);
  }
  /**
   * Convert timer value to buffer
   * @param {number} value - Timer value
   * @returns {Buffer} - 2-byte buffer
   */
  static timerToBuffer(value) {
    const buffer = Buffer.alloc(2);
    buffer.writeInt16BE(value, 0);
    return buffer;
  }
  /**
   * Convert buffer to Counter value (2 bytes)
   * S7 Counter format: 16-bit signed integer
   * @param {Buffer} buffer - 2-byte buffer
   * @returns {number} - Counter value
   */
  static bufferToCounter(buffer) {
    if (buffer.length < 2) {
      throw new Error("Buffer too short for Counter (requires 2 bytes)");
    }
    return buffer.readInt16BE(0);
  }
  /**
   * Convert counter value to buffer
   * @param {number} value - Counter value
   * @returns {Buffer} - 2-byte buffer
   */
  static counterToBuffer(value) {
    const buffer = Buffer.alloc(2);
    buffer.writeInt16BE(value, 0);
    return buffer;
  }
}
class TelegramProcessor {
  constructor(pluginManager = null) {
    this.pluginManager = pluginManager;
  }
  /**
   * Set plugin manager
   * @param {PluginManager} pluginManager - Plugin manager instance
   */
  setPluginManager(pluginManager) {
    this.pluginManager = pluginManager;
  }
  /**
   * Process received telegram data
   * @param {Buffer} buffer - Raw telegram data
   * @param {number} offset - Offset in buffer
   * @param {number} size - Size of data
   * @param {string} peerIp - IP address of sender
   * @returns {number} - Number of processed telegrams
   */
  async processReceived(buffer, offset, size, peerIp) {
    try {
      log$1.info(
        `Telegram(s) received. Buffer size ${size}, offset: ${offset} from ${peerIp}`
      );
      const telegramData = buffer.subarray(offset, offset + size);
      let processedCount = 0;
      let currentOffset = 0;
      while (currentOffset < telegramData.length) {
        if (currentOffset + 2 > telegramData.length) {
          log$1.warn("Incomplete telegram number in buffer");
          break;
        }
        const telNo = S7DataTypes.bufferToWord(
          telegramData.subarray(currentOffset, currentOffset + 2)
        );
        const headerLength = TelegramHeader.getHeaderLength(telNo);
        if (currentOffset + headerLength > telegramData.length) {
          log$1.warn(
            `Incomplete telegram header received. Need ${headerLength} bytes, have ${telegramData.length - currentOffset}`
          );
          break;
        }
        const header = this.parseHeader(
          telegramData.subarray(
            currentOffset,
            currentOffset + headerLength
          ),
          telNo
        );
        if (!header) {
          log$1.error("Failed to parse telegram header");
          break;
        }
        if (currentOffset + header.telLen > telegramData.length) {
          log$1.warn(
            `Incomplete telegram received. Expected ${header.telLen} bytes, got ${telegramData.length - currentOffset}`
          );
          break;
        }
        const completeTelegram = telegramData.subarray(
          currentOffset,
          currentOffset + header.telLen
        );
        log$1.info(
          `Telegram ${header.telNo} (${header.headerType}) received from ${peerIp}`
        );
        header.peerIp = peerIp;
        header.direction = "R";
        const messageBody = completeTelegram.subarray(headerLength);
        await this.processMessageThroughPlugins(
          header,
          messageBody,
          peerIp
        );
        this.debugTelegram(completeTelegram, header.telLen);
        processedCount++;
        currentOffset += header.telLen;
      }
      return processedCount;
    } catch (error) {
      log$1.error("Error processing received telegram:", error);
      return 0;
    }
  }
  /**
   * Parse telegram header from buffer
   * @param {Buffer} headerBuffer - Header buffer (8 or 20 bytes)
   * @param {number} telNo - Telegram number (already read)
   * @returns {TelegramHeader20|TelegramHeader8|null} - Parsed header or null if parsing failed
   */
  parseHeader(headerBuffer, telNo) {
    try {
      const headerType = TelegramHeader.getHeaderType(telNo);
      const expectedLength = TelegramHeader.getHeaderLength(telNo);
      if (headerBuffer.length < expectedLength) {
        log$1.error(
          `Invalid header length: ${headerBuffer.length}, expected ${expectedLength} for ${headerType}`
        );
        return null;
      }
      if (headerType === "TelegramHeader20") {
        return this.parseHeader20(headerBuffer);
      } else {
        return this.parseHeader8(headerBuffer);
      }
    } catch (error) {
      log$1.error("Error parsing telegram header:", error);
      return null;
    }
  }
  /**
   * Parse 20-byte telegram header (telNo < 1000)
   * Format: telNo(2) + telLen(2) + sendId(2) + recId(2) + createTime(8) + telCounter(2) + spare(2)
   * @param {Buffer} headerBuffer - 20-byte header buffer
   * @returns {TelegramHeader20|null} - Parsed header or null if parsing failed
   */
  parseHeader20(headerBuffer) {
    try {
      const header = new TelegramHeader20();
      let offset = 0;
      header.telNo = S7DataTypes.bufferToWord(
        headerBuffer.subarray(offset, offset + 2)
      );
      offset += 2;
      header.telLen = S7DataTypes.bufferToWord(
        headerBuffer.subarray(offset, offset + 2)
      );
      offset += 2;
      header.sendId = S7DataTypes.bufferToFixedString(
        headerBuffer.subarray(offset, offset + 2)
      );
      offset += 2;
      header.recId = S7DataTypes.bufferToFixedString(
        headerBuffer.subarray(offset, offset + 2)
      );
      offset += 2;
      const timestampBuffer = headerBuffer.subarray(offset, offset + 8);
      try {
        header.createTime = S7DataTypes.bufferToDateTime(timestampBuffer);
      } catch (tsError) {
        log$1.warn(
          "Failed to parse S7 timestamp, using current time:",
          tsError
        );
        header.createTime = /* @__PURE__ */ new Date();
      }
      offset += 8;
      header.telCounter = S7DataTypes.bufferToWord(
        headerBuffer.subarray(offset, offset + 2)
      );
      offset += 2;
      header.spare = S7DataTypes.bufferToWord(
        headerBuffer.subarray(offset, offset + 2)
      );
      this.debugHeader(header);
      return header;
    } catch (error) {
      log$1.error("Error parsing 20-byte telegram header:", error);
      return null;
    }
  }
  /**
   * Parse 8-byte telegram header (telNo >= 1000)
   * Format: telNo(2) + telLen(2) + telCounter(2) + spare(2)
   * @param {Buffer} headerBuffer - 8-byte header buffer
   * @returns {TelegramHeader8|null} - Parsed header or null if parsing failed
   */
  parseHeader8(headerBuffer) {
    try {
      const header = new TelegramHeader8();
      let offset = 0;
      header.telNo = S7DataTypes.bufferToWord(
        headerBuffer.subarray(offset, offset + 2)
      );
      offset += 2;
      header.telLen = S7DataTypes.bufferToWord(
        headerBuffer.subarray(offset, offset + 2)
      );
      offset += 2;
      header.telCounter = S7DataTypes.bufferToWord(
        headerBuffer.subarray(offset, offset + 2)
      );
      offset += 2;
      header.spare = S7DataTypes.bufferToWord(
        headerBuffer.subarray(offset, offset + 2)
      );
      this.debugHeader(header);
      return header;
    } catch (error) {
      log$1.error("Error parsing 8-byte telegram header:", error);
      return null;
    }
  }
  /**
   * Compose telegram header to buffer using S7 format
   * @param {TelegramHeader20|TelegramHeader8} header - Telegram header
   * @returns {Buffer} - Header buffer (8 or 20 bytes)
   */
  composeHeader(header) {
    try {
      if (!header.headerType) {
        header.headerType = TelegramHeader.getHeaderType(header.telNo);
      }
      if (header.headerType === "TelegramHeader20") {
        return this.composeHeader20(header);
      } else {
        return this.composeHeader8(header);
      }
    } catch (error) {
      log$1.error("Error composing telegram header:", error);
      return null;
    }
  }
  /**
   * Compose 20-byte telegram header (telNo < 1000)
   * @param {TelegramHeader20} header - Telegram header
   * @returns {Buffer} - 20-byte header buffer
   */
  composeHeader20(header) {
    try {
      const headerBuffer = Buffer.alloc(20);
      let offset = 0;
      S7DataTypes.wordToBuffer(header.telNo).copy(headerBuffer, offset);
      offset += 2;
      S7DataTypes.wordToBuffer(header.telLen).copy(headerBuffer, offset);
      offset += 2;
      S7DataTypes.fixedStringToBuffer(
        header.sendId || "L2",
        2,
        "ascii"
      ).copy(headerBuffer, offset);
      offset += 2;
      S7DataTypes.fixedStringToBuffer(
        header.recId || "PL",
        2,
        "ascii"
      ).copy(headerBuffer, offset);
      offset += 2;
      const createTime = header.createTime || /* @__PURE__ */ new Date();
      const timestampBuffer = S7DataTypes.dateTimeToBuffer(createTime);
      timestampBuffer.copy(headerBuffer, offset);
      offset += 8;
      S7DataTypes.wordToBuffer(header.telCounter || 0).copy(
        headerBuffer,
        offset
      );
      offset += 2;
      S7DataTypes.wordToBuffer(header.spare || 0).copy(
        headerBuffer,
        offset
      );
      return headerBuffer;
    } catch (error) {
      log$1.error("Error composing 20-byte telegram header:", error);
      return null;
    }
  }
  /**
   * Compose 8-byte telegram header (telNo >= 1000)
   * @param {TelegramHeader8} header - Telegram header
   * @returns {Buffer} - 8-byte header buffer
   */
  composeHeader8(header) {
    try {
      const headerBuffer = Buffer.alloc(8);
      let offset = 0;
      S7DataTypes.wordToBuffer(header.telNo).copy(headerBuffer, offset);
      offset += 2;
      S7DataTypes.wordToBuffer(header.telLen).copy(headerBuffer, offset);
      offset += 2;
      S7DataTypes.wordToBuffer(header.telCounter || 0).copy(
        headerBuffer,
        offset
      );
      offset += 2;
      S7DataTypes.wordToBuffer(header.spare || 0).copy(
        headerBuffer,
        offset
      );
      return headerBuffer;
    } catch (error) {
      log$1.error("Error composing 8-byte telegram header:", error);
      return null;
    }
  }
  /**
   * Debug output for telegram header
   * @param {TelegramHeader} header - Telegram header
   */
  debugHeader(header) {
    log$1.debug(
      `Telegram Header - No: ${header.telNo}, Len: ${header.telLen}, Send: ${header.sendId}, Rec: ${header.recId}, Counter: ${header.telCounter}, Time: ${header.createTime ? header.createTime.toISOString() : "N/A"}`
    );
  }
  /**
   * Debug output for raw telegram data
   * @param {Buffer} telegram - Raw telegram data
   * @param {number} length - Telegram length
   */
  debugTelegram(telegram, length) {
    if (log$1.transports.file.level === "debug") {
      const hexString = telegram.slice(0, Math.min(length, 64)).toString("hex");
      log$1.debug(
        `Raw telegram (${length} bytes): ${hexString}${length > 64 ? "..." : ""}`
      );
    }
  }
  /**
   * Compose a telegram with header and body
   * @param {number} telNo - Telegram number
   * @param {string} sendId - Sender ID
   * @param {string} recId - Receiver ID
   * @param {Buffer} messageBody - Message body
   * @param {number} telCounter - Telegram counter
   * @returns {Buffer} - Complete telegram
   */
  composeTelegram(telNo, sendId, recId, messageBody, telCounter = 0) {
    try {
      const totalLength = this.headerLength + messageBody.length;
      const telegram = Buffer.alloc(totalLength);
      telegram.writeUInt16LE(telNo, 0);
      telegram.writeUInt16LE(totalLength, 2);
      telegram.write(sendId.padEnd(2, "\0"), 4, 2, "ascii");
      telegram.write(recId.padEnd(2, "\0"), 6, 2, "ascii");
      const now = Date.now();
      telegram.writeBigUInt64LE(BigInt(now), 8);
      telegram.writeUInt16LE(telCounter, 16);
      telegram.writeUInt16LE(0, 18);
      messageBody.copy(telegram, this.headerLength);
      return telegram;
    } catch (error) {
      log$1.error("Error composing telegram:", error);
      throw error;
    }
  }
  /**
   * Process message through plugin system
   * @param {TelegramHeader} header - Telegram header
   * @param {Buffer} messageBody - Message body
   * @param {string} peerIp - Peer IP address
   */
  async processMessageThroughPlugins(header, messageBody, peerIp) {
    try {
      if (!this.pluginManager) {
        log$1.warn(
          `No plugin manager available for telegram ${header.telNo}`
        );
        return;
      }
      const plugin = this.pluginManager.getReceivePlugin(header.telNo);
      if (!plugin) {
        log$1.warn(`No plugin found for telegram ${header.telNo}`);
        return;
      }
      log$1.debug(
        `Processing telegram ${header.telNo} with plugin: ${plugin.pluginName}`
      );
      plugin.setSendRequestCallback((responseData) => {
        this.handlePluginSendRequest(responseData, peerIp);
      });
      const parseSuccess = plugin.parse(messageBody);
      if (parseSuccess) {
        log$1.debug(`Telegram ${header.telNo} parsed successfully`);
        plugin.debugOutput();
        if (plugin.isSaveToDb) {
          try {
            await plugin.saveToDb();
            log$1.debug(`Telegram ${header.telNo} saved to database`);
          } catch (error) {
            log$1.error(
              `Failed to save telegram ${header.telNo} to database:`,
              error
            );
          }
        }
        await plugin.process(messageBody);
        log$1.debug(`Telegram ${header.telNo} processing completed`);
      } else {
        log$1.error(`Failed to parse telegram ${header.telNo}`);
      }
    } catch (error) {
      log$1.error(
        `Error processing telegram ${header.telNo} through plugins:`,
        error
      );
    }
  }
  /**
   * Handle send request from plugin
   * @param {Buffer} responseData - Response telegram data
   * @param {string} peerIp - Target peer IP
   */
  handlePluginSendRequest(responseData, peerIp) {
    try {
      if (this.sendRequestCallback) {
        this.sendRequestCallback(responseData);
      } else {
        log$1.warn("No send request callback available");
      }
    } catch (error) {
      log$1.error("Error handling plugin send request:", error);
    }
  }
  /**
   * Set send request callback
   * @param {Function} callback - Callback function
   */
  setSendRequestCallback(callback) {
    this.sendRequestCallback = callback;
  }
  /**
   * Compose telegram using send plugin
   * @param {number} telegramNo - Telegram number
   * @returns {Buffer|null} - Composed telegram or null if no plugin found
   */
  async composeWithPlugin(telegramNo) {
    try {
      if (!this.pluginManager) {
        log$1.warn("No plugin manager available for composing telegram");
        return null;
      }
      const plugin = this.pluginManager.getSendPlugin(telegramNo);
      if (!plugin) {
        log$1.warn(`No send plugin found for telegram ${telegramNo}`);
        return null;
      }
      log$1.debug(
        `Composing telegram ${telegramNo} with plugin: ${plugin.pluginName}`
      );
      const telegram = plugin.compose();
      plugin.incrementCounter();
      log$1.debug(`Telegram ${telegramNo} composed successfully`);
      return telegram;
    } catch (error) {
      log$1.error(`Error composing telegram ${telegramNo}:`, error);
      return null;
    }
  }
  /**
   * Create a send request event handler
   * @param {Function} sendCallback - Callback function to send telegram
   * @returns {Function} - Event handler function
   */
  createSendRequestHandler(sendCallback) {
    return (telegramData) => {
      try {
        if (sendCallback && typeof sendCallback === "function") {
          sendCallback(telegramData);
        } else {
          log$1.warn(
            "No send callback provided for telegram send request"
          );
        }
      } catch (error) {
        log$1.error("Error in send request handler:", error);
      }
    };
  }
}
class ServerService extends EventEmitter {
  constructor() {
    super();
    this.tcpServer = new TcpServerService();
    this.tcpClientManager = new TcpClientManager();
    this.pluginManager = new PluginManager();
    this.isRunning = false;
    this.config = null;
    this.statistics = {
      startTime: null,
      connections: 0,
      telegramsReceived: 0,
      telegramsSent: 0
    };
    this.setupEventHandlers();
  }
  setupEventHandlers() {
    this.tcpServer.on("serverStarted", (data) => {
      log$1.info("TCP Server started successfully");
      this.emit(EventTypes.SERVER_STARTED, data);
    });
    this.tcpServer.on("serverStopped", () => {
      log$1.info("TCP Server stopped");
      this.emit(EventTypes.SERVER_STOPPED);
    });
    this.tcpServer.on("connectionChanged", (data) => {
      this.updateConnectionStatistics(data);
      this.emit(EventTypes.CONNECTION_CHANGED, data);
    });
    this.tcpServer.on("telegramProcessed", (data) => {
      this.updateTelegramStatistics(data);
      if (data.type === "received") {
        this.emit(EventTypes.TELEGRAM_RECEIVED, data);
      } else if (data.type === "sent") {
        this.emit(EventTypes.TELEGRAM_SENT, data);
      }
    });
    this.tcpServer.on("serverError", (error) => {
      log$1.error("TCP Server error:", error);
      this.emit("serverError", error);
    });
    this.tcpClientManager.on("clientConnected", (data) => {
      this.updateConnectionStatistics({ connected: true, type: "A" });
      this.emit(EventTypes.CONNECTION_CHANGED, {
        connected: true,
        sessionId: data.clientId,
        peerIp: data.address,
        type: "A"
        // Active connection
      });
    });
    this.tcpClientManager.on("clientDisconnected", (data) => {
      this.updateConnectionStatistics({ connected: false, type: "A" });
      this.emit(EventTypes.CONNECTION_CHANGED, {
        connected: false,
        sessionId: data.clientId,
        peerIp: data.address,
        type: "A"
      });
    });
    this.tcpClientManager.on("telegramProcessed", (data) => {
      this.updateTelegramStatistics(data);
      if (data.type === "received") {
        this.emit(EventTypes.TELEGRAM_RECEIVED, data);
      } else if (data.type === "sent") {
        this.emit(EventTypes.TELEGRAM_SENT, data);
      }
    });
    this.tcpClientManager.on("clientError", (data) => {
      log$1.error("TCP Client error:", data);
      this.emit("clientError", data);
    });
    this.pluginManager.on("pluginLoaded", (data) => {
      log$1.info(`Plugin loaded: ${data.pluginName} (${data.type})`);
      this.emit("pluginLoaded", data);
    });
    this.pluginManager.on("pluginReloaded", (data) => {
      log$1.info(`Plugin reloaded: ${data.filePath}`);
      this.emit("pluginReloaded", data);
    });
    this.pluginManager.on("pluginError", (data) => {
      log$1.error(`Plugin error: ${data.filePath} - ${data.error}`);
      this.emit("pluginError", data);
    });
  }
  updateConnectionStatistics(data) {
    if (data.connected) {
      this.statistics.connections++;
    } else {
      this.statistics.connections = Math.max(
        0,
        this.statistics.connections - 1
      );
    }
  }
  updateTelegramStatistics(data) {
    if (data.type === "received") {
      this.statistics.telegramsReceived += data.count || 1;
    } else if (data.type === "sent") {
      this.statistics.telegramsSent += data.count || 1;
    }
  }
  async start(config) {
    if (this.isRunning) {
      throw new Error("Server is already running");
    }
    try {
      this.config = config;
      this.statistics.startTime = /* @__PURE__ */ new Date();
      await this.pluginManager.initialize();
      this.tcpServer.setPluginManager(this.pluginManager);
      await this.tcpServer.start(config.server.ip, config.server.port);
      if (config.clients && config.clients.length > 0) {
        for (const clientConfig of config.clients) {
          try {
            this.tcpClientManager.addClient(
              clientConfig.ip,
              clientConfig.port,
              true
            );
            log$1.info(
              `Added active client connection to ${clientConfig.ip}:${clientConfig.port}`
            );
          } catch (error) {
            log$1.error(
              `Failed to add client connection to ${clientConfig.ip}:${clientConfig.port}:`,
              error
            );
          }
        }
      }
      if (config.cyclicSends && config.cyclicSends.length > 0) {
        log$1.info(
          `${config.cyclicSends.length} cyclic send configurations found (not yet implemented)`
        );
      }
      this.isRunning = true;
      log$1.info("L2CommServer started successfully");
    } catch (error) {
      log$1.error("Failed to start server:", error);
      await this.stop();
      throw error;
    }
  }
  async stop() {
    if (!this.isRunning) {
      return;
    }
    try {
      log$1.info("Stopping L2CommServer...");
      await this.tcpServer.stop();
      await this.tcpClientManager.disconnectAll();
      this.pluginManager.cleanup();
      this.isRunning = false;
      this.statistics.startTime = null;
      log$1.info("L2CommServer stopped successfully");
    } catch (error) {
      log$1.error("Error stopping server:", error);
      throw error;
    }
  }
  getStatus() {
    const serverStatus = this.tcpServer.getStatus();
    const clientStats = this.tcpClientManager.getStatistics();
    return {
      isRunning: this.isRunning,
      config: this.config,
      server: serverStatus,
      clients: {
        total: clientStats.totalConnections,
        active: clientStats.activeConnections,
        list: this.tcpClientManager.getAllClients().map((client) => client.getStatus())
      },
      statistics: {
        ...this.statistics,
        uptime: this.statistics.startTime ? Math.floor(
          (Date.now() - this.statistics.startTime.getTime()) / 1e3
        ) : 0,
        totalConnections: serverStatus.statistics.connections + clientStats.activeConnections
      }
    };
  }
  async sendTelegram(sessionId, telegramData) {
    try {
      this.tcpServer.sendToSession(sessionId, telegramData);
      return;
    } catch (error) {
    }
    const clients = this.tcpClientManager.getAllClients();
    const client = clients.find((c) => c.id === sessionId);
    if (client && client.isConnected) {
      await client.sendAsync(telegramData);
    } else {
      throw new Error(
        `Session or client ${sessionId} not found or not connected`
      );
    }
  }
  async sendToAllSessions(telegramData) {
    this.tcpServer.sendToAll(telegramData);
    await this.tcpClientManager.sendToAllClients(telegramData);
  }
  getActiveSessions() {
    const serverSessions = this.tcpServer.getStatus().activeSessions || [];
    const clientSessions = this.tcpClientManager.getAllClients().filter((client) => client.isConnected).map((client) => ({
      id: client.id,
      peerIp: client.address,
      peerPort: client.port,
      connectionTime: client.connectionTime,
      type: "A"
      // Active
    }));
    return [...serverSessions, ...clientSessions];
  }
  addClient(ip, port) {
    return this.tcpClientManager.addClient(ip, port, true);
  }
  removeClient(ip, port) {
    this.tcpClientManager.removeClient(ip, port);
  }
  getServerStatistics() {
    const status = this.getStatus();
    return {
      connections: status.statistics.totalConnections,
      telegramsReceived: status.statistics.telegramsReceived,
      telegramsSent: status.statistics.telegramsSent,
      uptime: status.statistics.uptime,
      startTime: status.statistics.startTime,
      serverSessions: status.server.statistics.connections,
      clientSessions: status.clients.active
    };
  }
  /**
   * Get all loaded plugins
   * @returns {Object} - Plugin information
   */
  getAllPlugins() {
    return this.pluginManager.getAllPlugins();
  }
  /**
   * Reload all plugins
   */
  async reloadAllPlugins() {
    await this.pluginManager.reloadAllPlugins();
  }
  /**
   * Get plugin statistics
   * @returns {Object} - Plugin statistics
   */
  getPluginStatistics() {
    return this.pluginManager.getStatistics();
  }
  /**
   * Compose and send telegram using plugin
   * @param {number} telegramNo - Telegram number
   * @param {string} targetIp - Target IP address (optional)
   * @returns {boolean} - Success status
   */
  async sendTelegramWithPlugin(telegramNo, targetIp = null) {
    try {
      const processor = new TelegramProcessor(this.pluginManager);
      const telegram = await processor.composeWithPlugin(telegramNo);
      if (!telegram) {
        return false;
      }
      if (targetIp) {
        const clients = this.tcpClientManager.getAllClients();
        const targetClient = clients.find(
          (client) => client.address === targetIp
        );
        if (targetClient && targetClient.isConnected) {
          await targetClient.sendAsync(telegram);
        } else {
          const sessions = this.tcpServer.getSessions();
          const targetSession = sessions.find(
            (session) => session.peerIp === targetIp
          );
          if (targetSession) {
            targetSession.onSendRequest(telegram);
          } else {
            log$1.warn(
              `No connection found for target IP: ${targetIp}`
            );
            return false;
          }
        }
      } else {
        await this.sendToAllSessions(telegram);
      }
      return true;
    } catch (error) {
      log$1.error(`Failed to send telegram ${telegramNo}:`, error);
      return false;
    }
  }
}
class TrayService {
  constructor() {
    this.tray = null;
    this.mainWindow = null;
    this.isQuitting = false;
  }
  /**
   * Initialize system tray
   * @param {BrowserWindow} mainWindow - Main application window
   */
  initialize(mainWindow2) {
    this.mainWindow = mainWindow2;
    try {
      const iconPath = this.getIconPath();
      const icon = nativeImage.createFromPath(iconPath);
      const resizedIcon = icon.resize({ width: 16, height: 16 });
      this.tray = new Tray(resizedIcon);
      this.tray.setToolTip(
        "L2 Communication Server - 双击显示窗口，右键菜单"
      );
      this.createContextMenu();
      this.tray.on("double-click", () => {
        this.showWindow();
      });
      this.tray.on("click", () => {
        if (process.platform === "win32") {
          this.showWindow();
        }
      });
      log$1.info("System tray initialized successfully");
    } catch (error) {
      log$1.error("Failed to initialize system tray:", error);
    }
  }
  /**
   * Get icon path based on platform
   * @returns {string} - Icon file path
   */
  getIconPath() {
    const isDev = process.env.NODE_ENV === "development";
    if (isDev) {
      return path.join(__dirname, "../../../resources/server.ico");
    } else {
      return path.join(process.resourcesPath, "server.ico");
    }
  }
  /**
   * Create context menu for tray icon
   */
  createContextMenu() {
    const contextMenu = Menu.buildFromTemplate([
      {
        label: "显示窗口",
        click: () => {
          this.showWindow();
        },
        enabled: !this.isWindowVisible()
      },
      {
        label: "隐藏窗口",
        click: () => {
          this.hideWindow();
        },
        enabled: this.isWindowVisible()
      },
      {
        type: "separator"
      },
      {
        label: "重新加载插件",
        click: () => {
          this.reloadPlugins();
        }
      },
      {
        label: "服务器状态",
        submenu: [
          {
            label: "启动服务器",
            click: () => {
              this.startServer();
            }
          },
          {
            label: "停止服务器",
            click: () => {
              this.stopServer();
            }
          },
          {
            type: "separator"
          },
          {
            label: "查看连接状态",
            click: () => {
              this.showConnectionStatus();
            }
          }
        ]
      },
      {
        type: "separator"
      },
      {
        label: "关于",
        click: () => {
          this.showAbout();
        }
      },
      {
        label: "退出",
        click: () => {
          this.exitApplication();
        }
      }
    ]);
    this.tray.setContextMenu(contextMenu);
  }
  /**
   * Show main window
   */
  showWindow() {
    if (this.mainWindow) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.show();
      this.mainWindow.focus();
      this.createContextMenu();
      log$1.info("Main window shown from tray");
    }
  }
  /**
   * Hide main window
   */
  hideWindow() {
    if (this.mainWindow) {
      this.mainWindow.hide();
      this.createContextMenu();
      log$1.info("Main window hidden to tray");
    }
  }
  /**
   * Check if main window is visible
   * @returns {boolean} - True if window is visible
   */
  isWindowVisible() {
    return this.mainWindow && this.mainWindow.isVisible() && !this.mainWindow.isMinimized();
  }
  /**
   * Reload plugins
   */
  reloadPlugins() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send("tray:reload-plugins");
      log$1.info("Plugin reload requested from tray");
    }
  }
  /**
   * Start server
   */
  startServer() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send("tray:start-server");
      log$1.info("Server start requested from tray");
    }
  }
  /**
   * Stop server
   */
  stopServer() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send("tray:stop-server");
      log$1.info("Server stop requested from tray");
    }
  }
  /**
   * Show connection status
   */
  showConnectionStatus() {
    if (this.mainWindow) {
      this.showWindow();
      this.mainWindow.webContents.send("tray:show-connections");
      log$1.info("Connection status view requested from tray");
    }
  }
  /**
   * Show about dialog
   */
  showAbout() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send("tray:show-about");
      log$1.info("About dialog requested from tray");
    }
  }
  /**
   * Exit application
   */
  exitApplication() {
    log$1.info("Application exit requested from tray");
    this.isQuitting = true;
    if (this.tray) {
      this.tray.destroy();
      this.tray = null;
    }
    app.quit();
  }
  /**
   * Handle window close event
   * @param {Event} event - Close event
   */
  handleWindowClose(event) {
    if (!this.isQuitting) {
      event.preventDefault();
      this.hideWindow();
      if (this.tray && !this.hasShownHideNotification) {
        this.tray.displayBalloon({
          title: "L2 Communication Server",
          content: "应用程序已最小化到系统托盘。双击托盘图标可重新显示窗口。",
          icon: nativeImage.createFromPath(this.getIconPath())
        });
        this.hasShownHideNotification = true;
      }
    }
  }
  /**
   * Update tray menu based on application state
   * @param {Object} state - Application state
   */
  updateTrayMenu(state = {}) {
    if (!this.tray) return;
    const contextMenu = Menu.buildFromTemplate([
      {
        label: "显示窗口",
        click: () => {
          this.showWindow();
        },
        enabled: !this.isWindowVisible()
      },
      {
        label: "隐藏窗口",
        click: () => {
          this.hideWindow();
        },
        enabled: this.isWindowVisible()
      },
      {
        type: "separator"
      },
      {
        label: "重新加载插件",
        click: () => {
          this.reloadPlugins();
        }
      },
      {
        label: "服务器状态",
        submenu: [
          {
            label: state.serverRunning ? "停止服务器" : "启动服务器",
            click: () => {
              if (state.serverRunning) {
                this.stopServer();
              } else {
                this.startServer();
              }
            }
          },
          {
            type: "separator"
          },
          {
            label: `连接数: ${state.connectionCount || 0}`,
            enabled: false
          },
          {
            label: "查看连接状态",
            click: () => {
              this.showConnectionStatus();
            }
          }
        ]
      },
      {
        type: "separator"
      },
      {
        label: "关于",
        click: () => {
          this.showAbout();
        }
      },
      {
        label: "退出",
        click: () => {
          this.exitApplication();
        }
      }
    ]);
    this.tray.setContextMenu(contextMenu);
  }
  /**
   * Cleanup tray resources
   */
  destroy() {
    if (this.tray) {
      this.tray.destroy();
      this.tray = null;
    }
  }
}
function setupLogHandlers(mainWindow2) {
  loggingService.setMainWindow(mainWindow2);
  ipcMain.handle("get-log-level", () => {
    return loggingService.getLogLevel();
  });
  ipcMain.handle("set-log-level", (_, level) => {
    loggingService.setLogLevel(level);
    return true;
  });
  ipcMain.handle("add-log", (_, level, ...args) => {
    loggingService.log(level, ...args);
    return true;
  });
  ipcMain.handle("clear-logs", () => {
    loggingService.clearBuffer();
    return true;
  });
  ipcMain.handle("get-buffered-logs", () => {
    return loggingService.getBufferedLogs();
  });
}
function initializeIpcHandlers(mainWindow2) {
  setupLogHandlers(mainWindow2);
}
Store.initRenderer();
!app.isPackaged;
const isMacOS = process.platform === "darwin";
const isLinux = process.platform === "linux";
const isWindows = process.platform === "win32";
const gotTheLock = app.requestSingleInstanceLock();
let serverService = null;
let trayService = null;
function createWindow() {
  const mainWindow2 = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1024,
    minHeight: 768,
    show: !isWindows,
    title: "L2 Communication Server",
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    },
    autoHideMenuBar: true,
    titleBarStyle: isLinux ? "default" : "hidden",
    titleBarOverlay: isWindows ? {
      color: "#f3f4f6",
      symbolColor: "#333",
      height: 36
    } : false,
    trafficLightPosition: isMacOS ? { x: 10, y: 8 } : void 0,
    backgroundColor: "#fff"
  });
  mainWindow2.on("ready-to-show", () => {
    mainWindow2.show();
  });
  mainWindow2.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url);
    return { action: "deny" };
  });
  mainWindow2.on("close", (event) => {
    if (trayService) {
      trayService.handleWindowClose(event);
    }
  });
  if (is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow2.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow2.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
  loggingService.info("L2CommServer-JS is ready");
  return mainWindow2;
}
let mainWindow;
if (!gotTheLock) app.quit();
else {
  app.whenReady().then(() => {
    electronApp.setAppUserModelId("com.l2commserver.js");
    app.on("browser-window-created", (_, window) => {
      optimizer.watchWindowShortcuts(window);
    });
    if (isMacOS && app.dock) ;
    mainWindow = createWindow();
    initializeIpcHandlers(mainWindow);
    serverService = new ServerService();
    setupIpcHandlers(mainWindow, serverService);
    trayService = new TrayService();
    trayService.initialize(mainWindow);
  });
  app.on("window-all-closed", () => {
    if (process.platform !== "darwin" && !trayService) {
      app.quit();
    }
  });
  app.on("activate", function() {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
  app.on("before-quit", () => {
    if (trayService) {
      trayService.destroy();
    }
    if (serverService) {
      serverService.stop();
    }
  });
}
function setupIpcHandlers(mainWindow2, serverService2) {
  ipcMain.handle("server:start", async () => {
    try {
      const defaultConfig = {
        server: {
          ip: "127.0.0.1",
          port: 1111,
          title: "L2 Communication Server"
        },
        clients: [],
        cyclicSends: []
      };
      await serverService2.start(defaultConfig);
      return { success: true };
    } catch (error) {
      log.error("Failed to start server:", error);
      return { success: false, error: error.message };
    }
  });
  ipcMain.handle("server:stop", async () => {
    try {
      await serverService2.stop();
      return { success: true };
    } catch (error) {
      log.error("Failed to stop server:", error);
      return { success: false, error: error.message };
    }
  });
  ipcMain.handle("server:status", () => {
    return serverService2.getStatus();
  });
  ipcMain.handle("db:getStatistics", () => {
    return serverService2.getServerStatistics();
  });
  ipcMain.handle("plugins:load", () => {
    try {
      return serverService2.getAllPlugins();
    } catch (error) {
      log.error("Failed to get plugins:", error);
      return { error: error.message };
    }
  });
  ipcMain.handle("plugins:reload", async () => {
    try {
      await serverService2.reloadAllPlugins();
      return {
        success: true,
        message: "All plugins reloaded successfully"
      };
    } catch (error) {
      log.error("Failed to reload plugins:", error);
      return { success: false, error: error.message };
    }
  });
  ipcMain.handle("plugins:list", () => {
    try {
      return serverService2.getAllPlugins();
    } catch (error) {
      log.error("Failed to get plugin list:", error);
      return { receive: [], send: [], total: 0 };
    }
  });
  ipcMain.handle("plugins:statistics", () => {
    try {
      return serverService2.getPluginStatistics();
    } catch (error) {
      log.error("Failed to get plugin statistics:", error);
      return { receivePlugins: 0, sendPlugins: 0, totalPlugins: 0 };
    }
  });
  ipcMain.handle(
    "plugins:sendTelegram",
    async (event, telegramNo, targetIp) => {
      try {
        const success = await serverService2.sendTelegramWithPlugin(
          telegramNo,
          targetIp
        );
        return {
          success,
          message: success ? "Telegram sent successfully" : "Failed to send telegram"
        };
      } catch (error) {
        log.error("Failed to send telegram:", error);
        return { success: false, error: error.message };
      }
    }
  );
  ipcMain.handle("config:load", () => {
    return { message: "Configuration loading not yet implemented" };
  });
  ipcMain.handle("config:save", (event, config) => {
    return { message: "Configuration saving not yet implemented" };
  });
  ipcMain.handle("db:getTelegramHistory", (event, limit) => {
    return { message: "Telegram history not yet implemented" };
  });
  ipcMain.handle("tray:updateMenu", (event, state) => {
    if (trayService) {
      trayService.updateTrayMenu(state);
    }
    return { success: true };
  });
  serverService2.on("server:started", (data) => {
    mainWindow2.webContents.send("server:event", {
      status: "running",
      data
    });
    if (trayService) {
      trayService.updateTrayMenu({
        serverRunning: true,
        connectionCount: 0
      });
    }
  });
  serverService2.on("server:stopped", () => {
    mainWindow2.webContents.send("server:event", { status: "stopped" });
    if (trayService) {
      trayService.updateTrayMenu({
        serverRunning: false,
        connectionCount: 0
      });
    }
  });
  serverService2.on("connection:changed", (data) => {
    mainWindow2.webContents.send("connection:changed", data);
    if (trayService) {
      trayService.updateTrayMenu({
        serverRunning: serverService2.isRunning(),
        connectionCount: data.connectionCount || 0
      });
    }
  });
  serverService2.on("telegram:received", (data) => {
    mainWindow2.webContents.send("telegram:received", data);
  });
  serverService2.on("telegram:sent", (data) => {
    mainWindow2.webContents.send("telegram:sent", data);
  });
}
