const {
    ReceivePluginBase,
    exportPlugin,
    S7DataTypes,
} = require('../plugin-base');

/**
 * TeleReceive103 - Simple distance measurement telegram processor
 * Processes telegram 103 containing a single distance measurement
 * This is a simplified version similar to the original C# implementation
 */
class TeleReceive103 extends ReceivePluginBase {
    constructor() {
        super();
        this.telegramNo = 103;
        this.pluginName = 'TeleReceive103';
        this.version = '1.0.0';
        this.description = 'Process telegram 103 - Simple distance measurement';
        this.author = 'L2CommServer Team';
        this.messageLength = 4; // Expected message length: 4 bytes for distance (Real)

        // Plugin-specific properties
        this.distance = 0.0;
    }

    /**
     * Parse message body
     * @param {Buffer} body - Message body (4 bytes containing distance as Real)
     * @returns {boolean} - True if parsing was successful
     */
    parse(body) {
        try {
            if (body.length >= this.messageLength) {
                // Parse distance value from first 4 bytes (Real/Float32)
                // Offset: 0, Length: 4 bytes
                this.distance = S7DataTypes.bufferToReal(body.subarray(0, 4));
                return true;
            } else {
                console.warn(
                    `[${this.pluginName}] Invalid telegram body size. Expected ${this.messageLength}, got ${body.length}`
                );
                return false;
            }
        } catch (error) {
            console.error(
                `[${this.pluginName}] Parse telegram failed:`,
                error.message
            );
            return false;
        }
    }

    /**
     * Process the parsed message
     * @param {Buffer} body - Message body
     */
    async process(body) {
        try {
            console.log(
                `[${this.pluginName}] Start processing telegram ${this.telegramNo}`
            );

            // Parse message
            if (this.parse(body)) {
                console.log(
                    `[${this.pluginName}] Parsed telegram ${this.telegramNo} successfully`
                );

                // Debug Output
                this.debugOutput();

                // Save to DB
                if (this.isSaveToDb) {
                    const rowsAffected = await this.saveToDb();
                    if (rowsAffected > 0) {
                        console.log(
                            `[${this.pluginName}] Telegram ${this.telegramNo} saved to database`
                        );
                    }
                }

                // Process message - ADD CUSTOM CODE HERE
                // This is where you would add your specific business logic

                console.log(
                    `[${this.pluginName}] Process telegram ${this.telegramNo} finished`
                );
            } else {
                // Parse method returned false
                console.error(
                    `[${this.pluginName}] Parse telegram ${this.telegramNo} failed`
                );
            }
        } catch (error) {
            console.error(`[${this.pluginName}] Process error:`, error);
        }
    }

    /**
     * Debug output for the plugin
     */
    debugOutput() {
        console.log(
            `[${this.pluginName}] Distance: ${this.distance.toFixed(3)}`
        );
    }

    /**
     * Save data to database (override default implementation)
     * @returns {number} - Number of affected rows
     */
    async saveToDb() {
        try {
            // This would typically save to a database
            // For now, just log the operation
            console.log(
                `[${this.pluginName}] Saving distance measurement: ${this.distance}`
            );

            // Simulate database operation
            // In a real implementation, you would:
            // 1. Connect to database
            // 2. Insert/update the distance value
            // 3. Return number of affected rows

            return 1;
        } catch (error) {
            console.error(`[${this.pluginName}] Database save error:`, error);
            return 0;
        }
    }

    /**
     * Get current distance value
     * @returns {number} - Current distance
     */
    getDistance() {
        return this.distance;
    }

    /**
     * Get plugin data as object
     * @returns {Object} - Plugin data
     */
    getData() {
        return {
            telegramNo: this.telegramNo,
            distance: this.distance,
            timestamp: new Date(),
        };
    }
}

// Export the plugin
module.exports = exportPlugin(TeleReceive103);
