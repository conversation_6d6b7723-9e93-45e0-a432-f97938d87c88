import { BrowserWindow } from 'electron'
import { NodeS7 } from '@common/libs/nodeS7'
import {
    BilletData,
    BilletDataStruct,
    getTagName,
} from '@common/interfaces/tcData'
import log from 'electron-log'

export class PlcService {
    private window: BrowserWindow
    private conn: NodeS7
    private plcAddress: string = '***************'
    private billetMax: number = 88
    private lastDischPos: number = 98
    private dischPosTag: string = 'DB900,INT64'
    private isConnected: boolean = false

    constructor(mainWindow: BrowserWindow) {
        this.window = mainWindow
        this.conn = new NodeS7()
    }

    public async connect(): Promise<void> {
        try {
            await this.conn.initiateConnectionAsync({
                port: 102,
                host: this.plcAddress,
                rack: 0,
                slot: 1,
                debug: false,
            })
            this.isConnected = true
            this.connected()
        } catch (err) {
            this.isConnected = false
            if (typeof err !== 'undefined') {
                log.error(err)
            }
        }
    }

    public getConnectionStatus(): boolean {
        return this.isConnected
    }

    private connected(): void {
        this.conn.addItems(this.dischPosTag)
        let items: string[] = []
        for (let i = 0; i < this.billetMax; i++) {
            items.push(...this.getBilletTags(i))
        }
        items.push(...this.getBilletTags(this.lastDischPos))
        this.conn.addItems(items)
    }

    private getBilletTags(pos: number): string[] {
        let res: string[] = []
        for (let key in BilletDataStruct) {
            if (BilletDataStruct.hasOwnProperty(key)) {
                res.push(getTagName(pos, key))
            }
        }
        return res
    }

    private parseBilletData(pos: number, values: any): BilletData {
        let res = {} as BilletData
        for (let key in BilletDataStruct) {
            if (BilletDataStruct.hasOwnProperty(key)) {
                res[key] = values[getTagName(pos, key)] ?? ''
            }
        }
        return res
    }

    private parseBillets(values: any): BilletData[] {
        let res: BilletData[] = []
        const count = values[this.dischPosTag] ?? this.billetMax
        for (let i = 0; i < count; i++) {
            res[i] = this.parseBilletData(i, values)
        }
        return res
    }

    private valuesReady(values: any): void {
        const billetData = this.parseBillets(values)
        const dischPos = values[this.dischPosTag]
        const lastDischarged = this.parseBilletData(this.lastDischPos, values)
        this.window.webContents.send('tc-tracking-data', {
            BilletData: billetData,
            DischPos: dischPos,
            LastDischarged: lastDischarged,
        })
    }

    public async loadTCData(): Promise<void> {
        if (!this.isConnected) {
            log.warn('Attempted to load TC data while not connected')
            return
        }
        try {
            const values = await this.conn.readAllItemsAsync()
            this.valuesReady(values)
        } catch (err) {
            this.isConnected = false
            if (err !== 'undefined') {
                log.error('Got wrong values', err)
            }
        }
    }
}
