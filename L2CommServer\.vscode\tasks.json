{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/L2CommServer/L2CommServer.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/L2CommServer/L2CommServer.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/L2CommServer/L2CommServer.csproj"], "problemMatcher": "$msCompile"}]}