# L2CommServer-JS

A JavaScript/Electron implementation of the L2 Communication Server, migrated from the original C#/.NET version.

## Overview

L2CommServer-JS is a modern implementation of the L2 Communication Server using JavaScript and Electron. It provides the same core functionality as the original C# version:

- TCP server for handling telegram-based communication
- Plugin-based message processing system
- Database integration for logging and data storage
- Real-time monitoring and management interface
- Configuration management through INI files

## Features

- **TCP Communication Server**: Handles incoming and outgoing telegram communications
- **Plugin Architecture**: Dynamic loading of message processors for different telegram types
- **Database Integration**: SQLite database for logging telegram history and application data
- **Real-time UI**: Electron-based interface for monitoring and controlling the server
- **Configuration Management**: INI file-based configuration system
- **Logging System**: Comprehensive logging with multiple output targets
- **Cross-platform**: Runs on Windows, macOS, and Linux

## Architecture

The application follows Electron's multi-process architecture:

- **Main Process** (`src/main/`): Core server logic, TCP server, database operations
- **Renderer Process** (`src/renderer/`): User interface and visualization
- **Preload Scripts** (`src/preload/`): Secure communication bridge between main and renderer
- **Common** (`src/common/`): Shared interfaces and utilities

## Project Structure

```
L2CommServer-JS/
├── src/
│   ├── main/           # Main process (Node.js)
│   │   ├── index.js    # Application entry point
│   │   ├── services/   # Core services (TCP server, database, plugins)
│   │   └── ipc-handlers/ # IPC message handlers
│   ├── preload/        # Preload scripts
│   │   └── index.js    # Main preload script
│   ├── renderer/       # Renderer process (UI)
│   │   ├── index.html  # Main HTML file
│   │   └── src/        # UI JavaScript and assets
│   └── common/         # Shared code and interfaces
│       └── interfaces.js
├── package.json
├── electron.vite.config.js
└── README.md
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

## Development

Start the development server:
```bash
npm run dev
```

## Building

Build the application:
```bash
npm run build
```

Build for Windows:
```bash
npm run build:win
```

## Configuration

The application uses an INI configuration file similar to the original C# version. Configuration includes:

- Server settings (IP, port)
- Database connection parameters
- Client connection definitions
- Cyclic send configurations

## Plugin System

The plugin system allows for dynamic loading of message processors:

- **Receive Plugins**: Process incoming telegrams
- **Send Plugins**: Compose outgoing telegrams
- **Dynamic Loading**: Plugins can be loaded/reloaded at runtime

## Migration from C# Version

This JavaScript implementation maintains compatibility with the original C# version:

- Same telegram format and protocol
- Compatible database schema
- Equivalent plugin interface
- Similar configuration format

## Dependencies

- **Electron**: Desktop application framework
- **electron-vite**: Build tooling for Electron
- **electron-log**: Logging system
- **electron-store**: Configuration storage
- **sqlite3**: Database operations
- **ini**: INI file parsing

## License

[Add license information here]

## Contributing

[Add contributing guidelines here]
