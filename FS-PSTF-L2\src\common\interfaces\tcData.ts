const billetDb = 'DB960'
const billetDataLen = 200

export interface BilletData {
    Billet_ID: string
    Roll_No: string
    Batch_No: string
    Steel_Grade: string
    Billet_Length: number // billet length, mm
    Billet_Width: number // billet width, mm
}

// Possible type: 'S', 'WS', 'INT', 'X', 'DT', 'REAL', 'DINT', 'WORD'
const BilletDataStruct = {
    // Billet_ID, string[12], offset 0
    Billet_ID: { type: 'S', offset: 0, length: 12 },
    Roll_No: { type: 'S', offset: 14, length: 10 },
    Batch_No: { type: 'S', offset: 26, length: 14 },
    Steel_Grade: { type: 'S', offset: 42, length: 30 },
    Billet_Length: { type: 'INT', offset: 110 },
    Billet_Width: { type: 'INT', offset: 122 },
}

function getTagName(pos: number, key: string): string {
    switch (BilletDataStruct[key].type) {
        case 'S':
            return (
                billetDb +
                ',' +
                BilletDataStruct[key].type +
                (
                    pos * billetDataLen +
                    BilletDataStruct[key].offset
                ).toString() +
                '.' +
                BilletDataStruct[key].length
            )
        // everything else is default
        default:
            return (
                billetDb +
                ',' +
                BilletDataStruct[key].type +
                (pos * billetDataLen + BilletDataStruct[key].offset).toString()
            )
    }
}

// export functions
export { BilletDataStruct, getTagName }
