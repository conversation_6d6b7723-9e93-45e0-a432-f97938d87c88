<template>
    <div class="panel">
        <n-space vertical>
            <img class="w-256 h-256" alt="Logo" :src="appIcon" />
            <n-text strong tag="div" class="flex justify-center text-16px">
                {{ appName }} v{{ appVersion }}
            </n-text>
            <n-text strong tag="div" class="flex justify-center text-14px">
                2025.07
            </n-text>
        </n-space>
    </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useApplicationStore } from '@/stores/application'
import eventBus from '@common/libs/eventBus'
import appIcon from '@/images/logo-1024.png'

const { appName, appVersion } = useApplicationStore()

onMounted(() => {
    window.electron.ipcRenderer.invoke('add-log', 'verbose', 'INFO页面已加载')
    // set window title
    eventBus.emit('update-window-title', '关于')
})
</script>

<style lang="scss" scoped>
.panel {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    margin: auto;
    height: 100%;
    width: 500px;
}
</style>
