// app related
import { ipcMain, app, BrowserWindow } from 'electron'

export default (mainWindow: BrowserWindow) => {
    // 监听渲染进程发出的事件
    // check if app is in dev mode
    ipcMain.handle('check-is-dev', (_event) => {
        return !app.isPackaged
    })

    // get app path, return app path
    ipcMain.handle('get-app-path', (_event) => {
        return app.getAppPath()
    })

    // get path, return path as string
    ipcMain.handle('get-path', (_event, arg) => {
        return app.getPath(arg)
    })

    // get current window
    ipcMain.handle('get-current-window', (_event) => {
        return mainWindow
    })

    // open dev tools
    ipcMain.handle('open-dev-tools', (_event) => {
        mainWindow.webContents.openDevTools()
    })

    // get app version (synced)
    ipcMain.on('get-app-version', (event) => {
        event.returnValue = app.getVersion()
    })

    // get app name (synced)
    ipcMain.on('get-app-name', (event) => {
        event.returnValue = app.getName()
    })
}
