import { toRaw } from 'vue'
import { defineStore } from 'pinia'
import { uidGen } from '@common/libs/uidGen'
import { useElectronStore } from '@/composables'

const persistentStore = useElectronStore('tabs')

export interface WorkspaceTab {
    // workspace id
    uid: string
    // tab id
    tabUid?: string
    index?: number
    selected?: boolean
    // 'data' for data entry tabs
    type?: string
    // tab name (tab Title)
    elementName?: string
    elementNewName?: string
    // tab type: quote, client, dict-material, new-quote...
    elementType?: string
    isChanged?: boolean
    // item index in db, coresponding to navigation tree option.key
    dbIndex: string
    autorun?: boolean
}

export interface Workspace {
    uid: string
    selectedTab: string | number
    searchTerm: string
    tabs: WorkspaceTab[]
    users: { host: string; name: string; password?: string }[]
    loadingElements: { name: string; type: string }[]
    indexTypes?: string[]
    version?: {
        number: string
        name: string
        arch: string
        os: string
    }
    engines?: { [key: string]: string | boolean | number }[]
}

const tabIndex: { [key: string]: number } = {}
const defaultWorkspaceName = 'EMPTY'

export const useWorkspacesStore = defineStore('workspaces', {
    state: () => ({
        workspaces: [] as Workspace[],
        selectedWorkspace: defaultWorkspaceName,
    }),
    getters: {
        getSelected: (state) => {
            if (!state.workspaces.length) return defaultWorkspaceName
            if (state.selectedWorkspace) return state.selectedWorkspace

            return state.workspaces[0].uid
        },
        getWorkspace: (state) => (uid: string) => {
            return state.workspaces.find((workspace) => workspace.uid === uid)
        },
        getWorkspaceTab(state) {
            return (tUid: string) => {
                if (!this.getSelected) {
                    return {}
                }
                const workspace = state.workspaces.find(
                    (workspace) => workspace.uid === this.getSelected,
                )
                if ('tabs' in <Workspace>workspace) {
                    return workspace?.tabs.find((tab) => tab.uid === tUid)
                }
                return {}
            }
        },
    },
    actions: {
        setWorkspace(uid: string) {
            if (!uid)
                this.selectedWorkspace = this.workspaces.length
                    ? this.workspaces[0].uid
                    : defaultWorkspaceName
            else this.selectedWorkspace = uid
        },
        addWorkspace(uid: string) {
            const workspace: Workspace = {
                uid,
                selectedTab: 0,
                searchTerm: '',
                tabs: [],
                users: [],
                loadingElements: [],
            }

            this.workspaces.push(workspace)
        },
        addLoadingElement(element: { name: string; type: string }) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) => {
                    if (workspace.uid === this.getSelected)
                        workspace.loadingElements.push(element)
                    return workspace
                },
            )
        },
        removeLoadingElement(element: { name: string; type: string }) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) => {
                    if (workspace.uid === this.getSelected) {
                        const loadingElements =
                            workspace.loadingElements.filter(
                                (el) =>
                                    el.name !== element.name &&
                                    el.type !== element.type,
                            )

                        workspace = { ...workspace, loadingElements }
                    }
                    return workspace
                },
            )
        },
        setSearchTerm(term: string) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) =>
                    workspace.uid === this.getSelected
                        ? {
                              ...workspace,
                              searchTerm: term,
                          }
                        : workspace,
            )
        },
        _addTab({
            uid,
            tabUid,
            dbIndex,
            type,
            autorun,
            elementName,
            elementType,
        }: WorkspaceTab) {
            if (type === 'data')
                tabIndex[uid] = tabIndex[uid] ? ++tabIndex[uid] : 1
            tabIndex[uid] = tabIndex[uid] ? ++tabIndex[uid] : 1

            const newTab: WorkspaceTab = {
                uid: uid,
                tabUid: tabUid,
                //index: type === 'data' ? tabIndex[uid] : 0,
                index: tabIndex[uid],
                selected: false,
                type: type,
                elementName: elementName,
                elementType: elementType,
                dbIndex: dbIndex || '',
                autorun: !!autorun,
            }

            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) => {
                    if (workspace.uid === uid) {
                        return {
                            ...workspace,
                            tabs: toRaw(workspace.tabs).concat([newTab]),
                        }
                    } else return workspace
                },
            )

            persistentStore.set(
                uid,
                (this.workspaces as Workspace[]).find(
                    (workspace) => workspace.uid === uid,
                )?.tabs,
            )
        },
        _replaceTab({
            uid,
            tabUid: tUid,
            type,
            dbIndex,
            elementName,
            elementType,
        }: WorkspaceTab) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) => {
                    if (workspace.uid === uid) {
                        return {
                            ...workspace,
                            tabs: workspace.tabs.map((tab) => {
                                if (tab.tabUid === tUid)
                                    return {
                                        ...tab,
                                        type,
                                        dbIndex,
                                        elementName,
                                        elementType,
                                    }

                                return tab
                            }),
                        }
                    } else return workspace
                },
            )

            persistentStore.set(
                uid,
                (this.workspaces as Workspace[]).find(
                    (workspace) => workspace.uid === uid,
                )?.tabs,
            )
        },
        newTab({
            uid,
            dbIndex,
            type,
            autorun,
            elementName,
            elementType,
        }: WorkspaceTab) {
            let newTabUid
            const tempWorkspace = (this.workspaces as Workspace[]).find(
                (workspace) => workspace.uid === uid,
            )

            switch (type) {
                case 'temp-data':
                    {
                        const existentTab = tempWorkspace
                            ? tempWorkspace.tabs.find(
                                  (tab) =>
                                      tab.elementName === elementName &&
                                      tab.elementType === elementType &&
                                      [
                                          type,
                                          type.replace('temp-', ''),
                                      ].includes(tab.type as string),
                              )
                            : false

                        if (existentTab) {
                            // if tab exists
                            newTabUid = existentTab.tabUid
                        } else {
                            const tempTabs = tempWorkspace
                                ? tempWorkspace.tabs.filter((tab) =>
                                      tab.type?.includes('temp-'),
                                  )
                                : false

                            if (tempTabs && tempTabs.length) {
                                // if temp tab already opened
                                for (const tab of tempTabs) {
                                    if (tab.isChanged) {
                                        this._replaceTab({
                                            // make permanent a temp table with unsaved changes
                                            uid,
                                            tabUid: tab.tabUid,
                                            type: (<string>tab.type).replace(
                                                'temp-',
                                                '',
                                            ),
                                            dbIndex,
                                            elementName: tab.elementName,
                                            elementType: tab.elementType,
                                        })

                                        newTabUid = uidGen('T')
                                        this._addTab({
                                            uid,
                                            tabUid: newTabUid,
                                            dbIndex,
                                            type,
                                            autorun,
                                            elementName,
                                            elementType,
                                        })
                                    } else {
                                        this._replaceTab({
                                            uid,
                                            tabUid: tab.tabUid,
                                            type,
                                            dbIndex,
                                            elementName,
                                            elementType,
                                        })
                                        newTabUid = tab.tabUid
                                    }
                                }
                            } else {
                                newTabUid = uidGen('T')
                                this._addTab({
                                    uid,
                                    tabUid: newTabUid,
                                    dbIndex,
                                    type,
                                    autorun,
                                    elementName,
                                    elementType,
                                })
                            }
                        }
                    }
                    break
                case 'data':
                    {
                        const existentTab = tempWorkspace
                            ? tempWorkspace.tabs.find(
                                  (tab) =>
                                      tab.elementName === elementName &&
                                      tab.elementType === elementType &&
                                      [`temp-${type}`, type].includes(
                                          <string>tab.type,
                                      ),
                              )
                            : false

                        if (existentTab) {
                            this._replaceTab({
                                uid,
                                tabUid: existentTab.tabUid,
                                dbIndex,
                                type,
                                elementName,
                                elementType,
                            })
                            newTabUid = existentTab.tabUid
                        } else {
                            newTabUid = uidGen('T')
                            this._addTab({
                                uid,
                                tabUid: newTabUid,
                                dbIndex,
                                type,
                                autorun,
                                elementName,
                                elementType,
                            })
                        }
                    }
                    break
                default:
                    newTabUid = uidGen('T')
                    this._addTab({
                        uid,
                        tabUid: newTabUid,
                        dbIndex,
                        type,
                        autorun,
                        elementName,
                        elementType,
                    })
                    break
            }

            this.selectTab({ uid, tabUid: newTabUid })
        },
        checkSelectedTabExists(uid: string) {
            const workspace = (this.workspaces as Workspace[]).find(
                (workspace) => workspace.uid === uid,
            )
            const isSelectedExistent = workspace
                ? workspace.tabs.some(
                      (tab) => tab.tabUid === workspace.selectedTab,
                  )
                : false

            if (!isSelectedExistent && workspace?.tabs.length)
                this.selectTab({
                    uid,
                    tabUid: <string>(
                        workspace.tabs[workspace.tabs.length - 1].tabUid
                    ),
                })
        },
        updateTabdbIndex({ uid, tabUid, type, dbIndex }: WorkspaceTab) {
            this._replaceTab({ uid, tabUid, type, dbIndex })
        },
        renameTab({ uid, dbIndex, elementNewName }) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) => {
                    if (workspace.uid === uid) {
                        return {
                            ...workspace,
                            tabs: workspace.tabs.map((tab) => {
                                if (tab.dbIndex === dbIndex) {
                                    return {
                                        ...tab,
                                        elementName: elementNewName,
                                    }
                                }

                                return tab
                            }),
                        }
                    } else return workspace
                },
            )

            persistentStore.set(
                uid,
                (this.workspaces as Workspace[]).find(
                    (workspace) => workspace.uid === uid,
                )?.tabs,
            )
        },
        removeTab({ uid, tabUid: tUid }: { uid: string; tabUid: string }) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) => {
                    if (workspace.uid === uid) {
                        return {
                            ...workspace,
                            tabs: workspace.tabs.filter(
                                (tab) => tab.tabUid !== tUid,
                            ),
                        }
                    } else return workspace
                },
            )

            persistentStore.set(
                uid,
                (this.workspaces as Workspace[]).find(
                    (workspace) => workspace.uid === uid,
                )?.tabs,
            )
            this.checkSelectedTabExists(uid)
        },
        removeTabByIndex({ uid, dbIndex }: { uid: string; dbIndex: string }) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) => {
                    if (workspace.uid === uid) {
                        return {
                            ...workspace,
                            tabs: workspace.tabs.filter(
                                (tab) => tab.dbIndex !== dbIndex,
                            ),
                        }
                    } else return workspace
                },
            )

            persistentStore.set(
                uid,
                (this.workspaces as Workspace[]).find(
                    (workspace) => workspace.uid === uid,
                )?.tabs,
            )
            this.checkSelectedTabExists(uid)
        },
        removeTabs({ uid, elementName, elementType }: WorkspaceTab) {
            // Multiple tabs based on element name
            if (elementType === 'procedure') elementType = 'routine' // TODO: pass directly "routine"

            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) => {
                    if (workspace.uid === uid) {
                        return {
                            ...workspace,
                            tabs: workspace.tabs.filter(
                                (tab) =>
                                    tab.elementName !== elementName ||
                                    tab.elementType !== elementType,
                            ),
                        }
                    } else return workspace
                },
            )

            persistentStore.set(
                uid,
                (this.workspaces as Workspace[]).find(
                    (workspace) => workspace.uid === uid,
                )?.tabs,
            )
            this.checkSelectedTabExists(uid)
        },
        selectTab({ uid, tabUid }: { uid: string; tabUid: string }) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) =>
                    workspace.uid === uid
                        ? { ...workspace, selectedTab: tabUid }
                        : workspace,
            )
        },
        selectNextTab({ uid }: { uid: string }) {
            const workspace = (this.workspaces as Workspace[]).find(
                (workspace) => workspace.uid === uid,
            )

            // workspace exist and has tab?
            if (workspace && workspace.tabs && workspace.tabs.length > 0) {
                let newIndex =
                    workspace.tabs.findIndex(
                        (tab) =>
                            tab.selected ||
                            tab.tabUid === workspace.selectedTab,
                    ) + 1

                if (newIndex > workspace.tabs.length - 1) newIndex = 0

                this.selectTab({
                    uid,
                    tabUid: <string>workspace.tabs[newIndex].tabUid,
                })
            }
        },
        selectPrevTab({ uid }: { uid: string }) {
            const workspace = (this.workspaces as Workspace[]).find(
                (workspace) => workspace.uid === uid,
            )

            // workspace exist and has tab?
            if (workspace && workspace.tabs && workspace.tabs.length > 0) {
                let newIndex =
                    workspace.tabs.findIndex(
                        (tab) =>
                            tab.selected ||
                            tab.tabUid === workspace.selectedTab,
                    ) - 1

                if (newIndex < 0) newIndex = workspace.tabs.length - 1

                this.selectTab({
                    uid,
                    tabUid: <string>workspace.tabs[newIndex].tabUid,
                })
            }
        },
        updateTabs({ uid, tabs }: { uid: string; tabs: WorkspaceTab[] }) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) =>
                    workspace.uid === uid ? { ...workspace, tabs } : workspace,
            )
            persistentStore.set(
                uid,
                (this.workspaces as Workspace[]).find(
                    (workspace) => workspace.uid === uid,
                )?.tabs,
            )
        },
        setUnsavedChanges({
            uid,
            tUid,
            isChanged,
        }: {
            uid: string
            tUid: string
            isChanged: boolean
        }) {
            this.workspaces = (this.workspaces as Workspace[]).map(
                (workspace) => {
                    if (workspace.uid === uid) {
                        return {
                            ...workspace,
                            tabs: workspace.tabs.map((tab) => {
                                if (tab.tabUid === tUid)
                                    return { ...tab, isChanged }

                                return tab
                            }),
                        }
                    } else return workspace
                },
            )
        },
    },
})
