﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;

namespace L2CommClient
{
    public class Bindable : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        public void OnPropertyChanged(string propertyName)
        {
            var handler = PropertyChanged;
            if (handler != null)
            {
                handler(this, new PropertyChangedEventArgs(propertyName));
            }
        }
    }

    #region VewModelBase
    public class ViewModelBase<T> : Bindable where T : Bindable
    {
        private T _child;
        public T Child
        {
            get { return _child; }
            set
            {
                _child = value;
                OnPropertyChanged("Child");
            }
        }

        // define UI commands
        public Command SearchCommand { get; private set; }

        // constructor
        public ViewModelBase()
        {
            // define actions like:
            SearchCommand = new Command(Search);
        }

        // Actions for the command
        private void Search()
        {
            // search logic 
        }

        // here's how enable and disable command
        //SearchCommand.IsEnabled = false; //disables the "Search" Button
        //DeleteCommand.IsEnabled = true; //enables the "Delete" Button
    }


    /**
        In WPF, it is common to use the ObservableCollection<T>, because it is a specialized collection 
        type that raises events whenever items are added / removed / cleared from it.WPF's binding 
        engine listens for such events and updates the UI accordingly.

        Since we do not know up front what type of items will be shown in the DataGrid, and our ultimate
        goal is reusability, we add another generic type parameter to parent ViewModelBase:
    **/
    public class ViewModelBase<T, TItems>: Bindable where T: Bindable where TItems: class
    {
        private T _child;
        public T Child
        {
            get { return _child; }
            set
            {
                _child = value;
                OnPropertyChanged("Child");
            }
        }

        // define UI commands
        public Command SearchCommand { get; private set; }

        // constructor
        public ViewModelBase()
        {
            // define actions like:
            SearchCommand = new Command(Search);
        }

        // Actions for the command
        private void Search()
        {
            // search logic 
        }

        // here's how enable and disable command
        //SearchCommand.IsEnabled = false; //disables the "Search" Button
        //DeleteCommand.IsEnabled = true; //enables the "Delete" Button

        private ObservableCollection<TItems> _searchResult;
        public ObservableCollection<TItems> SearchResult
        {
            get { return _searchResult; }
            set
            {
                _searchResult = value;
                OnPropertyChanged("SearchResult");
            }
        }

        private TItems _selectedItem;
        public TItems SelectItems
        {
            get { return _selectedItem; }
            set
            {
                _selectedItem = value;
                
                // SearchCommand.IsEnabled = value != null;
                // ....
            }
        }
    }


    #endregion


    // implementation of ICommand
    // Servers as an abstraction of Actions performed by the user via UI interaction
    public class Command : ICommand
    {
        public Action Action { get; set; }

        public void Execute(object parameter)
        {
            Action?.Invoke();
        }

        public bool CanExecute(object parameter)
        {
            return IsEnabled;
        }

        private bool _isEnabled = true;

        public bool IsEnabled
        {
            get { return _isEnabled; }
            set
            {
                _isEnabled = value;
                if (CanExecuteChanged != null)
                    CanExecuteChanged(this, EventArgs.Empty);
            }
        }

        public event EventHandler CanExecuteChanged;

        public Command(Action action)
        {
            Action = action;
        }
    }
}
