<template>
    <n-form
        ref="formRef"
        :rules="rules"
        :model="dbSettings"
        :show-require-mark="false"
        label-placement="left"
        label-width="auto"
        size="medium"
    >
        <n-grid :cols="24">
            <n-form-item-gi label="服务器地址" path="serverAddress" :span="11">
                <n-input
                    v-model:value="dbSettings.serverAddress"
                    placeholder="***********"
                    data-testid="server-address"
                />
            </n-form-item-gi>
            <n-gi :offset="1" :span="12">
                <n-text tag="div"> 数据库服务器地址 (域名或IP地址). </n-text>
            </n-gi>

            <n-form-item-gi label="端口" path="serverPort" :span="11">
                <n-input-number
                    v-model:value="dbSettings.serverPort"
                    placeholder="1433"
                    :show-button="false"
                    data-testid="server-port"
                >
                </n-input-number>
            </n-form-item-gi>
            <n-gi :offset="1" :span="12">
                <n-text tag="div"> 数据库服务器端口, 缺省为1433 </n-text>
            </n-gi>

            <n-form-item-gi label="数据库名" path="database" :span="11">
                <n-input
                    v-model:value="dbSettings.database"
                    placeholder=""
                    data-testid="database"
                >
                </n-input>
            </n-form-item-gi>
            <n-gi :offset="1" :span="12">
                <n-text tag="div"> 数据库名 </n-text>
            </n-gi>

            <n-form-item-gi label="用户名" path="username" :span="11">
                <n-input
                    v-model:value="dbSettings.username"
                    placeholder="sa"
                    data-testid="username"
                >
                </n-input>
            </n-form-item-gi>
            <n-gi :offset="1" :span="12">
                <n-text tag="div"> 用户名 </n-text>
            </n-gi>

            <n-form-item-gi label="密码" path="password" :span="11">
                <n-input
                    v-model:value="dbSettings.password"
                    step="1"
                    placeholder="8"
                    data-testid="password"
                >
                </n-input>
            </n-form-item-gi>
            <n-gi :offset="1" :span="12">
                <n-text tag="div"> 密码 </n-text>
            </n-gi>
        </n-grid>
        <div class="footer">
            <div>更改设置后需要重新启动软件</div>
            <n-button secondary type="primary" @click="handleSave">
                保存
            </n-button>
        </div>
    </n-form>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSettingsStore } from '@/stores/settings'
import { FormInst, useMessage } from 'naive-ui'
import { DbSettings } from '@common/interfaces/settings'

const formRef = ref<FormInst | null>(null)
const message = useMessage()

const settingsStore = useSettingsStore()
const dbSettings = ref(<DbSettings>{})

// form rules
const rules = {
    serverAddress: {
        type: 'string',
        required: true,
        trigger: ['blur', 'change'],
        message: '请输入数据服务器地址',
    },
    database: {
        type: 'string',
        required: true,
        trigger: ['blur', 'change'],
        message: '请输入数据库名',
    },
    username: {
        type: 'string',
        required: true,
        trigger: ['blur', 'change'],
        message: '请输入用户名',
    },
}

onMounted(async () => {
    dbSettings.value = await settingsStore.getDbSettings()
})

const handleSave = async (e: MouseEvent) => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'verbose',
        'Save database settings',
    )

    e.preventDefault()
    formRef.value?.validate(async (errors) => {
        if (!errors) {
            if (
                dbSettings.value.serverPort === null ||
                dbSettings.value.serverPort === 0
            ) {
                dbSettings.value.serverPort = 1433
            }
            try {
                // 保存设置
                await settingsStore.setDbSettings(dbSettings.value)
                message.success('保存成功')
            } catch (error) {
                window.electron.ipcRenderer.invoke(
                    'add-log',
                    'error',
                    '保存数据库设置失败:',
                    error,
                )
                message.error('保存失败,请检查设置并重试')
            }
        }
    })
}
</script>

<style lang="scss" scoped>
.n-text {
    margin: 6px;
    font-style: italic;
    font-size: 0.8rem;
}

.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
