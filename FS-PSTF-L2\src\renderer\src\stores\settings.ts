import { defineStore } from 'pinia'
import { useElectronStore } from '@/composables'

const persistentStore = useElectronStore('settings')

export interface AppSettings {
    quoteExplorebarSize: number
    datalogExplorebarSize: number
}

export const useSettingsStore = defineStore('settings', {
    state: () => ({
        // app settings
        appSettings: persistentStore.get('app-settings', {
            // 侧栏宽度
            quoteExplorebarSize: 250,
            datalogExplorebarSize: 150,
        }) as AppSettings,
    }),
    actions: {
        setQuoteExplorebarSize(size: number) {
            this.appSettings.quoteExplorebarSize = size
            persistentStore.set('app-settings', this.appSettings)
        },
        setDatalogExplorebarSize(size: number) {
            this.appSettings.datalogExplorebarSize = size
            persistentStore.set('app-settings', this.appSettings)
        },
        setAppSettings(settings: AppSettings) {
            this.appSettings = settings
            persistentStore.set('app-settings', settings)
        },
        async getDbSettings() {
            return await window.electron.ipcRenderer.invoke('get-db-settings')
        },
        async setDbSettings(settings: any) {
            const serializedSettings = JSON.parse(JSON.stringify(settings))
            await window.electron.ipcRenderer.invoke(
                'set-db-settings',
                serializedSettings,
            )
        },
    },
})
