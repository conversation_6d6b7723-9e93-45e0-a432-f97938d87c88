<template>
    <div class="flex justify-between items-center px-8">
        <div class="flex gap-4">
            <div class="i-mdi:database-check-outline text-20" />
            <div class="ele-text">
                {{
                    connectionStatus
                        ? '已连接数据库服务器'
                        : '未连接数据库服务器'
                }}
                {{ dbSettings.serverAddress }}
            </div>
            <div class="h-20 w-1 bg-#ccc mx-4"></div>
            <!-- <n-icon size="20">
                <plc-icon />
            </n-icon>
            <div class="ele-text">
                {{ plcConnectionStatus ? '已连接PLC' : '未连接PLC' }}
            </div> -->
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useSettingsStore } from '@/stores/settings';
import { DbSettings } from '@common/interfaces/settings';

const connectionStatus = ref(false);
//const plcConnectionStatus = ref(false)

// settings
const settingsStore = useSettingsStore();
const dbSettings = ref(<DbSettings>{});

const updateConnectionStatus = async () => {
    connectionStatus.value = await window.electron.ipcRenderer.invoke(
        'get-connection-status'
    );
    //     plcConnectionStatus.value = await window.electron.ipcRenderer.invoke(
    //         'get-plc-connection-status',
    //     )
};

let intervalId: NodeJS.Timeout;

onMounted(async () => {
    // get dbSettings
    dbSettings.value = await settingsStore.getDbSettings();

    updateConnectionStatus();
    intervalId = setInterval(updateConnectionStatus, 5000);
});

onUnmounted(() => {
    if (intervalId) {
        clearInterval(intervalId);
    }
});
</script>
