﻿using Serilog;
using System.Net;
using System.Windows.Media;


namespace L2CommClient
{
    //要实现绑定到变量，必须实现INotifyPropertyChanged  
    public class ServerInfo : Bindable
    {
        // Creates or loads an INI file in the same directory as your executable
        private IniFile MyIni = new IniFile("Settings.ini");

        public ServerInfo()
        {
            ReadIniFile();
        }

        /// <summary>
        /// read server info from Ini file
        /// </summary>
        public void ReadIniFile()
        {
            // read ini file
            try
            {
                string ipaddress = MyIni.Read("IP", "SERVER");
                // in case can not read IP
                if (ipaddress != string.Empty)
                    IpAddress = ipaddress;
                else
                    IpAddress = "127.0.0.1";
            }
            catch
            {
                Log.Fatal("Cannot read IP address from Settings.ini");
            }
            try
            {
                string port_num = MyIni.Read("PORT", "SERVER");
                // in case can not read PORT
                if (port_num != string.Empty)
                    Port = int.Parse(port_num);
                else
                    Port = 2000;                
            }
            catch
            {
                Log.Fatal("Cannot read Port from Settings.ini");
            }
        }

        /// <summary>
        /// Write server info into Ini file
        /// </summary>
        public void WriteIniFile()
        {
            try
            {
                // write settings before close
                MyIni.Write("IP", IpAddress, "SERVER");
                MyIni.Write("PORT", Port.ToString(), "SERVER");
            }
            catch
            {
                Log.Error("Cannot write INI file.");
            }
        }


        /// <summary>
        /// Server IP address 
        /// </summary>
        private string _ipAddress = "";

        /// <summary>
        /// Port to listen / connect to 
        /// </summary>
        private int _port = 0;

        private bool _isEnabledIpAddress;
        private bool _isEnabledPort;

        public string IpAddress
        {
            //获取值时将私有字段传出；  
            get { return _ipAddress; }
            set
            {
                //赋值时将值传给私有字段  
                _ipAddress = value;
                //一旦执行了赋值操作说明其值被修改了，则立马通过INotifyPropertyChanged接口告诉UI(IntValue)被修改了  
                OnPropertyChanged("IpAddress");
            }
        }

        public int Port
        {
            //获取值时将私有字段传出；  
            get { return _port; }
            set
            {
                //赋值时将值传给私有字段  
                _port = value;
                //一旦执行了赋值操作说明其值被修改了，则立马通过INotifyPropertyChanged接口告诉UI(IntValue)被修改了  
                OnPropertyChanged("Port");
            }
        }

        public bool IsEnabledIpAddress
        {
            get { return _isEnabledIpAddress; }
            set
            {
                _isEnabledIpAddress = value;
                OnPropertyChanged("IsEnabledIpAddress");
            }
        }

        public bool IsEnabledPort
        {
            get { return _isEnabledPort; }
            set
            {
                _isEnabledPort = value;
                OnPropertyChanged("IsEnabledPort");
            }
        }


        private string _buttonCaption;
        private string _serverStateIcon;
        private Brush _serverStateIconColor;

        public string ButtonCaption
        {
            //获取值时将私有字段传出；  
            get { return _buttonCaption; }
            set
            {
                //赋值时将值传给私有字段  
                _buttonCaption = value;
                //一旦执行了赋值操作说明其值被修改了，则立马通过INotifyPropertyChanged接口告诉UI(IntValue)被修改了  
                OnPropertyChanged("ButtonCaption");
            }
        }

        public string ServerStateIcon
        {
            get { return _serverStateIcon; }
            set
            {
                _serverStateIcon = value;
                OnPropertyChanged("ServerStateIcon");
            }
        }

        public Brush ServerStateIconColor
        {
            get { return _serverStateIconColor; }
            set
            {
                _serverStateIconColor = value;
                OnPropertyChanged("ServerStateIconColor");
            }
        }


    }
}
