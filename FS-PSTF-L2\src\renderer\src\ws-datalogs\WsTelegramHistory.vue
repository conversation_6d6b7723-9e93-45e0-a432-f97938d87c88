<template>
    <div v-show="isSelected" class="panel">
        <n-space vertical>
            <!--
            <n-card bordered embedded>
                <n-grid :cols="4">
                    <n-gi>
                        <n-statistic label="记录数量">
                            {{ totalRecords }}
                        </n-statistic>  
                    </n-gi>
                </n-grid>
            </n-card>
        -->
            <n-space justify="space-between">
                <div style="width: 460px">
                    <n-form
                        :show-require-mark="false"
                        :show-feedback="false"
                        label-width="90px"
                        label-placement="left"
                        size="small"
                    >
                        <n-space>
                            <n-form-item
                                label="查找报文号"
                                path="tel_no"
                                style="width: 180px"
                            >
                                <n-input-number
                                    v-model:value="inputFilter.tel_no"
                                    @update:value="
                                        (value) => updateFilter('tel_no', value)
                                    "
                                    :show-button="false"
                                    placeholder="报文号"
                                    :clearable="true"
                                    data-testid="input-filter-tel-no"
                                />
                            </n-form-item>
                            <n-form-item
                                label="查找日期"
                                path="date"
                                style="width: 240px"
                            >
                                <n-date-picker
                                    v-model:value="inputFilter.date"
                                    @update:value="
                                        (value) => updateFilter('date', value)
                                    "
                                    type="date"
                                    placeholder="日期"
                                    data-testid="input-filter-date"
                                    :clearable="true"
                                />
                            </n-form-item>
                        </n-space>
                    </n-form>
                </div>
                <AutoRefresh
                    :default-refresh="true"
                    :default-interval="30"
                    @refresh="refreshData"
                />
            </n-space>
            <n-data-table
                remote
                :row-key="(row) => row.id"
                :columns="columns"
                :data="telegrams"
                :loading="loading"
                :pagination="paginationRef"
                :on-update:page="handlePageChange"
                :single-line="false"
                titleAlign="center"
                striped
                size="small"
            />
        </n-space>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { DataTableColumns, NSpace } from 'naive-ui'
import {
    dbTelegramHistory,
    TelegramHistory,
    TeleHistoryFilterOptions,
} from '@/models'
import { useDataTable, useFilter } from '@/composables'
// UI components
import { AutoRefresh } from './components'

// 接收父组件传递过来的值
defineProps({
    tabUid: String,
    isSelected: Boolean,
})

const fetchData = async (start: number, pageSize: number) => {
    return await dbTelegramHistory.fetch(
        start,
        pageSize,
        'desc',
        inputFilter.value,
    )
}

const fetchCount = async () => {
    return await dbTelegramHistory.count(inputFilter.value)
}

const mapTelegram = (tele: TelegramHistory) => {
    return {
        ...tele,
    }
}

const {
    data,
    loading,
    pagination: paginationRef,
    handlePageChange,
    refreshData,
    renderTableTitle,
} = useDataTable<TelegramHistory>(fetchData, fetchCount, mapTelegram, 15)

// setup filter
const { filter: inputFilter, updateFilter } =
    useFilter<TeleHistoryFilterOptions>(refreshData)

const telegrams = computed(() => {
    let tempTelegrams

    // add extra columns
    tempTelegrams = data.value?.map((tele) => ({
        ...tele,
        isEdit: false,
        isDeletable: false,
    }))
    return tempTelegrams
})

const createColumns = (): DataTableColumns<TelegramHistory> => {
    const columns = [
        {
            title: renderTableTitle('#'),
            key: 'id',
            width: 60,
            className: 'center',
        },
        {
            title: renderTableTitle('方向'),
            key: 'direction',
            width: 80,
            className: 'center',
        },
        {
            title: renderTableTitle('报文号'),

            key: 'tel_no',
            width: 80,
            className: 'center',
        },
        {
            title: renderTableTitle('发送方'),
            key: 'send_id',
            width: 80,
            className: 'center',
        },
        {
            title: renderTableTitle('接收方'),
            key: 'rec_id',
            width: 80,
            className: 'center',
        },
        {
            title: renderTableTitle('报文序号'),
            key: 'tel_counter',
            width: 100,
            className: 'center',
        },
        {
            title: renderTableTitle('发送日期'),
            key: 'create_time',
            width: 200,
            className: 'center',
            render: (rowData: TelegramHistory, _rowIndex: number) => {
                return rowData.record_time.toLocaleString('zh-CN', {
                    timeZone: 'UTC',
                })
            },
        },
    ]
    // no buttons
    // define buttons column
    // const buttonsCol = {
    //     key: 'options',
    //     width: 100,
    //     title: renderTableTitle('选项'),
    // }

    return [...columns] as unknown as DataTableColumns<TelegramHistory>
}

// create columns
const columns = ref<DataTableColumns<TelegramHistory>>()
columns.value = createColumns()

onMounted(async () => {
    refreshData()
})
</script>

<style lang="scss" scoped>
.panel {
    padding: 0;
    margin: 15px;
    height: calc(100vh - 70px - 64px);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    overflow: auto;

    :deep(.n-data-table-td) {
        padding: 6px 6px;
    }

    :deep(.deleting td) {
        background-color: rgba(var(--warning-color), 0.1) !important;
        font-weight: bold;
    }

    :deep(.center) {
        text-align: center;
    }
}
</style>
