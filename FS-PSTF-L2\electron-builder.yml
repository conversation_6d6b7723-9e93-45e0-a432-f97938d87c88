appId: com.tuobang.pstf-l2
productName: 抚顺特钢板材厂固溶炉L2
directories:
    buildResources: build
files:
    - '!**/.vscode/*'
    - '!src/*'
    - '!electron.vite.config.{js,ts,mjs,cjs}'
    - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
    - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
    - '!db/*'
extraFiles:
    - 'assets/**/*'
asarUnpack:
    - resources/*
icon: build/icon.ico
win:
    executableName: pstf-l2
nsis:
    artifactName: ${name}-${version}-setup.${ext}
    shortcutName: ${productName}
    uninstallDisplayName: ${productName}
    createDesktopShortcut: always
    perMachine: true
appImage:
    artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
    provider: generic
    url: https://example.com/auto-updates
electronDownload:
    mirror: https://npmmirror.com/mirrors/electron/
