import { Page } from 'playwright'
import { ElectronApplication } from 'playwright-core'
import { test, expect } from '@playwright/test'
import { mainMenu } from './constants'
import helpers from './helpers'

let appWindow: Page
let electronApp: ElectronApplication

test.beforeAll(async () => {
    const app = await helpers.launchApp()
    appWindow = app.appWindow
    electronApp = app.electronApp
})

// go to settings page
// mainmenu sequence defined in constants.ts
test('open settings page', async () => {
    await appWindow
        .getByRole('menuitem', { name: mainMenu.Settings.label })
        .click()
    // page header
    await expect(
        appWindow.locator('.n-card-header__main'),
        `expect page heading has ${mainMenu.Settings.label} `,
    ).toHaveText(mainMenu.Settings.label)

    // tabs count
    expect(
        await appWindow.locator('.n-tabs-tab__label').count(),
        `expect tabs count = ${mainMenu.Settings.tabsCount}`,
    ).toEqual(mainMenu.Settings.tabsCount)
})

const fillValueApp = [
    {
        testid: 'datalog-explorebar-size',
        role: 'textbox',
        value: 150,
    },
]

// app settings page
test('Fill app settings form and save', async () => {
    // should have 1 input box
    await expect(
        appWindow.getByRole('textbox'),
        'App settings page should have 1 setting input boxes',
    ).toHaveCount(1)
    // fill form
    await helpers.fillForm(appWindow, fillValueApp)
    // click save
    await appWindow.getByRole('button', { name: '保存' }).click()
    // wait 0.5s
    await helpers.delay(500)
    // expect message
    await expect(
        appWindow.locator('.n-message__content'),
        'expect message 保存成功',
    ).toHaveText('保存成功')
})

// change setting
test('open database settings tab', async () => {
    // click on the quote tab
    await appWindow.locator('.n-tabs-tab-wrapper').nth(1).click()
    // check tab text
    await expect(
        appWindow.locator('[data-name="database"]'),
        'expect tab 数据库 to be activated',
    ).toHaveClass(/n-tabs-tab--active/)
})

const fillValueDatabase = [
    {
        testid: 'server-address',
        role: 'textbox',
        value: '***************',
    },
    {
        testid: 'server-port',
        role: 'textbox',
        value: 1433,
    },
    {
        testid: 'database',
        role: 'textbox',
        value: 'FS-PSTF-L2',
    },
    {
        testid: 'username',
        role: 'textbox',
        value: 'sa',
    },
    {
        testid: 'password',
        role: 'textbox',
        value: 'h3r3w3g0#',
    },
]
// fill form and save
test('fill form and save', async () => {
    // should have 5 input box
    await expect(
        appWindow.getByRole('textbox'),
        'should have 5 setting input boxes',
    ).toHaveCount(5)
    // fill form
    await helpers.fillForm(appWindow, fillValueDatabase)
    // click save
    await appWindow.getByRole('button', { name: '保存' }).click()
    // wait 0.5s
    await helpers.delay(500)
    // expect message
    // message is <div class="n-message__content">保存成功</div>
    await expect(
        appWindow.getByText('保存成功'),
        'expect message 保存成功 * 2',
    ).toHaveCount(2)
})

// switch to info page, then come back. should read same value
test('switch out and back, read saved value', async () => {
    // go to info page
    await appWindow.getByRole('menuitem', { name: mainMenu.Info.label }).click()
    await helpers.delay(100)
    // go to settings page
    await appWindow
        .getByRole('menuitem', { name: mainMenu.Settings.label })
        .click()
    // click on the quote tab
    await appWindow.locator('.n-tabs-tab-wrapper').nth(1).click()
    // verify form
    await helpers.verifyForm(appWindow, fillValueDatabase)
})

test.afterAll(async () => {
    await electronApp.close()
})
