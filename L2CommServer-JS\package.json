{"name": "l2commserver-js", "productName": "L2 Communication Server", "version": "1.0.0", "description": "L2 Communication Server - JavaScript/Electron Implementation", "type": "module", "main": "./out/main/index.js", "author": "L2CommServer Team", "homepage": "https://www.example.com", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs --fix", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:win": "npm run build && electron-builder --win --config"}, "dependencies": {"@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "chokidar": "^3.5.3", "electron-log": "^5.4.1", "electron-store": "^8.2.0", "ini": "^5.0.0", "knex": "^3.1.0", "pinia": "^3.0.3", "tedious": "^18.6.1"}, "devDependencies": {"@rushstack/eslint-patch": "^1.11.0", "@unocss/preset-rem-to-px": "^66.3.3", "@unocss/vite": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "electron": "^35.2.1", "electron-builder": "^26.0.12", "electron-vite": "^3.1.0", "eslint": "^9.31.0", "naive-ui": "^2.42.0", "unocss": "^66.3.3", "vite": "^6.3.3", "vue": "^3.5.17"}}