<template>
    <div class="panel">
        <img class="logo" height="128" :src="appIcon" />
    </div>
</template>

<script setup lang="ts">
import appIcon from '@/images/logo-1024.png'
</script>

<style lang="scss" scoped>
.panel {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    margin: auto;
    height: 100%;
    width: 100%px;
    .logo {
        width: 128px;
        filter: grayscale(1);
        opacity: 0.5;
    }
}
</style>
