<template>
    <div class="workspace-container">
        <WsExploreBar
            is-edit
            is-add
            is-delete
            @menu-item-click="menuItemClick"
            @menu-item-dblclick="menuItemDblClick"
            @tab-rename="tabRename"
            @tab-remove="tabRemove"
        />
        <div class="workspace">
            <!--draggable
                    :list="workspace.tabs"
                    tag="n-tabs"
                    item-key="tabUid"
                    :component-data="tabCompData"
                -->
            <n-tabs
                type="card"
                size="medium"
                animated
                closable
                :value="selectedTab"
                @close="handleTabClose"
                @update:value="handleTabChange"
            >
                <template v-for="tab of workspace?.tabs" :key="tab.tabUid">
                    <n-tab
                        :name="tab.tabUid"
                        :class="tab.type?.startsWith('temp-') ? 'tab-temp' : ''"
                        @dblclick="openAsPermanentTab(tab)"
                    >
                        <div class="tab-header">
                            <div class="i-mdi:table text-20 mr-4" />
                            {{ tab.elementName }}
                        </div>
                    </n-tab>
                </template>
            </n-tabs>
            <!--
                    <template #item="{ element: tab }">
                        <n-tab
                            :name="tab.tabUid"
                            :tab="tab.elementName"
                            :class="tab.type.startsWith('temp-') ? 'tab-temp' : ''"
                            @dblclick="openAsPermanentTab(tab)"
                        />
                    </template>
                </draggable>
            -->
            <WsEmpty v-if="!workspace?.tabs.length" />
            <template v-for="tab of workspace?.tabs" :key="tab.tabUid">
                <WsTelegramHistory
                    v-if="
                        ['data', 'temp-data'].includes(tab.type as string) &&
                        tab.elementType === 'telegram-history'
                    "
                    :tab-uid="tab.tabUid"
                    :is-selected="selectedTab === tab.tabUid"
                />
                <!-- PDI -->
                <WsPdi
                    v-if="
                        ['data', 'temp-data'].includes(tab.type as string) &&
                        tab.elementType === 'pdi'
                    "
                    :tab-uid="tab.tabUid"
                    :is-selected="selectedTab === tab.tabUid"
                />
                <!-- TELE 103 -->
                <WsTelegram103
                    v-if="
                        ['data', 'temp-data'].includes(tab.type as string) &&
                        tab.elementType === 'telegram-103'
                    "
                    :tab-uid="tab.tabUid"
                    :is-selected="selectedTab === tab.tabUid"
                />
                <WsTelegram111
                    v-if="
                        ['data', 'temp-data'].includes(tab.type as string) &&
                        tab.elementType === 'telegram-111'
                    "
                    :tab-uid="tab.tabUid"
                    :is-selected="selectedTab === tab.tabUid"
                />
                <WsTelegram121
                    v-if="
                        ['data', 'temp-data'].includes(tab.type as string) &&
                        tab.elementType === 'telegram-121'
                    "
                    :tab-uid="tab.tabUid"
                    :is-selected="selectedTab === tab.tabUid"
                />
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onActivated } from 'vue'
import { storeToRefs } from 'pinia'
import eventBus from '@common/libs/eventBus'
//import draggable from 'vuedraggable'
import { useWorkspacesStore, WorkspaceTab } from '@/stores/workspaces'
// UI components
import WsExploreBar from './WsExploreBar.vue'
import WsEmpty from './WsEmpty.vue'
import WsTelegramHistory from './WsTelegramHistory.vue'
import WsPdi from './WsPdi.vue'
import WsTelegram103 from './WsTelegram103.vue'
import WsTelegram111 from './WsTelegram111.vue'
import WsTelegram121 from './WsTelegram121.vue'

const workspaceName = 'DATALOGS'
const workspacesStore = useWorkspacesStore()
const {
    getWorkspace,
    setWorkspace,
    selectTab,
    newTab,
    removeTab,
    removeTabByIndex,
    renameTab,
} = workspacesStore

// set workspace
setWorkspace(workspaceName)

const { getSelected: selectedWorkspace } = storeToRefs(workspacesStore)
const workspace = computed(() => getWorkspace(selectedWorkspace.value))

// get current selected tab
const selectedTab = computed(() => {
    return workspace.value ? workspace.value.selectedTab : null
})

// valid tab keys
const validTabKeys = [
    'telegram-history',
    'pdi',
    'telegram-103',
    'telegram-111',
    'telegram-121',
]

// event handlers
// on-menu-item-click
const menuItemClick = (key, label) => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'debug',
        'on-menu-item-click',
        key,
        label,
    )

    // add new tab
    if (validTabKeys.includes(key)) {
        addNewTab('temp-data', '', label, key)
    }
}

// on-menu-item-dblclick
const menuItemDblClick = (key, label) => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'debug',
        'on-menu-item-dblclick',
        key,
        label,
    )

    // add new tab
    if (validTabKeys.includes(key)) {
        addNewTab('data', '', label, key)
    }
}

// on-tab-rename
const tabRename = (key, label) => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'debug',
        'on-tab-rename',
        key,
        label,
    )

    renameTab({
        uid: workspaceName,
        dbIndex: key,
        elementNewName: label,
    })
}

// on-tab-remove
const tabRemove = (key) => {
    window.electron.ipcRenderer.invoke('add-log', 'debug', 'on-tab-remove', key)

    removeTabByIndex({
        uid: workspaceName,
        dbIndex: key,
    })
}

// tab functions
const handleTabClose = (tuid: string) => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'debug',
        'handleTabClose',
        tuid,
    )

    removeTab({ uid: workspaceName, tabUid: tuid })
}

// 新建Tab
const addNewTab = (
    tabType: string,
    dbIndex: string,
    elName: string,
    elType: string,
) => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'debug',
        'addNewTab',
        tabType,
        dbIndex,
        elName,
        elType,
    )

    newTab({
        uid: workspaceName,
        dbIndex: dbIndex,
        type: tabType,
        elementName: elName,
        elementType: elType,
    })
}

const handleTabChange = (value: string) => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'debug',
        'handleTabChange',
        value,
    )

    selectTab({
        uid: workspaceName,
        tabUid: value,
    })
}

// open a tab as permanent
const openAsPermanentTab = (tab: WorkspaceTab) => {
    const permanentTabs = {
        client: 'data',
        quote: 'data',
    } as { [key: string]: string }

    newTab({
        uid: workspaceName, // uid for workspace
        dbIndex: tab.dbIndex,
        type: permanentTabs[tab.elementType as string],
        elementName: tab.elementName,
        elementType: tab.elementType,
    })
}

onMounted(() => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'info',
        'Workspace DATALOGS mounted',
    )
})

onActivated(() => {
    // set window title
    eventBus.emit('update-window-title', '数据记录')
})
</script>

<style lang="scss">
.workspace-container {
    display: flex;
    height: calc(100vh - 72px);
    overflow: hidden;
}

.workspace {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .tab-temp {
        font-style: italic;
    }

    .tab-header {
        display: flex;
    }
}
</style>
