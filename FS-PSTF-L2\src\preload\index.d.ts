import { ElectronAPI } from '@electron-toolkit/preload'
import knex<PERSON><PERSON> from 'knex'

declare global {
    interface Window {
        electron: ElectronAPI
        api: myAPI
        knex: knexAPI
        store: {
            get: (storeName: string, key: string, defaultValue: any) => any
            set: (storeName: string, key: string, value: any) => void
            delete: (storeName: string, key: string) => void
        }
    }
}
