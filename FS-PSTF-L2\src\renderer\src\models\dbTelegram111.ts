import { executeQuery } from '@/models/dbUtils'

export interface Telegram111 {
    id: number | null

    mat_no: string // 坯料号
    shift_no: number // 班次
    shift_group: number // 班组
    create_time: Date
    fnc_z1_time: Date
    fnc_z2_time: Date
    fnc_z3_time: Date
    fnc_z4_time: Date
    fnc_z5_time: Date
    fnc_z6_time: Date
    discharge_time: Date
    fnc_min_temp_z0: number
    fnc_max_temp_z0: number
    fnc_min_temp_z1: number
    fnc_max_temp_z1: number
    fnc_min_temp_z2: number
    fnc_max_temp_z2: number
    fnc_min_temp_z3: number
    fnc_max_temp_z3: number
    fnc_min_temp_z4: number
    fnc_max_temp_z4: number
    fnc_min_temp_z5: number
    fnc_max_temp_z5: number
    fnc_min_temp_z6: number
    fnc_max_temp_z6: number
    total_heat_time: number

    record_time: Date // 创建时间

    // extra keys
    isEdit: boolean
    isDeletable: boolean
}

export interface Tele111FilterOptions {
    billet_semicode: number | null
    roll_no: string | null
    date: number | null
}
// extra keys that does not belong to table
//const extraKeys: (keyof Telegram111)[] = [
//    <keyof Telegram111>'isEdit',
//    <keyof Telegram111>'isDeletable',
//`]

const TableName = 'Tele111_Plate_Proc_Data'

// log function stub
async function fetchById(id: number): Promise<Telegram111 | undefined> {
    if (id > 0) {
        try {
            const result = await executeQuery(
                'SELECT * FROM ' + TableName + ' WHERE id = ? ',
                [id],
            )
            return result[0] as Telegram111
        } catch (err) {
            window.electron.ipcRenderer.invoke('add-log', 'error', err)
            return undefined
        }
    } else {
        window.electron.ipcRenderer.invoke(
            'add-log',
            'warn',
            'Fetch Telegram111 by id, but id is 0.',
        )

        return undefined
    }
}

// fetch all Telegram111s, and their quotes
async function fetch(
    pageStart: number,
    pageSize: number,
    order: string,
    filter: Tele111FilterOptions,
): Promise<Telegram111[]> {
    try {
        let query = 'SELECT * FROM ' + TableName + ' WHERE 1=1'
        const params: (string | number)[] = []

        if (
            typeof filter?.billet_semicode === 'number' &&
            filter.billet_semicode > 0
        ) {
            query += ' AND billet_semicode = ?'
            params.push(filter.billet_semicode)
        }

        if (typeof filter?.roll_no === 'string' && filter.roll_no !== '') {
            query += ' AND upper(roll_no) LIKE ? '
            params.push(`%${filter.roll_no.toUpperCase()}%`)
        }

        // 2023-01-01 = 1672502400000
        if (typeof filter?.date === 'number' && filter.date >= 1672502400000) {
            query += ` AND DATEDIFF(day, [record_time], '${new Date(
                filter.date,
            ).toLocaleDateString('zh-CN')}')=0`
        }

        query += ` ORDER BY id ${order} OFFSET ? ROWS FETCH NEXT ? ROWS ONLY`
        params.push(pageStart, pageSize)

        const telegrams = await executeQuery(query, params)
        return telegrams as Telegram111[]
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return []
    }
}

// get total record count
async function count(filter?: Tele111FilterOptions): Promise<number> {
    try {
        let query = 'SELECT COUNT(*) as count FROM ' + TableName + ' WHERE 1=1'
        const params: (string | number)[] = []

        if (
            typeof filter?.billet_semicode === 'number' &&
            filter.billet_semicode > 0
        ) {
            query += ' AND billet_semicode = ?'
            params.push(filter.billet_semicode)
        }

        if (typeof filter?.roll_no === 'string' && filter.roll_no !== '') {
            query += " AND roll_no LIKE '%?%' "
            params.push(filter.roll_no)
        }
        // 2023-01-01 = 1672502400000
        if (typeof filter?.date === 'number' && filter.date >= 1672502400000) {
            query += ` AND DATEDIFF(day, [record_time], '${new Date(
                filter.date,
            ).toLocaleDateString('zh-CN')}')=0`
        }

        const result = await executeQuery(query, params)
        return result[0].count as number
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// delete a Telegram111
async function del(id: number): Promise<number> {
    try {
        const result = await executeQuery(
            'DELETE FROM ' + TableName + ' WHERE id = ?',
            [id],
        )
        return result.affectedRows
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// export functions
export default {
    fetchById,
    fetch,
    count,
    del,
}
