<?xml version="1.0"?>
<doc>
    <assembly>
        <name>S7.Net</name>
    </assembly>
    <members>
        <member name="T:S7.Net.Conversion">
            <summary>
            Conversion methods to convert from Siemens numeric format to C# and back
            </summary>
        </member>
        <member name="M:S7.Net.Conversion.BinStringToInt32(System.String)">
            <summary>
            Converts a binary string to Int32 value
            </summary>
            <param name="txt"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Conversion.BinStringToByte(System.String)">
            <summary>
            Converts a binary string to a byte. Can return null.
            </summary>
            <param name="txt"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Conversion.ValToBinString(System.Object)">
            <summary>
            Converts the value to a binary string
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Conversion.SelectBit(System.Byte,System.Int32)">
            <summary>
            Helper to get a bit value given a byte and the bit index.
            Example: DB1.DBX0.5 -> var bytes = ReadBytes(DB1.DBW0); bool bit = bytes[0].SelectBit(5); 
            </summary>
            <param name="data"></param>
            <param name="bitPosition"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Conversion.ConvertToShort(System.UInt16)">
            <summary>
            Converts from ushort value to short value; it's used to retrieve negative values from words
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Conversion.ConvertToUshort(System.Int16)">
            <summary>
            Converts from short value to ushort value; it's used to pass negative values to DWs
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Conversion.ConvertToInt(System.UInt32)">
            <summary>
            Converts from UInt32 value to Int32 value; it's used to retrieve negative values from DBDs
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Conversion.ConvertToUInt(System.Int32)">
            <summary>
            Converts from Int32 value to UInt32 value; it's used to pass negative values to DBDs
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Conversion.ConvertToUInt(System.Single)">
            <summary>
            Converts from float to DWord (DBD)
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Conversion.ConvertToFloat(System.UInt32)">
            <summary>
            Converts from DWord (DBD) to float
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:S7.Net.COTP">
            <summary>
            COTP Protocol functions and types
            </summary>
        </member>
        <member name="T:S7.Net.COTP.TPDU">
            <summary>
            Describes a COTP TPDU (Transport protocol data unit)
            </summary>
        </member>
        <member name="M:S7.Net.COTP.TPDU.ReadAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads COTP TPDU (Transport protocol data unit) from the network stream
            See: https://tools.ietf.org/html/rfc905
            </summary>
            <param name="stream">The socket to read from</param>
            <param name="cancellationToken">A cancellation token that can be used to cancel the asynchronous operation.</param>
            <returns>COTP DPDU instance</returns>
        </member>
        <member name="T:S7.Net.COTP.TSDU">
            <summary>
            Describes a COTP TSDU (Transport service data unit). One TSDU consist of 1 ore more TPDUs
            </summary>
        </member>
        <member name="M:S7.Net.COTP.TSDU.ReadAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads the full COTP TSDU (Transport service data unit)
            See: https://tools.ietf.org/html/rfc905
            </summary>
            <param name="stream">The stream to read from</param>
            <param name="cancellationToken">A cancellation token that can be used to cancel the asynchronous operation.</param>
            <returns>Data in TSDU</returns>
        </member>
        <member name="T:S7.Net.CpuType">
            <summary>
            Types of S7 cpu supported by the library
            </summary>
        </member>
        <member name="F:S7.Net.CpuType.S7200">
            <summary>
            S7 200 cpu type
            </summary>
        </member>
        <member name="F:S7.Net.CpuType.Logo0BA8">
            <summary>
            Siemens Logo 0BA8
            </summary>
        </member>
        <member name="F:S7.Net.CpuType.S7200Smart">
            <summary>
            S7 200 Smart
            </summary>
        </member>
        <member name="F:S7.Net.CpuType.S7300">
            <summary>
            S7 300 cpu type
            </summary>
        </member>
        <member name="F:S7.Net.CpuType.S7400">
            <summary>
            S7 400 cpu type
            </summary>
        </member>
        <member name="F:S7.Net.CpuType.S71200">
            <summary>
            S7 1200 cpu type
            </summary>
        </member>
        <member name="F:S7.Net.CpuType.S71500">
            <summary>
            S7 1500 cpu type
            </summary>
        </member>
        <member name="T:S7.Net.ErrorCode">
            <summary>
            Types of error code that can be set after a function is called
            </summary>
        </member>
        <member name="F:S7.Net.ErrorCode.NoError">
            <summary>
            The function has been executed correctly
            </summary>
        </member>
        <member name="F:S7.Net.ErrorCode.WrongCPU_Type">
            <summary>
            Wrong type of CPU error
            </summary>
        </member>
        <member name="F:S7.Net.ErrorCode.ConnectionError">
            <summary>
            Connection error
            </summary>
        </member>
        <member name="F:S7.Net.ErrorCode.IPAddressNotAvailable">
            <summary>
            Ip address not available
            </summary>
        </member>
        <member name="F:S7.Net.ErrorCode.WrongVarFormat">
            <summary>
            Wrong format of the variable
            </summary>
        </member>
        <member name="F:S7.Net.ErrorCode.WrongNumberReceivedBytes">
            <summary>
            Wrong number of received bytes
            </summary>
        </member>
        <member name="F:S7.Net.ErrorCode.SendData">
            <summary>
            Error on send data
            </summary>
        </member>
        <member name="F:S7.Net.ErrorCode.ReadData">
            <summary>
            Error on read data
            </summary>
        </member>
        <member name="F:S7.Net.ErrorCode.WriteData">
            <summary>
            Error on write data
            </summary>
        </member>
        <member name="T:S7.Net.DataType">
            <summary>
            Types of memory area that can be read
            </summary>
        </member>
        <member name="F:S7.Net.DataType.Input">
            <summary>
            Input area memory
            </summary>
        </member>
        <member name="F:S7.Net.DataType.Output">
            <summary>
            Output area memory
            </summary>
        </member>
        <member name="F:S7.Net.DataType.Memory">
            <summary>
            Merkers area memory (M0, M0.0, ...)
            </summary>
        </member>
        <member name="F:S7.Net.DataType.DataBlock">
            <summary>
            DB area memory (DB1, DB2, ...)
            </summary>
        </member>
        <member name="F:S7.Net.DataType.Timer">
            <summary>
            Timer area memory(T1, T2, ...)
            </summary>
        </member>
        <member name="F:S7.Net.DataType.Counter">
            <summary>
            Counter area memory (C1, C2, ...)
            </summary>
        </member>
        <member name="T:S7.Net.VarType">
            <summary>
            Types
            </summary>
        </member>
        <member name="F:S7.Net.VarType.Bit">
            <summary>
            S7 Bit variable type (bool)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.Byte">
            <summary>
            S7 Byte variable type (8 bits)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.Word">
            <summary>
            S7 Word variable type (16 bits, 2 bytes)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.DWord">
            <summary>
            S7 DWord variable type (32 bits, 4 bytes)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.Int">
            <summary>
            S7 Int variable type (16 bits, 2 bytes)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.DInt">
            <summary>
            DInt variable type (32 bits, 4 bytes)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.Real">
            <summary>
            Real variable type (32 bits, 4 bytes)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.LReal">
            <summary>
            LReal variable type (64 bits, 8 bytes)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.String">
            <summary>
            Char Array / C-String variable type (variable)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.S7String">
            <summary>
            S7 String variable type (variable)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.S7WString">
            <summary>
            S7 WString variable type (variable)
            </summary>
        </member>
        <member name="F:S7.Net.VarType.Timer">
            <summary>
            Timer variable type
            </summary>
        </member>
        <member name="F:S7.Net.VarType.Counter">
            <summary>
            Counter variable type
            </summary>
        </member>
        <member name="F:S7.Net.VarType.DateTime">
            <summary>
            DateTIme variable type
            </summary>
        </member>
        <member name="F:S7.Net.VarType.DateTimeLong">
            <summary>
            DateTimeLong variable type
            </summary>
        </member>
        <member name="T:S7.Net.Plc">
            <summary>
            Creates an instance of S7.Net driver
            </summary>
            <summary>
            Creates an instance of S7.Net driver
            </summary>
        </member>
        <member name="F:S7.Net.Plc.DefaultPort">
            <summary>
            The default port for the S7 protocol.
            </summary>
        </member>
        <member name="F:S7.Net.Plc.DefaultTimeout">
            <summary>
            The default timeout (in milliseconds) used for <see cref="P:ReadTimeout"/> and <see cref="P:WriteTimeout"/>.
            </summary>
        </member>
        <member name="P:S7.Net.Plc.IP">
            <summary>
            IP address of the PLC
            </summary>
        </member>
        <member name="P:S7.Net.Plc.Port">
            <summary>
            PORT Number of the PLC, default is 102
            </summary>
        </member>
        <member name="P:S7.Net.Plc.TsapPair">
            <summary>
            The TSAP addresses used during the connection request.
            </summary>
        </member>
        <member name="P:S7.Net.Plc.CPU">
            <summary>
            CPU type of the PLC
            </summary>
        </member>
        <member name="P:S7.Net.Plc.Rack">
            <summary>
            Rack of the PLC
            </summary>
        </member>
        <member name="P:S7.Net.Plc.Slot">
            <summary>
            Slot of the CPU of the PLC
            </summary>
        </member>
        <member name="P:S7.Net.Plc.MaxPDUSize">
            <summary>
            Max PDU size this cpu supports
            </summary>
        </member>
        <member name="P:S7.Net.Plc.ReadTimeout">
            <summary>Gets or sets the amount of time that a read operation blocks waiting for data from PLC.</summary>
            <returns>A <see cref="T:System.Int32" /> that specifies the amount of time, in milliseconds, that will elapse before a read operation fails. The default value, <see cref="F:System.Threading.Timeout.Infinite" />, specifies that the read operation does not time out.</returns>
        </member>
        <member name="P:S7.Net.Plc.WriteTimeout">
            <summary>Gets or sets the amount of time that a write operation blocks waiting for data to PLC. </summary>
            <returns>A <see cref="T:System.Int32" /> that specifies the amount of time, in milliseconds, that will elapse before a write operation fails. The default value, <see cref="F:System.Threading.Timeout.Infinite" />, specifies that the write operation does not time out.</returns>
        </member>
        <member name="P:S7.Net.Plc.IsConnected">
             <summary>
             Gets a value indicating whether a connection to the PLC has been established.
             </summary>
             <remarks>
             The <see cref="P:S7.Net.Plc.IsConnected"/> property gets the connection state of the Client socket as
             of the last I/O operation. When it returns <c>false</c>, the Client socket was either
             never  connected, or is no longer connected.
            
             <para>
             Because the <see cref="P:S7.Net.Plc.IsConnected"/> property only reflects the state of the connection
             as of the most recent operation, you should attempt to send or receive a message to
             determine the current state. After the message send fails, this property no longer
             returns <c>true</c>. Note that this behavior is by design. You cannot reliably test the
             state of the connection because, in the time between the test and a send/receive, the
             connection could have been lost. Your code should assume the socket is connected, and
             gracefully handle failed transmissions.
             </para>
             </remarks>
        </member>
        <member name="M:S7.Net.Plc.#ctor(S7.Net.CpuType,System.String,System.Int16,System.Int16)">
            <summary>
            Creates a PLC object with all the parameters needed for connections.
            For S7-1200 and S7-1500, the default is rack = 0 and slot = 0.
            You need slot > 0 if you are connecting to external ethernet card (CP).
            For S7-300 and S7-400 the default is rack = 0 and slot = 2.
            </summary>
            <param name="cpu">CpuType of the PLC (select from the enum)</param>
            <param name="ip">Ip address of the PLC</param>
            <param name="rack">rack of the PLC, usually it's 0, but check in the hardware configuration of Step7 or TIA portal</param>
            <param name="slot">slot of the CPU of the PLC, usually it's 2 for S7300-S7400, 0 for S7-1200 and S7-1500.
             If you use an external ethernet card, this must be set accordingly.</param>
        </member>
        <member name="M:S7.Net.Plc.#ctor(S7.Net.CpuType,System.String,System.Int32,System.Int16,System.Int16)">
            <summary>
            Creates a PLC object with all the parameters needed for connections.
            For S7-1200 and S7-1500, the default is rack = 0 and slot = 0.
            You need slot > 0 if you are connecting to external ethernet card (CP).
            For S7-300 and S7-400 the default is rack = 0 and slot = 2.
            </summary>
            <param name="cpu">CpuType of the PLC (select from the enum)</param>
            <param name="ip">Ip address of the PLC</param>
            <param name="port">Port number used for the connection, default 102.</param>
            <param name="rack">rack of the PLC, usually it's 0, but check in the hardware configuration of Step7 or TIA portal</param>
            <param name="slot">slot of the CPU of the PLC, usually it's 2 for S7300-S7400, 0 for S7-1200 and S7-1500.
             If you use an external ethernet card, this must be set accordingly.</param>
        </member>
        <member name="M:S7.Net.Plc.#ctor(System.String,S7.Net.Protocol.TsapPair)">
            <summary>
            Creates a PLC object with all the parameters needed for connections.
            For S7-1200 and S7-1500, the default is rack = 0 and slot = 0.
            You need slot > 0 if you are connecting to external ethernet card (CP).
            For S7-300 and S7-400 the default is rack = 0 and slot = 2.
            </summary>
            <param name="ip">Ip address of the PLC</param>
            <param name="tsapPair">The TSAP addresses used for the connection request.</param>
        </member>
        <member name="M:S7.Net.Plc.#ctor(System.String,System.Int32,S7.Net.Protocol.TsapPair)">
            <summary>
            Creates a PLC object with all the parameters needed for connections. Use this constructor
            if you want to manually override the TSAP addresses used during the connection request.
            </summary>
            <param name="ip">Ip address of the PLC</param>
            <param name="port">Port number used for the connection, default 102.</param>
            <param name="tsapPair">The TSAP addresses used for the connection request.</param>
        </member>
        <member name="M:S7.Net.Plc.Close">
            <summary>
            Close connection to PLC
            </summary>
        </member>
        <member name="M:S7.Net.Plc.Dispose(System.Boolean)">
            <summary>
            Dispose Plc Object
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:S7.Net.Plc.OpenAsync(System.Threading.CancellationToken)">
            <summary>
            Connects to the PLC and performs a COTP ConnectionRequest and S7 CommunicationSetup.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that the cancellation will not affect opening the socket in any way and only affects data transfers for configuring the connection after the socket connection is successfully established.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous open operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadBytesAsync(S7.Net.DataType,System.Int32,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads a number of bytes from a DB starting from a specified index. This handles more than 200 bytes with multiple requests.
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="count">Byte count, if you want to read 120 bytes, set this to 120.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>Returns the bytes in an array</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadBytesAsync(System.Memory{System.Byte},S7.Net.DataType,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads a number of bytes from a DB starting from a specified index. This handles more than 200 bytes with multiple requests.
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="buffer">Buffer to receive the read bytes. The <see cref="P:System.Memory`1.Length"/> determines the number of bytes to read.</param>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>Returns the bytes in an array</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadAsync(S7.Net.DataType,System.Int32,System.Int32,S7.Net.VarType,System.Int32,System.Byte,System.Threading.CancellationToken)">
            <summary>
            Read and decode a certain number of bytes of the "VarType" provided.
            This can be used to read multiple consecutive variables of the same type (Word, DWord, Int, etc).
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="varType">Type of the variable/s that you are reading</param>
            <param name="bitAdr">Address of bit. If you want to read DB1.DBX200.6, set 6 to this parameter.</param>
            <param name="varCount"></param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
        </member>
        <member name="M:S7.Net.Plc.ReadAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Reads a single variable from the PLC, takes in input strings like "DB1.DBX0.0", "DB20.DBD200", "MB20", "T45", etc.
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="variable">Input strings like "DB1.DBX0.0", "DB20.DBD200", "MB20", "T45", etc.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>Returns an object that contains the value. This object must be cast accordingly.</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadStructAsync(System.Type,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads all the bytes needed to fill a struct in C#, starting from a certain address, and return an object that can be casted to the struct.
            </summary>
            <param name="structType">Type of the struct to be readed (es.: TypeOf(MyStruct)).</param>
            <param name="db">Address of the DB.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>Returns a struct that must be cast.</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadStructAsync``1(System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads all the bytes needed to fill a struct in C#, starting from a certain address, and returns the struct or null if nothing was read.
            </summary>
            <typeparam name="T">The struct type</typeparam>
            <param name="db">Address of the DB.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>Returns a nulable struct. If nothing was read null will be returned.</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadClassAsync(System.Object,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads all the bytes needed to fill a class in C#, starting from a certain address, and set all the properties values to the value that are read from the PLC.
            This reads only properties, it doesn't read private variable or public variable without {get;set;} specified.
            </summary>
            <param name="sourceClass">Instance of the class that will store the values</param>
            <param name="db">Index of the DB; es.: 1 is for DB1</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>The number of read bytes</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadClassAsync``1(System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads all the bytes needed to fill a class in C#, starting from a certain address, and set all the properties values to the value that are read from the PLC.
            This reads only properties, it doesn't read private variable or public variable without {get;set;} specified. To instantiate the class defined by the generic
            type, the class needs a default constructor.
            </summary>
            <typeparam name="T">The class that will be instantiated. Requires a default constructor</typeparam>
            <param name="db">Index of the DB; es.: 1 is for DB1</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>An instance of the class with the values read from the PLC. If no data has been read, null will be returned</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadClassAsync``1(System.Func{``0},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads all the bytes needed to fill a class in C#, starting from a certain address, and set all the properties values to the value that are read from the PLC.
            This reads only properties, it doesn't read private variable or public variable without {get;set;} specified.
            </summary>
            <typeparam name="T">The class that will be instantiated</typeparam>
            <param name="classFactory">Function to instantiate the class</param>
            <param name="db">Index of the DB; es.: 1 is for DB1</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>An instance of the class with the values read from the PLC. If no data has been read, null will be returned</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadMultipleVarsAsync(System.Collections.Generic.List{S7.Net.Types.DataItem},System.Threading.CancellationToken)">
            <summary>
            Reads multiple vars in a single request.
            You have to create and pass a list of DataItems and you obtain in response the same list with the values.
            Values are stored in the property "Value" of the dataItem and are already converted.
            If you don't want the conversion, just create a dataItem of bytes.
            The number of DataItems as well as the total size of the requested data can not exceed a certain limit (protocol restriction).
            </summary>
            <param name="dataItems">List of dataitems that contains the list of variables that must be read.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
        </member>
        <member name="M:S7.Net.Plc.ReadStatusAsync(System.Threading.CancellationToken)">
            <summary>
            Read the current status from the PLC. A value of 0x08 indicates the PLC is in run status, regardless of the PLC type.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous operation, with it's result set to the current PLC status on completion.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteBytesAsync(S7.Net.DataType,System.Int32,System.Int32,System.Byte[],System.Threading.CancellationToken)">
            <summary>
            Write a number of bytes from a DB starting from a specified index. This handles more than 200 bytes with multiple requests.
            If the write was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to write DB1.DBW200, this is 200.</param>
            <param name="value">Bytes to write. If more than 200, multiple requests will be made.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteBytesAsync(S7.Net.DataType,System.Int32,System.Int32,System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
            <summary>
            Write a number of bytes from a DB starting from a specified index. This handles more than 200 bytes with multiple requests.
            If the write was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to write DB1.DBW200, this is 200.</param>
            <param name="value">Bytes to write. If more than 200, multiple requests will be made.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteBitAsync(S7.Net.DataType,System.Int32,System.Int32,System.Int32,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Write a single bit from a DB with the specified index.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to write DB1.DBW200, this is 200.</param>
            <param name="bitAdr">The address of the bit. (0-7)</param>
            <param name="value">Bytes to write. If more than 200, multiple requests will be made.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteBitAsync(S7.Net.DataType,System.Int32,System.Int32,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Write a single bit from a DB with the specified index.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to write DB1.DBW200, this is 200.</param>
            <param name="bitAdr">The address of the bit. (0-7)</param>
            <param name="value">Bytes to write. If more than 200, multiple requests will be made.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteAsync(S7.Net.DataType,System.Int32,System.Int32,System.Object,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Takes in input an object and tries to parse it to an array of values. This can be used to write many data, all of the same type.
            You must specify the memory area type, memory are address, byte start address and bytes count.
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="value">Bytes to write. The lenght of this parameter can't be higher than 200. If you need more, use recursion.</param>
            <param name="bitAdr">The address of the bit. (0-7)</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteAsync(System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            Writes a single variable from the PLC, takes in input strings like "DB1.DBX0.0", "DB20.DBD200", "MB20", "T45", etc.
            </summary>
            <param name="variable">Input strings like "DB1.DBX0.0", "DB20.DBD200", "MB20", "T45", etc.</param>
            <param name="value">Value to be written to the PLC</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteStructAsync(System.Object,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Writes a C# struct to a DB in the PLC
            </summary>
            <param name="structValue">The struct to be written</param>
            <param name="db">Db address</param>
            <param name="startByteAdr">Start bytes on the PLC</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteClassAsync(System.Object,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Writes a C# class to a DB in the PLC
            </summary>
            <param name="classValue">The class to be written</param>
            <param name="db">Db address</param>
            <param name="startByteAdr">Start bytes on the PLC</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is None.
            Please note that cancellation is advisory/cooperative and will not lead to immediate cancellation in all cases.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteAsync(S7.Net.Types.DataItem[])">
            <summary>
            Write DataItem(s) to the PLC. Throws an exception if the response is invalid
            or when the PLC reports errors for item(s) written.
            </summary>
            <param name="dataItems">The DataItem(s) to write to the PLC.</param>
            <returns>Task that completes when response from PLC is parsed.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteBytesWithASingleRequestAsync(S7.Net.DataType,System.Int32,System.Int32,System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
            <summary>
            Writes up to 200 bytes to the PLC. You must specify the memory area type, memory are address, byte start address and bytes count.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="value">Bytes to write. The lenght of this parameter can't be higher than 200. If you need more, use recursion.</param>
            <param name="cancellationToken">A cancellation token that can be used to cancel the asynchronous operation.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteReadHeader(System.IO.MemoryStream,System.Int32)">
            <summary>
            Creates the header to read bytes from the PLC.
            </summary>
            <param name="stream">The stream to write to.</param>
            <param name="amount">The number of items to read.</param>
        </member>
        <member name="M:S7.Net.Plc.BuildReadDataRequestPackage(System.IO.MemoryStream,S7.Net.DataType,System.Int32,System.Int32,System.Int32)">
            <summary>
            Create the bytes-package to request data from the PLC. You have to specify the memory type (dataType),
            the address of the memory, the address of the byte and the bytes count.
            </summary>
            <param name="stream">The stream to write the read data request to.</param>
            <param name="dataType">MemoryType (DB, Timer, Counter, etc.)</param>
            <param name="db">Address of the memory to be read</param>
            <param name="startByteAdr">Start address of the byte</param>
            <param name="count">Number of bytes to be read</param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Plc.ParseBytes(S7.Net.VarType,System.Byte[],System.Int32,System.Byte)">
            <summary>
            Given a S7 variable type (Bool, Word, DWord, etc.), it converts the bytes in the appropriate C# format.
            </summary>
            <param name="varType"></param>
            <param name="bytes"></param>
            <param name="varCount"></param>
            <param name="bitAdr"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Plc.VarTypeToByteLength(S7.Net.VarType,System.Int32)">
            <summary>
            Given a S7 <see cref="T:S7.Net.VarType"/> (Bool, Word, DWord, etc.), it returns how many bytes to read.
            </summary>
            <param name="varType"></param>
            <param name="varCount"></param>
            <returns>Byte lenght of variable</returns>
        </member>
        <member name="M:S7.Net.Plc.Open">
            <summary>
            Connects to the PLC and performs a COTP ConnectionRequest and S7 CommunicationSetup.
            </summary>
        </member>
        <member name="M:S7.Net.Plc.ReadBytes(S7.Net.DataType,System.Int32,System.Int32,System.Int32)">
            <summary>
            Reads a number of bytes from a DB starting from a specified index. This handles more than 200 bytes with multiple requests.
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="count">Byte count, if you want to read 120 bytes, set this to 120.</param>
            <returns>Returns the bytes in an array</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadBytes(System.Span{System.Byte},S7.Net.DataType,System.Int32,System.Int32)">
            <summary>
            Reads a number of bytes from a DB starting from a specified index. This handles more than 200 bytes with multiple requests.
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="buffer">Buffer to receive the read bytes. The <see cref="P:System.Span`1.Length"/> determines the number of bytes to read.</param>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <returns>Returns the bytes in an array</returns>
        </member>
        <member name="M:S7.Net.Plc.Read(S7.Net.DataType,System.Int32,System.Int32,S7.Net.VarType,System.Int32,System.Byte)">
            <summary>
            Read and decode a certain number of bytes of the "VarType" provided.
            This can be used to read multiple consecutive variables of the same type (Word, DWord, Int, etc).
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="varType">Type of the variable/s that you are reading</param>
            <param name="bitAdr">Address of bit. If you want to read DB1.DBX200.6, set 6 to this parameter.</param>
            <param name="varCount"></param>
        </member>
        <member name="M:S7.Net.Plc.Read(System.String)">
            <summary>
            Reads a single variable from the PLC, takes in input strings like "DB1.DBX0.0", "DB20.DBD200", "MB20", "T45", etc.
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="variable">Input strings like "DB1.DBX0.0", "DB20.DBD200", "MB20", "T45", etc.</param>
            <returns>Returns an object that contains the value. This object must be cast accordingly. If no data has been read, null will be returned</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadStruct(System.Type,System.Int32,System.Int32)">
            <summary>
            Reads all the bytes needed to fill a struct in C#, starting from a certain address, and return an object that can be casted to the struct.
            </summary>
            <param name="structType">Type of the struct to be readed (es.: TypeOf(MyStruct)).</param>
            <param name="db">Address of the DB.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <returns>Returns a struct that must be cast. If no data has been read, null will be returned</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadStruct``1(System.Int32,System.Int32)">
            <summary>
            Reads all the bytes needed to fill a struct in C#, starting from a certain address, and returns the struct or null if nothing was read.
            </summary>
            <typeparam name="T">The struct type</typeparam>
            <param name="db">Address of the DB.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <returns>Returns a nullable struct. If nothing was read null will be returned.</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadClass(System.Object,System.Int32,System.Int32)">
            <summary>
            Reads all the bytes needed to fill a class in C#, starting from a certain address, and set all the properties values to the value that are read from the PLC.
            This reads only properties, it doesn't read private variable or public variable without {get;set;} specified.
            </summary>
            <param name="sourceClass">Instance of the class that will store the values</param>
            <param name="db">Index of the DB; es.: 1 is for DB1</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <returns>The number of read bytes</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadClass``1(System.Int32,System.Int32)">
            <summary>
            Reads all the bytes needed to fill a class in C#, starting from a certain address, and set all the properties values to the value that are read from the PLC.
            This reads only properties, it doesn't read private variable or public variable without {get;set;} specified. To instantiate the class defined by the generic
            type, the class needs a default constructor.
            </summary>
            <typeparam name="T">The class that will be instantiated. Requires a default constructor</typeparam>
            <param name="db">Index of the DB; es.: 1 is for DB1</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <returns>An instance of the class with the values read from the PLC. If no data has been read, null will be returned</returns>
        </member>
        <member name="M:S7.Net.Plc.ReadClass``1(System.Func{``0},System.Int32,System.Int32)">
            <summary>
            Reads all the bytes needed to fill a class in C#, starting from a certain address, and set all the properties values to the value that are read from the PLC.
            This reads only properties, it doesn't read private variable or public variable without {get;set;} specified.
            </summary>
            <typeparam name="T">The class that will be instantiated</typeparam>
            <param name="classFactory">Function to instantiate the class</param>
            <param name="db">Index of the DB; es.: 1 is for DB1</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <returns>An instance of the class with the values read from the PLC. If no data has been read, null will be returned</returns>
        </member>
        <member name="M:S7.Net.Plc.WriteBytes(S7.Net.DataType,System.Int32,System.Int32,System.Byte[])">
            <summary>
            Write a number of bytes from a DB starting from a specified index. This handles more than 200 bytes with multiple requests.
            If the write was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to write DB1.DBW200, this is 200.</param>
            <param name="value">Bytes to write. If more than 200, multiple requests will be made.</param>
        </member>
        <member name="M:S7.Net.Plc.WriteBytes(S7.Net.DataType,System.Int32,System.Int32,System.ReadOnlySpan{System.Byte})">
            <summary>
            Write a number of bytes from a DB starting from a specified index. This handles more than 200 bytes with multiple requests.
            If the write was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to write DB1.DBW200, this is 200.</param>
            <param name="value">Bytes to write. If more than 200, multiple requests will be made.</param>
        </member>
        <member name="M:S7.Net.Plc.WriteBit(S7.Net.DataType,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Write a single bit from a DB with the specified index.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to write DB1.DBW200, this is 200.</param>
            <param name="bitAdr">The address of the bit. (0-7)</param>
            <param name="value">Bytes to write. If more than 200, multiple requests will be made.</param>
        </member>
        <member name="M:S7.Net.Plc.WriteBit(S7.Net.DataType,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Write a single bit to a DB with the specified index.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to write DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to write DB1.DBW200, this is 200.</param>
            <param name="bitAdr">The address of the bit. (0-7)</param>
            <param name="value">Value to write (0 or 1).</param>
        </member>
        <member name="M:S7.Net.Plc.Write(S7.Net.DataType,System.Int32,System.Int32,System.Object,System.Int32)">
            <summary>
            Takes in input an object and tries to parse it to an array of values. This can be used to write many data, all of the same type.
            You must specify the memory area type, memory are address, byte start address and bytes count.
            If the read was not successful, check LastErrorCode or LastErrorString.
            </summary>
            <param name="dataType">Data type of the memory area, can be DB, Timer, Counter, Merker(Memory), Input, Output.</param>
            <param name="db">Address of the memory area (if you want to read DB1, this is set to 1). This must be set also for other memory area types: counters, timers,etc.</param>
            <param name="startByteAdr">Start byte address. If you want to read DB1.DBW200, this is 200.</param>
            <param name="value">Bytes to write. The lenght of this parameter can't be higher than 200. If you need more, use recursion.</param>
            <param name="bitAdr">The address of the bit. (0-7)</param>
        </member>
        <member name="M:S7.Net.Plc.Write(System.String,System.Object)">
            <summary>
            Writes a single variable from the PLC, takes in input strings like "DB1.DBX0.0", "DB20.DBD200", "MB20", "T45", etc.
            </summary>
            <param name="variable">Input strings like "DB1.DBX0.0", "DB20.DBD200", "MB20", "T45", etc.</param>
            <param name="value">Value to be written to the PLC</param>
        </member>
        <member name="M:S7.Net.Plc.WriteStruct(System.Object,System.Int32,System.Int32)">
            <summary>
            Writes a C# struct to a DB in the PLC
            </summary>
            <param name="structValue">The struct to be written</param>
            <param name="db">Db address</param>
            <param name="startByteAdr">Start bytes on the PLC</param>
        </member>
        <member name="M:S7.Net.Plc.WriteClass(System.Object,System.Int32,System.Int32)">
            <summary>
            Writes a C# class to a DB in the PLC
            </summary>
            <param name="classValue">The class to be written</param>
            <param name="db">Db address</param>
            <param name="startByteAdr">Start bytes on the PLC</param>
        </member>
        <member name="M:S7.Net.Plc.Write(S7.Net.Types.DataItem[])">
            <summary>
            Write DataItem(s) to the PLC. Throws an exception if the response is invalid
            or when the PLC reports errors for item(s) written.
            </summary>
            <param name="dataItems">The DataItem(s) to write to the PLC.</param>
        </member>
        <member name="M:S7.Net.Plc.ReadMultipleVars(System.Collections.Generic.List{S7.Net.Types.DataItem})">
            <summary>
            Reads multiple vars in a single request.
            You have to create and pass a list of DataItems and you obtain in response the same list with the values.
            Values are stored in the property "Value" of the dataItem and are already converted.
            If you don't want the conversion, just create a dataItem of bytes.
            The number of DataItems as well as the total size of the requested data can not exceed a certain limit (protocol restriction).
            </summary>
            <param name="dataItems">List of dataitems that contains the list of variables that must be read.</param>
        </member>
        <member name="M:S7.Net.Plc.ReadStatus">
            <summary>
            Read the current status from the PLC. A value of 0x08 indicates the PLC is in run status, regardless of the PLC type.
            </summary>
            <returns>The current PLC status.</returns>
        </member>
        <member name="T:S7.Net.Protocol.S7.DataItemAddress">
            <summary>
            Represents an area of memory in the PLC
            </summary>
        </member>
        <member name="P:S7.Net.Protocol.S7.DataItemAddress.DataType">
            <summary>
            Memory area to read 
            </summary>
        </member>
        <member name="P:S7.Net.Protocol.S7.DataItemAddress.DB">
            <summary>
            Address of memory area to read (example: for DB1 this value is 1, for T45 this value is 45)
            </summary>
        </member>
        <member name="P:S7.Net.Protocol.S7.DataItemAddress.StartByteAddress">
            <summary>
            Address of the first byte to read
            </summary>
        </member>
        <member name="P:S7.Net.Protocol.S7.DataItemAddress.ByteLength">
            <summary>
            Length of data to read
            </summary>
        </member>
        <member name="T:S7.Net.Protocol.Tsap">
            <summary>
            Provides a representation of the Transport Service Access Point, or TSAP in short. TSAP's are used
            to specify a client and server address. For most PLC types a default TSAP is available that allows
            connection from any IP and can be calculated using the rack and slot numbers.
            </summary>
        </member>
        <member name="P:S7.Net.Protocol.Tsap.FirstByte">
            <summary>
            First byte of the TSAP.
            </summary>
        </member>
        <member name="P:S7.Net.Protocol.Tsap.SecondByte">
            <summary>
            Second byte of the TSAP.
            </summary>
        </member>
        <member name="M:S7.Net.Protocol.Tsap.#ctor(System.Byte,System.Byte)">
            <summary>
            Initializes a new instance of the <see cref="T:S7.Net.Protocol.Tsap" /> class using the specified values.
            </summary>
            <param name="firstByte">The first byte of the TSAP.</param>
            <param name="secondByte">The second byte of the TSAP.</param>
        </member>
        <member name="T:S7.Net.Protocol.TsapPair">
            <summary>
            Implements a pair of TSAP addresses used to connect to a PLC.
            </summary>
        </member>
        <member name="P:S7.Net.Protocol.TsapPair.Local">
            <summary>
            The local <see cref="T:S7.Net.Protocol.Tsap" />.
            </summary>
        </member>
        <member name="P:S7.Net.Protocol.TsapPair.Remote">
            <summary>
            The remote <see cref="T:S7.Net.Protocol.Tsap" />
            </summary>
        </member>
        <member name="M:S7.Net.Protocol.TsapPair.#ctor(S7.Net.Protocol.Tsap,S7.Net.Protocol.Tsap)">
            <summary>
            Initializes a new instance of the <see cref="T:S7.Net.Protocol.TsapPair" /> class using the specified local and
            remote TSAP.
            </summary>
            <param name="local">The local TSAP.</param>
            <param name="remote">The remote TSAP.</param>
        </member>
        <member name="M:S7.Net.Protocol.TsapPair.GetDefaultTsapPair(S7.Net.CpuType,System.Int32,System.Int32)">
             <summary>
             Builds a <see cref="T:S7.Net.Protocol.TsapPair" /> that can be used to connect to a PLC using the default connection
             addresses.
             </summary>
             <remarks>
             The remote TSAP is constructed using <code>new Tsap(0x03, (byte) ((rack &lt;&lt; 5) | slot))</code>.
             </remarks>
             <param name="cpuType">The CPU type of the PLC.</param>
             <param name="rack">The rack of the PLC's network card.</param>
             <param name="slot">The slot of the PLC's network card.</param>
             <returns>A TSAP pair that matches the given parameters.</returns>
             <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="cpuType"/> is invalid.
            
             -or-
            
             The <paramref name="rack"/> parameter is less than 0.
            
             -or-
            
             The <paramref name="rack"/> parameter is greater than 15.
            
             -or-
            
             The <paramref name="slot"/> parameter is less than 0.
            
             -or-
            
             The <paramref name="slot"/> parameter is greater than 15.</exception>
        </member>
        <member name="T:S7.Net.StreamExtensions">
            <summary>
            Extensions for Streams
            </summary>
        </member>
        <member name="M:S7.Net.StreamExtensions.ReadExact(System.IO.Stream,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads bytes from the stream into the buffer until exactly the requested number of bytes (or EOF) have been read
            </summary>
            <param name="stream">the Stream to read from</param>
            <param name="buffer">the buffer to read into</param>
            <param name="offset">the offset in the buffer to read into</param>
            <param name="count">the amount of bytes to read into the buffer</param>
            <returns>returns the amount of read bytes</returns>
        </member>
        <member name="M:S7.Net.StreamExtensions.ReadExactAsync(System.IO.Stream,System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads bytes from the stream into the buffer until exactly the requested number of bytes (or EOF) have been read
            </summary>
            <param name="stream">the Stream to read from</param>
            <param name="buffer">the buffer to read into</param>
            <param name="offset">the offset in the buffer to read into</param>
            <param name="count">the amount of bytes to read into the buffer</param>
            <param name="cancellationToken">A cancellation token that can be used to cancel the asynchronous operation.</param>
            <returns>returns the amount of read bytes</returns>
        </member>
        <member name="T:S7.Net.TPKT">
            <summary>
            Describes a TPKT Packet
            </summary>
        </member>
        <member name="M:S7.Net.TPKT.ReadAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads a TPKT from the socket Async
            </summary>
            <param name="stream">The stream to read from</param>
            <param name="cancellationToken">A cancellation token that can be used to cancel the asynchronous operation.</param>
            <returns>Task TPKT Instace</returns>
        </member>
        <member name="T:S7.Net.Types.Bit">
            <summary>
            Contains the conversion methods to convert Bit from S7 plc to C#.
            </summary>
        </member>
        <member name="M:S7.Net.Types.Bit.FromByte(System.Byte,System.Byte)">
            <summary>
            Converts a Bit to bool
            </summary>
        </member>
        <member name="M:S7.Net.Types.Bit.ToBitArray(System.Byte[])">
            <summary>
            Converts an array of bytes to a BitArray.
            </summary>
            <param name="bytes">The bytes to convert.</param>
            <returns>A BitArray with the same number of bits and equal values as <paramref name="bytes"/>.</returns>
        </member>
        <member name="M:S7.Net.Types.Bit.ToBitArray(System.Byte[],System.Int32)">
            <summary>
            Converts an array of bytes to a BitArray.
            </summary>
            <param name="bytes">The bytes to convert.</param>
            <param name="length">The number of bits to return.</param>
            <returns>A BitArray with <paramref name="length"/> bits.</returns>
        </member>
        <member name="T:S7.Net.Types.Boolean">
            <summary>
            Contains the methods to read, set and reset bits inside bytes
            </summary>
        </member>
        <member name="M:S7.Net.Types.Boolean.GetValue(System.Byte,System.Int32)">
            <summary>
            Returns the value of a bit in a bit, given the address of the bit
            </summary>
        </member>
        <member name="M:S7.Net.Types.Boolean.SetBit(System.Byte,System.Int32)">
            <summary>
            Sets the value of a bit to 1 (true), given the address of the bit. Returns
            a copy of the value with the bit set.
            </summary>
            <param name="value">The input value to modify.</param>
            <param name="bit">The index (zero based) of the bit to set.</param>
            <returns>The modified value with the bit at index set.</returns>
        </member>
        <member name="M:S7.Net.Types.Boolean.SetBit(System.Byte@,System.Int32)">
            <summary>
            Sets the value of a bit to 1 (true), given the address of the bit.
            </summary>
            <param name="value">The value to modify.</param>
            <param name="bit">The index (zero based) of the bit to set.</param>
        </member>
        <member name="M:S7.Net.Types.Boolean.ClearBit(System.Byte,System.Int32)">
            <summary>
            Resets the value of a bit to 0 (false), given the address of the bit. Returns
            a copy of the value with the bit cleared.
            </summary>
            <param name="value">The input value to modify.</param>
            <param name="bit">The index (zero based) of the bit to clear.</param>
            <returns>The modified value with the bit at index cleared.</returns>
        </member>
        <member name="M:S7.Net.Types.Boolean.ClearBit(System.Byte@,System.Int32)">
            <summary>
            Resets the value of a bit to 0 (false), given the address of the bit
            </summary>
            <param name="value">The input value to modify.</param>
            <param name="bit">The index (zero based) of the bit to clear.</param>
        </member>
        <member name="T:S7.Net.Types.Byte">
            <summary>
            Contains the methods to convert from bytes to byte arrays
            </summary>
        </member>
        <member name="M:S7.Net.Types.Byte.ToByteArray(System.Byte)">
            <summary>
            Converts a byte to byte array
            </summary>
        </member>
        <member name="M:S7.Net.Types.Byte.FromByteArray(System.Byte[])">
            <summary>
            Converts a byte array to byte
            </summary>
            <param name="bytes"></param>
            <returns></returns>
        </member>
        <member name="T:S7.Net.Types.Class">
            <summary>
            Contains the methods to convert a C# class to S7 data types
            </summary>
        </member>
        <member name="M:S7.Net.Types.Class.GetClassSize(System.Object,System.Double,System.Boolean)">
            <summary>
            Gets the size of the class in bytes.
            </summary>
            <param name="instance">An instance of the class</param>
            <param name="numBytes">The offset of the current field.</param>
            <param name="isInnerProperty"><see langword="true" /> if this property belongs to a class being serialized as member of the class requested for serialization; otherwise, <see langword="false" />.</param>
            <returns>the number of bytes</returns>
        </member>
        <member name="M:S7.Net.Types.Class.FromBytes(System.Object,System.Byte[],System.Double,System.Boolean)">
            <summary>
            Sets the object's values with the given array of bytes
            </summary>
            <param name="sourceClass">The object to fill in the given array of bytes</param>
            <param name="bytes">The array of bytes</param>
            <param name="numBytes">The offset for the current field.</param>
            <param name="isInnerClass"><see langword="true" /> if this class is the type of a member of the class to be serialized; otherwise, <see langword="false" />.</param>
        </member>
        <member name="M:S7.Net.Types.Class.ToBytes(System.Object,System.Byte[],System.Double)">
            <summary>
            Creates a byte array depending on the struct type.
            </summary>
            <param name="sourceClass">The struct object.</param>
            <param name="bytes">The target byte array.</param>
            <param name="numBytes">The offset for the current field.</param>
            <returns>A byte array or null if fails.</returns>
        </member>
        <member name="T:S7.Net.Types.Counter">
            <summary>
            Contains the conversion methods to convert Counter from S7 plc to C# ushort (UInt16).
            </summary>
        </member>
        <member name="M:S7.Net.Types.Counter.FromByteArray(System.Byte[])">
            <summary>
            Converts a Counter (2 bytes) to ushort (UInt16)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Counter.ToByteArray(System.UInt16)">
            <summary>
            Converts a ushort (UInt16) to word (2 bytes)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Counter.ToByteArray(System.UInt16[])">
            <summary>
            Converts an array of ushort (UInt16) to an array of bytes
            </summary>
        </member>
        <member name="M:S7.Net.Types.Counter.ToArray(System.Byte[])">
            <summary>
            Converts an array of bytes to an array of ushort
            </summary>
        </member>
        <member name="T:S7.Net.Types.DataItem">
            <summary>
            Create an instance of a memory block that can be read by using ReadMultipleVars
            </summary>
        </member>
        <member name="P:S7.Net.Types.DataItem.DataType">
            <summary>
            Memory area to read 
            </summary>
        </member>
        <member name="P:S7.Net.Types.DataItem.VarType">
            <summary>
            Type of data to be read (default is bytes)
            </summary>
        </member>
        <member name="P:S7.Net.Types.DataItem.DB">
            <summary>
            Address of memory area to read (example: for DB1 this value is 1, for T45 this value is 45)
            </summary>
        </member>
        <member name="P:S7.Net.Types.DataItem.StartByteAdr">
            <summary>
            Address of the first byte to read
            </summary>
        </member>
        <member name="P:S7.Net.Types.DataItem.BitAdr">
            <summary>
            Addess of bit to read from StartByteAdr
            </summary>
        </member>
        <member name="P:S7.Net.Types.DataItem.Count">
            <summary>
            Number of variables to read
            </summary>
        </member>
        <member name="P:S7.Net.Types.DataItem.Value">
            <summary>
            Contains the value of the memory area after the read has been executed
            </summary>
        </member>
        <member name="M:S7.Net.Types.DataItem.#ctor">
            <summary>
            Create an instance of DataItem
            </summary>
        </member>
        <member name="M:S7.Net.Types.DataItem.FromAddress(System.String)">
            <summary>
            Create an instance of <see cref="T:S7.Net.Types.DataItem"/> from the supplied address.
            </summary>
            <param name="address">The address to create the DataItem for.</param>
            <returns>A new <see cref="T:S7.Net.Types.DataItem"/> instance with properties parsed from <paramref name="address"/>.</returns>
            <remarks>The <see cref="P:S7.Net.Types.DataItem.Count" /> property is not parsed from the address.</remarks>
        </member>
        <member name="M:S7.Net.Types.DataItem.FromAddressAndValue``1(System.String,``0)">
            <summary>
            Create an instance of <see cref="T:S7.Net.Types.DataItem"/> from the supplied address and value.
            </summary>
            <param name="address">The address to create the DataItem for.</param>
            <param name="value">The value to be applied to the DataItem.</param>
            <returns>A new <see cref="T:S7.Net.Types.DataItem"/> instance with properties parsed from <paramref name="address"/> and the supplied value set.</returns>
        </member>
        <member name="T:S7.Net.Types.DateTime">
            <summary>
            Contains the methods to convert between <see cref="T:System.DateTime"/> and S7 representation of datetime values.
            </summary>
        </member>
        <member name="F:S7.Net.Types.DateTime.SpecMinimumDateTime">
            <summary>
            The minimum <see cref="T:System.DateTime"/> value supported by the specification.
            </summary>
        </member>
        <member name="F:S7.Net.Types.DateTime.SpecMaximumDateTime">
            <summary>
            The maximum <see cref="T:System.DateTime"/> value supported by the specification.
            </summary>
        </member>
        <member name="M:S7.Net.Types.DateTime.FromByteArray(System.Byte[])">
            <summary>
            Parses a <see cref="T:System.DateTime"/> value from bytes.
            </summary>
            <param name="bytes">Input bytes read from PLC.</param>
            <returns>A <see cref="T:System.DateTime"/> object representing the value read from PLC.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the length of
              <paramref name="bytes"/> is not 8 or any value in <paramref name="bytes"/>
              is outside the valid range of values.</exception>
        </member>
        <member name="M:S7.Net.Types.DateTime.ToArray(System.Byte[])">
            <summary>
            Parses an array of <see cref="T:System.DateTime"/> values from bytes.
            </summary>
            <param name="bytes">Input bytes read from PLC.</param>
            <returns>An array of <see cref="T:System.DateTime"/> objects representing the values read from PLC.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the length of
              <paramref name="bytes"/> is not a multiple of 8 or any value in
              <paramref name="bytes"/> is outside the valid range of values.</exception>
        </member>
        <member name="M:S7.Net.Types.DateTime.ToByteArray(System.DateTime)">
            <summary>
            Converts a <see cref="T:System.DateTime"/> value to a byte array.
            </summary>
            <param name="dateTime">The DateTime value to convert.</param>
            <returns>A byte array containing the S7 date time representation of <paramref name="dateTime"/>.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the value of
              <paramref name="dateTime"/> is before <see cref="P:SpecMinimumDateTime"/>
              or after <see cref="P:SpecMaximumDateTime"/>.</exception>
        </member>
        <member name="M:S7.Net.Types.DateTime.ToByteArray(System.DateTime[])">
            <summary>
            Converts an array of <see cref="T:System.DateTime"/> values to a byte array.
            </summary>
            <param name="dateTimes">The DateTime values to convert.</param>
            <returns>A byte array containing the S7 date time representations of <paramref name="dateTimes"/>.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when any value of
              <paramref name="dateTimes"/> is before <see cref="P:SpecMinimumDateTime"/>
              or after <see cref="P:SpecMaximumDateTime"/>.</exception>
        </member>
        <member name="T:S7.Net.Types.DateTimeLong">
            <summary>
            Contains the methods to convert between <see cref="T:System.DateTime" /> and S7 representation of DateTimeLong (DTL) values.
            </summary>
        </member>
        <member name="F:S7.Net.Types.DateTimeLong.SpecMinimumDateTime">
            <summary>
            The minimum <see cref="T:System.DateTime" /> value supported by the specification.
            </summary>
        </member>
        <member name="F:S7.Net.Types.DateTimeLong.SpecMaximumDateTime">
            <summary>
            The maximum <see cref="T:System.DateTime" /> value supported by the specification.
            </summary>
        </member>
        <member name="M:S7.Net.Types.DateTimeLong.FromByteArray(System.Byte[])">
            <summary>
            Parses a <see cref="T:System.DateTime" /> value from bytes.
            </summary>
            <param name="bytes">Input bytes read from PLC.</param>
            <returns>A <see cref="T:System.DateTime" /> object representing the value read from PLC.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the length of
            <paramref name="bytes" /> is not 12 or any value in <paramref name="bytes" />
            is outside the valid range of values.
            </exception>
        </member>
        <member name="M:S7.Net.Types.DateTimeLong.ToArray(System.Byte[])">
            <summary>
            Parses an array of <see cref="T:System.DateTime" /> values from bytes.
            </summary>
            <param name="bytes">Input bytes read from PLC.</param>
            <returns>An array of <see cref="T:System.DateTime" /> objects representing the values read from PLC.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the length of
            <paramref name="bytes" /> is not a multiple of 12 or any value in
            <paramref name="bytes" /> is outside the valid range of values.
            </exception>
        </member>
        <member name="M:S7.Net.Types.DateTimeLong.ToByteArray(System.DateTime)">
            <summary>
            Converts a <see cref="T:System.DateTime" /> value to a byte array.
            </summary>
            <param name="dateTime">The DateTime value to convert.</param>
            <returns>A byte array containing the S7 DateTimeLong representation of <paramref name="dateTime" />.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when the value of
            <paramref name="dateTime" /> is before <see cref="P:SpecMinimumDateTime" />
            or after <see cref="P:SpecMaximumDateTime" />.
            </exception>
        </member>
        <member name="M:S7.Net.Types.DateTimeLong.ToByteArray(System.DateTime[])">
            <summary>
            Converts an array of <see cref="T:System.DateTime" /> values to a byte array.
            </summary>
            <param name="dateTimes">The DateTime values to convert.</param>
            <returns>A byte array containing the S7 DateTimeLong representations of <paramref name="dateTimes" />.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            Thrown when any value of
            <paramref name="dateTimes" /> is before <see cref="P:SpecMinimumDateTime" />
            or after <see cref="P:SpecMaximumDateTime" />.
            </exception>
        </member>
        <member name="T:S7.Net.Types.DInt">
            <summary>
            Contains the conversion methods to convert DInt from S7 plc to C# int (Int32).
            </summary>
        </member>
        <member name="M:S7.Net.Types.DInt.FromByteArray(System.Byte[])">
            <summary>
            Converts a S7 DInt (4 bytes) to int (Int32)
            </summary>
        </member>
        <member name="M:S7.Net.Types.DInt.ToByteArray(System.Int32)">
            <summary>
            Converts a int (Int32) to S7 DInt (4 bytes)
            </summary>
        </member>
        <member name="M:S7.Net.Types.DInt.ToByteArray(System.Int32[])">
            <summary>
            Converts an array of int (Int32) to an array of bytes
            </summary>
        </member>
        <member name="M:S7.Net.Types.DInt.ToArray(System.Byte[])">
            <summary>
            Converts an array of S7 DInt to an array of int (Int32)
            </summary>
        </member>
        <member name="T:S7.Net.Types.Double">
            <summary>
            Contains the conversion methods to convert Real from S7 plc to C# double.
            </summary>
        </member>
        <member name="M:S7.Net.Types.Double.FromByteArray(System.Byte[])">
            <summary>
            Converts a S7 Real (4 bytes) to double
            </summary>
        </member>
        <member name="M:S7.Net.Types.Double.FromDWord(System.Int32)">
            <summary>
            Converts a S7 DInt to double
            </summary>
        </member>
        <member name="M:S7.Net.Types.Double.FromDWord(System.UInt32)">
            <summary>
            Converts a S7 DWord to double
            </summary>
        </member>
        <member name="M:S7.Net.Types.Double.ToByteArray(System.Double)">
            <summary>
            Converts a double to S7 Real (4 bytes)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Double.ToByteArray(System.Double[])">
            <summary>
            Converts an array of double to an array of bytes 
            </summary>
        </member>
        <member name="M:S7.Net.Types.Double.ToArray(System.Byte[])">
            <summary>
            Converts an array of S7 Real to an array of double
            </summary>
        </member>
        <member name="T:S7.Net.Types.DWord">
            <summary>
            Contains the conversion methods to convert DWord from S7 plc to C#.
            </summary>
        </member>
        <member name="M:S7.Net.Types.DWord.FromByteArray(System.Byte[])">
            <summary>
            Converts a S7 DWord (4 bytes) to uint (UInt32)
            </summary>
        </member>
        <member name="M:S7.Net.Types.DWord.FromBytes(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Converts 4 bytes to DWord (UInt32)
            </summary>
        </member>
        <member name="M:S7.Net.Types.DWord.ToByteArray(System.UInt32)">
            <summary>
            Converts a uint (UInt32) to S7 DWord (4 bytes) 
            </summary>
        </member>
        <member name="M:S7.Net.Types.DWord.ToByteArray(System.UInt32[])">
            <summary>
            Converts an array of uint (UInt32) to an array of S7 DWord (4 bytes) 
            </summary>
        </member>
        <member name="M:S7.Net.Types.DWord.ToArray(System.Byte[])">
            <summary>
            Converts an array of S7 DWord to an array of uint (UInt32)
            </summary>
        </member>
        <member name="T:S7.Net.Types.Int">
            <summary>
            Contains the conversion methods to convert Int from S7 plc to C#.
            </summary>
        </member>
        <member name="M:S7.Net.Types.Int.FromByteArray(System.Byte[])">
            <summary>
            Converts a S7 Int (2 bytes) to short (Int16)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Int.ToByteArray(System.Int16)">
            <summary>
            Converts a short (Int16) to a S7 Int byte array (2 bytes)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Int.ToByteArray(System.Int16[])">
            <summary>
            Converts an array of short (Int16) to a S7 Int byte array (2 bytes)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Int.ToArray(System.Byte[])">
            <summary>
            Converts an array of S7 Int to an array of short (Int16)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Int.CWord(System.Int32)">
            <summary>
            Converts a C# int value to a C# short value, to be used as word.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:S7.Net.Types.LReal">
            <summary>
            Contains the conversion methods to convert Real from S7 plc to C# double.
            </summary>
        </member>
        <member name="M:S7.Net.Types.LReal.FromByteArray(System.Byte[])">
            <summary>
            Converts a S7 LReal (8 bytes) to double
            </summary>
        </member>
        <member name="M:S7.Net.Types.LReal.ToByteArray(System.Double)">
            <summary>
            Converts a double to S7 LReal (8 bytes)
            </summary>
        </member>
        <member name="M:S7.Net.Types.LReal.ToByteArray(System.Double[])">
            <summary>
            Converts an array of double to an array of bytes 
            </summary>
        </member>
        <member name="M:S7.Net.Types.LReal.ToArray(System.Byte[])">
            <summary>
            Converts an array of S7 LReal to an array of double
            </summary>
        </member>
        <member name="T:S7.Net.Types.Real">
            <summary>
            Contains the conversion methods to convert Real from S7 plc to C# double.
            </summary>
        </member>
        <member name="M:S7.Net.Types.Real.FromByteArray(System.Byte[])">
            <summary>
            Converts a S7 Real (4 bytes) to float
            </summary>
        </member>
        <member name="M:S7.Net.Types.Real.ToByteArray(System.Single)">
            <summary>
            Converts a float to S7 Real (4 bytes)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Real.ToByteArray(System.Single[])">
            <summary>
            Converts an array of float to an array of bytes 
            </summary>
        </member>
        <member name="M:S7.Net.Types.Real.ToArray(System.Byte[])">
            <summary>
            Converts an array of S7 Real to an array of float
            </summary>
        </member>
        <member name="T:S7.Net.Types.S7String">
            <summary>
            Contains the methods to convert from S7 strings to C# strings
            An S7 String has a preceeding 2 byte header containing its capacity and length
            </summary>
        </member>
        <member name="P:S7.Net.Types.S7String.StringEncoding">
            <summary>
            The Encoding used when serializing and deserializing S7String (Encoding.ASCII by default)
            </summary>
            <exception cref="T:System.ArgumentNullException">StringEncoding must not be null</exception>
        </member>
        <member name="M:S7.Net.Types.S7String.FromByteArray(System.Byte[])">
            <summary>
            Converts S7 bytes to a string
            </summary>
            <param name="bytes"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Types.S7String.ToByteArray(System.String,System.Int32)">
            <summary>
            Converts a <see cref="T:string"/> to S7 string with 2-byte header.
            </summary>
            <param name="value">The string to convert to byte array.</param>
            <param name="reservedLength">The length (in characters) allocated in PLC for the string.</param>
            <returns>A <see cref="T:byte[]" /> containing the string header and string value with a maximum length of <paramref name="reservedLength"/> + 2.</returns>
        </member>
        <member name="M:S7.Net.Types.S7StringAttribute.#ctor(S7.Net.Types.S7StringType,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:S7.Net.Types.S7StringAttribute"/> class.
            </summary>
            <param name="type">The string type.</param>
            <param name="reservedLength">Reserved length of the string in characters.</param>
            <exception cref="T:System.ArgumentException">Please use a valid value for the string type</exception>
        </member>
        <member name="P:S7.Net.Types.S7StringAttribute.Type">
            <summary>
            Gets the type of the string.
            </summary>
            <value>
            The string type.
            </value>
        </member>
        <member name="P:S7.Net.Types.S7StringAttribute.ReservedLength">
            <summary>
            Gets the reserved length of the string in characters.
            </summary>
            <value>
            The reserved length of the string in characters.
            </value>
        </member>
        <member name="P:S7.Net.Types.S7StringAttribute.ReservedLengthInBytes">
            <summary>
            Gets the reserved length in bytes.
            </summary>
            <value>
            The reserved length in bytes.
            </value>
        </member>
        <member name="T:S7.Net.Types.S7StringType">
            <summary>
            String type.
            </summary>
        </member>
        <member name="F:S7.Net.Types.S7StringType.S7String">
            <summary>
            ASCII string.
            </summary>
        </member>
        <member name="F:S7.Net.Types.S7StringType.S7WString">
            <summary>
            Unicode string.
            </summary>
        </member>
        <member name="T:S7.Net.Types.S7WString">
            <summary>
            Contains the methods to convert from S7 wstrings to C# strings
            An S7 WString has a preceding 4 byte header containing its capacity and length
            </summary>
        </member>
        <member name="M:S7.Net.Types.S7WString.FromByteArray(System.Byte[])">
            <summary>
            Converts S7 bytes to a string
            </summary>
            <param name="bytes"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Types.S7WString.ToByteArray(System.String,System.Int32)">
            <summary>
            Converts a <see cref="T:string"/> to S7 wstring with 4-byte header.
            </summary>
            <param name="value">The string to convert to byte array.</param>
            <param name="reservedLength">The length (in characters) allocated in PLC for the string.</param>
            <returns>A <see cref="T:byte[]" /> containing the string header and string value with a maximum length of <paramref name="reservedLength"/> + 4.</returns>
        </member>
        <member name="T:S7.Net.Types.Single">
            <summary>
            Contains the conversion methods to convert Real from S7 plc to C# float.
            </summary>
        </member>
        <member name="M:S7.Net.Types.Single.FromByteArray(System.Byte[])">
            <summary>
            Converts a S7 Real (4 bytes) to float
            </summary>
        </member>
        <member name="M:S7.Net.Types.Single.FromDWord(System.Int32)">
            <summary>
            Converts a S7 DInt to float
            </summary>
        </member>
        <member name="M:S7.Net.Types.Single.FromDWord(System.UInt32)">
            <summary>
            Converts a S7 DWord to float
            </summary>
        </member>
        <member name="M:S7.Net.Types.Single.ToByteArray(System.Single)">
            <summary>
            Converts a double to S7 Real (4 bytes)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Single.ToByteArray(System.Single[])">
            <summary>
            Converts an array of float to an array of bytes 
            </summary>
        </member>
        <member name="M:S7.Net.Types.Single.ToArray(System.Byte[])">
            <summary>
            Converts an array of S7 Real to an array of float
            </summary>
        </member>
        <member name="T:S7.Net.Types.String">
            <summary>
            Contains the methods to convert from S7 Array of Chars (like a const char[N] C-String) to C# strings
            </summary>
        </member>
        <member name="M:S7.Net.Types.String.ToByteArray(System.String,System.Int32)">
            <summary>
            Converts a string to <paramref name="reservedLength"/> of bytes, padded with 0-bytes if required.
            </summary>
            <param name="value">The string to write to the PLC.</param>
            <param name="reservedLength">The amount of bytes reserved for the <paramref name="value"/> in the PLC.</param>
        </member>
        <member name="M:S7.Net.Types.String.FromByteArray(System.Byte[])">
            <summary>
            Converts S7 bytes to a string
            </summary>
            <param name="bytes"></param>
            <returns></returns>
        </member>
        <member name="T:S7.Net.Types.StringEx">
            <inheritdoc cref="T:S7.Net.Types.S7String"/>
        </member>
        <member name="M:S7.Net.Types.StringEx.FromByteArray(System.Byte[])">
            <inheritdoc cref="M:S7.Net.Types.S7String.FromByteArray(System.Byte[])"/>
        </member>
        <member name="M:S7.Net.Types.StringEx.ToByteArray(System.String,System.Int32)">
            <inheritdoc cref="M:S7.Net.Types.S7String.ToByteArray(System.String,System.Int32)"/>
        </member>
        <member name="T:S7.Net.Types.Struct">
            <summary>
            Contains the method to convert a C# struct to S7 data types
            </summary>
        </member>
        <member name="M:S7.Net.Types.Struct.GetStructSize(System.Type)">
            <summary>
            Gets the size of the struct in bytes.
            </summary>
            <param name="structType">the type of the struct</param>
            <returns>the number of bytes</returns>
        </member>
        <member name="M:S7.Net.Types.Struct.FromBytes(System.Type,System.Byte[])">
            <summary>
            Creates a struct of a specified type by an array of bytes.
            </summary>
            <param name="structType">The struct type</param>
            <param name="bytes">The array of bytes</param>
            <returns>The object depending on the struct type or null if fails(array-length != struct-length</returns>
        </member>
        <member name="M:S7.Net.Types.Struct.ToBytes(System.Object)">
            <summary>
            Creates a byte array depending on the struct type.
            </summary>
            <param name="structValue">The struct object</param>
            <returns>A byte array or null if fails.</returns>
        </member>
        <member name="T:S7.Net.Types.Timer">
            <summary>
            Converts the Timer data type to C# data type
            </summary>
        </member>
        <member name="M:S7.Net.Types.Timer.FromByteArray(System.Byte[])">
            <summary>
            Converts the timer bytes to a double
            </summary>
        </member>
        <member name="M:S7.Net.Types.Timer.ToByteArray(System.UInt16)">
            <summary>
            Converts a ushort (UInt16) to an array of bytes formatted as time
            </summary>
        </member>
        <member name="M:S7.Net.Types.Timer.ToByteArray(System.UInt16[])">
            <summary>
            Converts an array of ushorts (Uint16) to an array of bytes formatted as time
            </summary>
        </member>
        <member name="M:S7.Net.Types.Timer.ToArray(System.Byte[])">
            <summary>
            Converts an array of bytes formatted as time to an array of doubles
            </summary>
            <param name="bytes"></param>
            <returns></returns>
        </member>
        <member name="M:S7.Net.Types.TypeHelper.ToByteArray``1(``0[],System.Func{``0,System.Byte[]})">
            <summary>
            Converts an array of T to an array of bytes 
            </summary>
        </member>
        <member name="M:S7.Net.Types.TypeHelper.ToArray``1(System.Byte[],System.Func{System.Byte[],``0})">
            <summary>
            Converts an array of T repesented as S7 binary data to an array of T
            </summary>
        </member>
        <member name="T:S7.Net.Types.Word">
            <summary>
            Contains the conversion methods to convert Words from S7 plc to C#.
            </summary>
        </member>
        <member name="M:S7.Net.Types.Word.FromByteArray(System.Byte[])">
            <summary>
            Converts a word (2 bytes) to ushort (UInt16)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Word.FromBytes(System.Byte,System.Byte)">
            <summary>
            Converts 2 bytes to ushort (UInt16)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Word.ToByteArray(System.UInt16)">
            <summary>
            Converts a ushort (UInt16) to word (2 bytes)
            </summary>
        </member>
        <member name="M:S7.Net.Types.Word.ToByteArray(System.UInt16[])">
            <summary>
            Converts an array of ushort (UInt16) to an array of bytes
            </summary>
        </member>
        <member name="M:S7.Net.Types.Word.ToArray(System.Byte[])">
            <summary>
            Converts an array of bytes to an array of ushort
            </summary>
        </member>
    </members>
</doc>
