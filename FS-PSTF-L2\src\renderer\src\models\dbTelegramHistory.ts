import { executeQuery } from '@/models/dbUtils'

export interface TelegramHistory {
    id: number | null
    // 方向, R | S
    direction: string
    // 报文号
    tel_no: number
    // 报文长度
    tel_len: number
    // 发送方
    send_id: string
    // 接收方
    rec_id: string
    // 报文计数
    tel_counter: number
    // 创建时间
    record_time: Date
    // 创建日期

    // extra keys
    isEdit: boolean
    isDeletable: boolean
}

export interface TeleHistoryFilterOptions {
    tel_no: number | null
    date: number | null
}

// extra keys that does not belong to table
//const extraKeys: (keyof TelegramHistory)[] = [
//    <keyof TelegramHistory>'isEdit',
//    <keyof TelegramHistory>'isDeletable',
//`]

// log function stub
async function fetchById(id: number): Promise<TelegramHistory | undefined> {
    if (id > 0) {
        try {
            const result = await executeQuery(
                'SELECT * FROM TelegramHistory WHERE id = ?',
                [id],
            )
            return result[0] as TelegramHistory
        } catch (err) {
            window.electron.ipcRenderer.invoke('add-log', 'error', err)
            return undefined
        }
    } else {
        window.electron.ipcRenderer.invoke(
            'add-log',
            'warn',
            'Fetch TelegramHistory by id, but id is 0.',
        )
        return undefined
    }
}

// fetch all TelegramHistorys, and their quotes
async function fetch(
    pageStart: number,
    pageSize: number,
    order: string,
    filter: TeleHistoryFilterOptions,
): Promise<TelegramHistory[]> {
    try {
        let query = 'SELECT * FROM TelegramHistory WHERE 1=1'
        const params: (string | number)[] = []

        if (typeof filter?.tel_no === 'number' && filter.tel_no !== 0) {
            query += ' AND tel_no = ?'
            params.push(filter.tel_no)
        }

        if (typeof filter?.date === 'number' && filter.date >= 1672502400000) {
            query += ` AND DATEDIFF(day, [record_time], '${new Date(
                filter.date,
            ).toLocaleDateString('zh-CN')}')=0`
        }

        query += ` ORDER BY id ${order} OFFSET ? ROWS FETCH NEXT ? ROWS ONLY`
        params.push(pageStart, pageSize)

        const telegrams = await executeQuery(query, params)
        return telegrams as TelegramHistory[]
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return []
    }
}

// get total record count
async function count(filter?: TeleHistoryFilterOptions): Promise<number> {
    try {
        let query = 'SELECT COUNT(*) as count FROM TelegramHistory WHERE 1=1'
        const params: (string | number)[] = []

        if (typeof filter?.tel_no === 'number' && filter.tel_no !== 0) {
            query += ' AND tel_no = ?'
            params.push(filter.tel_no)
        }

        if (typeof filter?.date === 'number' && filter.date >= 1672502400000) {
            query += ` AND DATEDIFF(day, [record_time], '${new Date(
                filter.date,
            ).toLocaleDateString('zh-CN')}')=0`
        }

        const result = await executeQuery(query, params)
        return result[0].count as number
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// delete a TelegramHistory
async function del(id: number): Promise<number> {
    try {
        const result = await executeQuery(
            'DELETE FROM TelegramHistory WHERE id = ?',
            [id],
        )
        return result.affectedRows
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// export functions
export default {
    fetchById,
    fetch,
    count,
    del,
}
