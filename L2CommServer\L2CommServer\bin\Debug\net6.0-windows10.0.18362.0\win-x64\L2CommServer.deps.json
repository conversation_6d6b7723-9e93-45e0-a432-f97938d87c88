{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/win-x64": {"L2CommServer/0.3.0": {"dependencies": {"Hardcodet.NotifyIcon.Wpf": "1.1.0", "ModernWpfUI": "0.9.4", "NetCoreServer": "5.1.0", "Serilog.Sinks.RichTextBox.Wpf": "1.1.0", "Services": "1.0.0", "Telegrams": "1.0.0", "libinidotnet": "3.0.0.1", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "6.0.4", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "6.0.4", "runtimepack.Microsoft.Windows.SDK.NET.Ref": "10.0.18362.24"}, "runtime": {"L2CommServer.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.4": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "11.100.422.16404"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "6.0.422.16404"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.28.29715.1"}, "System.IO.Compression.Native.dll": {"fileVersion": "42.42.42.42424"}, "api-ms-win-core-console-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-console-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-datetime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-debug-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-errorhandling-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-fibers-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l2-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-handle-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-interlocked-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-libraryloader-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-localization-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-memory-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-namedpipe-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processenvironment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-1.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-profile-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-rtlsupport-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-sysinfo-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-timezone-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-util-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-conio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-convert-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-environment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-filesystem-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-locale-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-math-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-multibyte-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-private-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-process-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-runtime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-stdio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-time-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-utility-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "clretwrc.dll": {"fileVersion": "6.0.422.16404"}, "clrjit.dll": {"fileVersion": "6.0.422.16404"}, "coreclr.dll": {"fileVersion": "6.0.422.16404"}, "createdump.exe": {"fileVersion": "6.0.422.16404"}, "dbgshim.dll": {"fileVersion": "6.0.422.16404"}, "hostfxr.dll": {"fileVersion": "6.0.422.16404"}, "hostpolicy.dll": {"fileVersion": "6.0.422.16404"}, "mscordaccore.dll": {"fileVersion": "6.0.422.16404"}, "mscordaccore_amd64_amd64_6.0.422.16404.dll": {"fileVersion": "6.0.422.16404"}, "mscordbi.dll": {"fileVersion": "6.0.422.16404"}, "mscorrc.dll": {"fileVersion": "6.0.422.16404"}, "msquic.dll": {"fileVersion": "*******"}, "ucrtbase.dll": {"fileVersion": "10.0.22000.194"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/6.0.4": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16407"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16407"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "6.0.422.16407"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16407"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16407"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16407"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16407"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16407"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16407"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16407"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16503"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22000.194"}, "PenImc_cor3.dll": {"fileVersion": "6.0.422.16503"}, "PresentationNative_cor3.dll": {"fileVersion": "**********02"}, "vcruntime140_cor3.dll": {"fileVersion": "14.31.31103.0"}, "wpfgfx_cor3.dll": {"fileVersion": "6.0.422.16503"}}}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.18362.24": {"runtime": {"Microsoft.Windows.SDK.NET.dll": {"assemblyVersion": "10.0.18362.24", "fileVersion": "10.0.18362.24"}, "WinRT.Runtime.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.1.38391"}}}, "Azure.Core/1.25.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/Azure.Core.dll": {"assemblyVersion": "1.25.0.0", "fileVersion": "1.2500.22.33004"}}}, "Azure.Identity/1.7.0": {"dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "*******", "fileVersion": "1.700.22.46903"}}}, "Dapper/2.0.138": {"runtime": {"lib/net5.0/Dapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.138.25557"}}}, "Hardcodet.NotifyIcon.Wpf/1.1.0": {"runtime": {"lib/net5.0-windows7.0/Hardcodet.NotifyIcon.Wpf.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.17"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Composition/1.0.31": {"dependencies": {"System.Composition": "1.0.31"}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.1.1": {"dependencies": {"Azure.Identity": "1.7.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.24.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"native": {"runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"fileVersion": "*******"}}}, "Microsoft.Identity.Client/4.47.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0", "Microsoft.Web.WebView2": "1.0.864.35"}, "runtime": {"lib/net5.0-windows10.0.17763/Microsoft.Identity.Client.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"dependencies": {"Microsoft.Identity.Client": "4.47.2", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.24.0", "System.Text.Encoding": "4.3.0", "System.Text.Json": "4.7.2"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Logging/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.24.0", "System.IdentityModel.Tokens.Jwt": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Tokens/6.24.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.24.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Web.WebView2/1.0.864.35": {"runtime": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.864.35", "fileVersion": "1.0.864.35"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.864.35", "fileVersion": "1.0.864.35"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.864.35", "fileVersion": "1.0.864.35"}}, "native": {"runtimes/win-x64/native/WebView2Loader.dll": {"fileVersion": "1.0.864.35"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {}, "ModernWpfUI/0.9.4": {"runtime": {"lib/net5.0-windows10.0.18362.0/ModernWpf.Controls.dll": {"assemblyVersion": "0.9.4.0", "fileVersion": "0.9.4.0"}, "lib/net5.0-windows10.0.18362.0/ModernWpf.dll": {"assemblyVersion": "0.9.4.0", "fileVersion": "0.9.4.0"}}, "resources": {"lib/net5.0-windows10.0.18362.0/zh-CN/ModernWpf.Controls.resources.dll": {"locale": "zh-CN"}, "lib/net5.0-windows10.0.18362.0/zh-CN/ModernWpf.resources.dll": {"locale": "zh-CN"}}}, "NetCoreServer/5.1.0": {"runtime": {"lib/net5.0/NetCoreServer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.any.System.Collections/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "runtime.any.System.Diagnostics.Tools/4.3.0": {}, "runtime.any.System.Globalization/4.3.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Extensions/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Resources.ResourceManager/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.win.System.Diagnostics.Debug/4.3.0": {}, "runtime.win.System.Runtime.Extensions/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "Serilog/2.10.0": {"runtime": {"lib/netstandard2.1/Serilog.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.10.0.0"}}}, "Serilog.Sinks.RichTextBox.Wpf/1.1.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/net5.0-windows7.0/Serilog.Sinks.RichTextBox.Wpf.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}}, "System.Composition/1.0.31": {"dependencies": {"System.Composition.AttributedModel": "1.0.31", "System.Composition.Convention": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Composition.TypedParts": "1.0.31"}}, "System.Composition.AttributedModel/1.0.31": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Convention/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Convention.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Hosting/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Hosting.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Runtime/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.TypedParts/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.TypedParts.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Diagnostics.Debug": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tools": "4.3.0"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}}, "System.Formats.Asn1/5.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Extensions": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/4.7.2": {}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}}, "S7netplus/1.0.0": {"runtime": {"S7.Net.dll": {}}}, "Services/1.0.0": {"dependencies": {"Dapper": "2.0.138", "Microsoft.Composition": "1.0.31", "Microsoft.Data.SqlClient": "5.1.1", "Serilog": "2.10.0"}, "runtime": {"Services.dll": {}}}, "Telegrams/1.0.0": {"dependencies": {"S7netplus": "1.0.0", "Services": "1.0.0"}, "runtime": {"Telegrams.dll": {}}}, "libinidotnet/3.0.0.1": {"runtime": {"libinidotnet.dll": {"assemblyVersion": "3.0.0.1", "fileVersion": "3.0.0.1"}}}}}, "libraries": {"L2CommServer/0.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.4": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/6.0.4": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.18362.24": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Azure.Core/1.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "path": "azure.core/1.25.0", "hashPath": "azure.core.1.25.0.nupkg.sha512"}, "Azure.Identity/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHEiCO/8+MfNc9nH5dVew/+FvxdaGrkRL4OMNwIz0W79+wtJyEoeRlXJ3SrXhoy9XR58geBYKmzMR83VO7bcAw==", "path": "azure.identity/1.7.0", "hashPath": "azure.identity.1.7.0.nupkg.sha512"}, "Dapper/2.0.138": {"type": "package", "serviceable": true, "sha512": "sha512-QfnstG3t1hQVKCwVDaMjK72O+B+Ognfcf5fYOFI60ELpxuwE0WGtaOOwqczcuPgU3QEDXq/3TTvOilCvwszRhg==", "path": "dapper/2.0.138", "hashPath": "dapper.2.0.138.nupkg.sha512"}, "Hardcodet.NotifyIcon.Wpf/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-FFEkdRSidMrbMfQVwhCzq6KtSyR1qQojqtiUrkCyjnOOuMf7W3TNQkzFnBBxYxFe7uLk5mz7M38wFh1qdWyfvg==", "path": "hardcodet.notifyicon.wpf/1.1.0", "hashPath": "hardcodet.notifyicon.wpf.1.1.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Composition/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-R8V1rw4ldOoKIg0QzDY033V8uKrNR0VRKuKVuA1wzuIVeBLwYGghF0y+WbmPI245xSnjRh5eMxxBaxDX9DYZmA==", "path": "microsoft.composition/1.0.31", "hashPath": "microsoft.composition.1.0.31.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MW5E9HFvCaV069o8b6YpuRDPBux8s96qDnOJ+4N9QNUCs7c5W3KxwQ+ftpAjbMUlImL+c9WR+l+f5hzjkqhu2g==", "path": "microsoft.data.sqlclient/5.1.1", "hashPath": "microsoft.data.sqlclient.5.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jVsElisM5sfBzaaV9kdq2NXZLwIbytetnsOIlJ0cQGgQP4zFNBmkfHBnpwtmKrtBJBEV9+9PVQPVrcCVhDgcIg==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.47.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPgesZRbXoDxg8Vv7k5Ou0ee7uupVw0E8ZCc4GKw25HANRLz1d5OSr0fvTVQRnEswo5Obk8qD4LOapYB+n5kzQ==", "path": "microsoft.identity.client/4.47.2", "hashPath": "microsoft.identity.client.4.47.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "serviceable": true, "sha512": "sha512-zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "path": "microsoft.identity.client.extensions.msal/2.19.3", "hashPath": "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-X6aBK56Ot15qKyG7X37KsPnrwah+Ka55NJWPppWVTDi8xWq7CJgeNw2XyaeHgE1o/mW4THwoabZkBbeG2TPBiw==", "path": "microsoft.identitymodel.abstractions/6.24.0", "hashPath": "microsoft.identitymodel.abstractions.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-XDWrkThcxfuWp79AvAtg5f+uRS1BxkIbJnsG/e8VPzOWkYYuDg33emLjp5EWcwXYYIDsHnVZD/00kM/PYFQc/g==", "path": "microsoft.identitymodel.jsonwebtokens/6.24.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-qLYWDOowM/zghmYKXw1yfYKlHOdS41i8t4hVXr9bSI90zHqhyhQh9GwVy8pENzs5wHeytU23DymluC9NtgYv7w==", "path": "microsoft.identitymodel.logging/6.24.0", "hashPath": "microsoft.identitymodel.logging.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-+NzKCkvsQ8X1r/Ff74V7CFr9OsdMRaB6DsV+qpH7NNLdYJ8O4qHbmTnNEsjFcDmk/gVNDwhoL2gN5pkPVq0lwQ==", "path": "microsoft.identitymodel.protocols/6.24.0", "hashPath": "microsoft.identitymodel.protocols.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-a/2RRrc8C9qaw8qdD9hv1ES9YKFgxaqr/SnwMSLbwQZJSUQDd4qx1K4EYgWaQWs73R+VXLyKSxN0f/uE9CsBiQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.24.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZPqHi86UYuqJXJ7bLnlEctHKkPKT4lGUFbotoCNiXNCSL02emYlcxzGYsRGWWmbFEcYDMi2dcTLLYNzHqWOTsw==", "path": "microsoft.identitymodel.tokens/6.24.0", "hashPath": "microsoft.identitymodel.tokens.6.24.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.864.35": {"type": "package", "serviceable": true, "sha512": "sha512-V1qyLRiAZ31qmOOCFCjoONgaUfvJRiTHWcJWkT3V7pluM2+P6QAgqmbE4UX7Gt4sh6eN34wqM30OnTZ6HXI/sw==", "path": "microsoft.web.webview2/1.0.864.35", "hashPath": "microsoft.web.webview2.1.0.864.35.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "ModernWpfUI/0.9.4": {"type": "package", "serviceable": true, "sha512": "sha512-HJ07Be9KOiGKGcMLz/AwY+84h3yGHRPuYpYXCE6h1yPtaFwGMWfanZ70jX7W5XWx8+Qk1vGox+WGKgxxsy6EHw==", "path": "modernwpfui/0.9.4", "hashPath": "modernwpfui.0.9.4.nupkg.sha512"}, "NetCoreServer/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-eZZEe/nslw5MO/DL2RUHmypJruerACyYleRILJT0g6/c9BiUO2LM0RuOWiUTx/U0wBa7FrkxsyPU8/Q/M4q+EQ==", "path": "netcoreserver/5.1.0", "hashPath": "netcoreserver.5.1.0.nupkg.sha512"}, "runtime.any.System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-23g6rqftKmovn2cLeGsuHUYm0FD7pdutb0uQMJpZ3qTvq+zHkgmt6J65VtRry4WDGYlmkMa4xDACtaQ94alNag==", "path": "runtime.any.system.collections/4.3.0", "hashPath": "runtime.any.system.collections.4.3.0.nupkg.sha512"}, "runtime.any.System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-S/GPBmfPBB48ZghLxdDR7kDAJVAqgAuThyDJho3OLP5OS4tWD2ydyL8LKm8lhiBxce10OKe9X2zZ6DUjAqEbPg==", "path": "runtime.any.system.diagnostics.tools/4.3.0", "hashPath": "runtime.any.system.diagnostics.tools.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sMDBnad4rp4t7GY442Jux0MCUuKL4otn5BK6Ni0ARTXTSpRNBzZ7hpMfKSvnVSED5kYJm96YOWsqV0JH0d2uuw==", "path": "runtime.any.system.globalization/4.3.0", "hashPath": "runtime.any.system.globalization.4.3.0.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cPhT+Vqu52+cQQrDai/V91gubXUnDKNRvlBnH+hOgtGyHdC17aQIU64EaehwAQymd7kJA5rSrVRNfDYrbhnzyA==", "path": "runtime.any.system.reflection.extensions/4.3.0", "hashPath": "runtime.any.system.reflection.extensions.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lxb89SMvf8w9p9+keBLyL6H6x/TEmc6QVsIIA0T36IuyOY3kNvIdyGddA2qt35cRamzxF8K5p0Opq4G4HjNbhQ==", "path": "runtime.any.system.resources.resourcemanager/4.3.0", "hashPath": "runtime.any.system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "runtime.win.System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hHHP0WCStene2jjeYcuDkETozUYF/3sHVRHAEOgS3L15hlip24ssqCTnJC28Z03Wpo078oMcJd0H4egD2aJI8g==", "path": "runtime.win.system.diagnostics.debug/4.3.0", "hashPath": "runtime.win.system.diagnostics.debug.4.3.0.nupkg.sha512"}, "runtime.win.System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RkgHVhUPvzZxuUubiZe8yr/6CypRVXj0VBzaR8hsqQ8f+rUo7e4PWrHTLOCjd8fBMGWCrY//fi7Ku3qXD7oHRw==", "path": "runtime.win.system.runtime.extensions/4.3.0", "hashPath": "runtime.win.system.runtime.extensions.4.3.0.nupkg.sha512"}, "Serilog/2.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "path": "serilog/2.10.0", "hashPath": "serilog.2.10.0.nupkg.sha512"}, "Serilog.Sinks.RichTextBox.Wpf/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-E/ngRTlTHqKXz1+LuebDU8eCIL4sNlxphrVx7Z/KjVxzqajLGxABJgtTD7tF+AQ6iXU2IPwgvLpJxEVb85tc3A==", "path": "serilog.sinks.richtextbox.wpf/1.1.0", "hashPath": "serilog.sinks.richtextbox.wpf.1.1.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Composition/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-I+D26qpYdoklyAVUdqwUBrEIckMNjAYnuPJy/h9dsQItpQwVREkDFs4b4tkBza0kT2Yk48Lcfsv2QQ9hWsh9Iw==", "path": "system.composition/1.0.31", "hashPath": "system.composition.1.0.31.nupkg.sha512"}, "System.Composition.AttributedModel/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-NHWhkM3ZkspmA0XJEsKdtTt1ViDYuojgSND3yHhTzwxepiwqZf+BCWuvCbjUt4fe0NxxQhUDGJ5km6sLjo9qnQ==", "path": "system.composition.attributedmodel/1.0.31", "hashPath": "system.composition.attributedmodel.1.0.31.nupkg.sha512"}, "System.Composition.Convention/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-GLjh2Ju71k6C0qxMMtl4efHa68NmWeIUYh4fkUI8xbjQrEBvFmRwMDFcylT8/PR9SQbeeL48IkFxU/+gd0nYEQ==", "path": "system.composition.convention/1.0.31", "hashPath": "system.composition.convention.1.0.31.nupkg.sha512"}, "System.Composition.Hosting/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-fN1bT4RX4vUqjbgoyuJFVUizAl2mYF5VAb+bVIxIYZSSc0BdnX+yGAxcavxJuDDCQ1K+/mdpgyEFc8e9ikjvrg==", "path": "system.composition.hosting/1.0.31", "hashPath": "system.composition.hosting.1.0.31.nupkg.sha512"}, "System.Composition.Runtime/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0LEJN+2NVM89CE4SekDrrk5tHV5LeATltkp+9WNYrR+Huiyt0vaCqHbbHtVAjPyeLWIc8dOz/3kthRBj32wGQg==", "path": "system.composition.runtime/1.0.31", "hashPath": "system.composition.runtime.1.0.31.nupkg.sha512"}, "System.Composition.TypedParts/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0Zae/FtzeFgDBBuILeIbC/T9HMYbW4olAmi8XqqAGosSOWvXfiQLfARZEhiGd0LVXaYgXr0NhxiU1LldRP1fpQ==", "path": "system.composition.typedparts/1.0.31", "hashPath": "system.composition.typedparts.1.0.31.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qibsj9MPWq8S/C0FgvmsLfIlHLE7ay0MJIaAmK94ivN3VyDdglqReed5qMvdQhSL0BzK6v0Z1wB/sD88zVu6Jw==", "path": "system.identitymodel.tokens.jwt/6.24.0", "hashPath": "system.identitymodel.tokens.jwt.6.24.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "S7netplus/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Telegrams/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "libinidotnet/3.0.0.1": {"type": "reference", "serviceable": false, "sha512": ""}}, "runtimes": {"win-x64": ["win", "any", "base"], "win-x64-aot": ["win-aot", "win-x64", "win", "aot", "any", "base"], "win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win7-x64": ["win7", "win-x64", "win", "any", "base"], "win7-x64-aot": ["win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win8-x64": ["win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win8-x64-aot": ["win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win81-x64": ["win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win81-x64-aot": ["win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}