import { computed, ref, reactive, h, Ref, VNode } from 'vue'
import { PaginationProps } from 'naive-ui'
import eventBus from '@common/libs/eventBus'

export interface UseDataTableReturn<T> {
    data: Ref<T[]>
    telegrams: Ref<T[]>
    loading: Ref<boolean>
    pagination: PaginationProps
    handlePageChange: (page: number) => void
    refreshData: () => Promise<void>
    renderTableTitle: (title: string) => VNode
}

export function useDataTable<T>(
    fetchDataFn: (start: number, pageSize: number) => Promise<T[]>,
    fetchCountFn: () => Promise<number>,
    mapFn: (tele: T) => T,
    initialPageSize: number = 15,
): UseDataTableReturn<T> {
    const data = ref<T[]>([]) as Ref<T[]>
    const loading = ref(true)
    const total = ref(0)

    const pagination: PaginationProps = reactive({
        page: 1,
        pageCount: 1,
        pageSize: initialPageSize,
        prefix({ itemCount }) {
            return `共 ${itemCount} 条记录`
        },
    })

    const handlePageChange = (page: number) => {
        pagination.page = page
        refreshData()
    }

    const refreshData = async () => {
        loading.value = true
        total.value = await fetchCountFn()

        let pageStart =
            ((pagination.page ?? 1) - 1) *
            (pagination.pageSize ?? initialPageSize)
        // double check if pageStart is > total.value
        if (pageStart > total.value) {
            pagination.page = 1
            pageStart = 0
        }
        data.value = await fetchDataFn(
            pageStart,
            pagination.pageSize ?? initialPageSize,
        )
        pagination.pageCount = Math.ceil(
            total.value / (pagination.pageSize ?? initialPageSize),
        )
        pagination.itemCount = total.value
        loading.value = false

        eventBus.emit('updateDatalogExploreBar')
    }

    const renderTableTitle = (title: string): VNode => {
        return h(
            'span',
            {
                style: 'font-weight: bold; font-size: 0.8rem; text-align: center',
            },
            { default: () => title },
        )
    }

    const telegrams = computed(() => {
        let tempTelegrams

        // add extra columns
        tempTelegrams = data.value?.map((tele) => mapFn(tele))
        return tempTelegrams
    })

    return {
        data, // raw telegrams
        telegrams, // mapped telegrams
        loading,
        pagination,
        handlePageChange,
        refreshData,
        renderTableTitle,
    }
}
