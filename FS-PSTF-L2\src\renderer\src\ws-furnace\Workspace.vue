<template>
    <div class="workspace">
        <div class="panel">
            <n-space vertical align="stretch">
                <n-card bordered size="small">
                    <n-grid :cols="24">
                        <n-gi :span="3">
                            <n-statistic label="炉内坯料">
                                {{ fncStatsRef.count }}
                            </n-statistic>
                        </n-gi>
                        <n-gi :span="3">
                            <n-statistic label="出料位置">
                                {{ fncStatsRef.dischPos }}
                            </n-statistic>
                        </n-gi>
                        <n-gi :span="4">
                            <n-statistic label="最后出料">
                                {{ fncStatsRef.lastDischarged.Billet_ID }}
                            </n-statistic>
                        </n-gi>
                    </n-grid>
                </n-card>
                <n-card class="fnc-card" bordered embedded size="small">
                    <div ref="fncDiv" class="fncDiv">
                        <template v-for="(billet, idx) in billets" :key="idx">
                            <BilletImage
                                v-if="
                                    billet.Billet_ID !== 'undefined' &&
                                    billet.Billet_ID !== ''
                                "
                                :billet="billet"
                                :idx="idx"
                                :x-ratio="xRatio"
                                :y-ratio="yRatio"
                                :billet-interval="billetInterval"
                                @click="selectedBillet = billet"
                            />
                        </template>
                    </div>
                </n-card>
                <div class="bottom">
                    <n-card
                        class="billet-card1"
                        bordered
                        size="small"
                        title="坯料详细信息"
                    >
                        <EmptyInfo v-if="selectedBillet === null" />
                        <BilletInfo
                            v-if="selectedBillet !== null"
                            :billet="selectedBillet"
                        />
                    </n-card>
                    <n-card
                        class="billet-card2"
                        bordered
                        size="small"
                        title="坯料时间"
                    >
                        <EmptyInfo v-if="selectedBillet === null" />
                        <BilletTimes
                            v-if="selectedBillet !== null"
                            :billet="selectedBillet"
                        />
                    </n-card>

                    <n-card class="billet-card3" bordered size="small" title="">
                        <EmptyInfo />
                    </n-card>
                </div>
            </n-space>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, Ref, onUnmounted } from 'vue'
import eventBus from '@common/libs/eventBus'
import { BilletData } from '@common/interfaces/tcData'

// components
import BilletImage from './BilletImage.vue'
import EmptyInfo from './EmptyInfo.vue'
import BilletInfo from './BilletInfo.vue'
import BilletTimes from './BilletTimes.vue'

const fncStatsRef = ref({
    count: 0,
    dischPos: 50,
    lastDischarged: <BilletData>{},
})

// furnace box ref
const fncDiv: Ref<HTMLInputElement> = ref(<HTMLInputElement>(<unknown>null))
let fncDivHeight = 0
let fncDivWidth = 0
// screen ratio, pixel/mm
const xRatio = ref(0)
const yRatio = ref(0)
const billetInterval = ref(0)

const billets = ref(<BilletData[]>[])
const selectedBillet = ref(<BilletData>(<unknown>null))

// listen on reply from IPC
window.electron.ipcRenderer.on('tc-tracking-data', (_event, values) => {
    // billets in furnace
    billets.value = values.BilletData
    // other data
    fncStatsRef.value.dischPos = values.DischPos
    fncStatsRef.value.lastDischarged = values.LastDischarged
    fncStatsRef.value.count = countBillets()

    // update ratio
    updateRatio()
})

// count Billets
const countBillets = () => {
    return billets.value.filter(
        (b) => b.Billet_ID !== 'undefined' && b.Billet_ID !== '',
    ).length
}

const refreshData = () => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'verbose',
        'Request data from PLC  ',
    )
    // invoke load
    window.electron.ipcRenderer.invoke('load-tc-tracking')
    // return data is also sent via IPC
}

// update display ratio
function updateRatio() {
    fncDivWidth = fncDiv.value?.clientWidth || 1000
    fncDivHeight = fncDiv.value?.clientHeight || 250

    // furnace chamber size: 32000*5500, ratio = pixel/mm
    xRatio.value = (fncDivWidth - 20) / 32000
    yRatio.value = (fncDivHeight - 20) / 5500

    // billet postition interval, px
    fncStatsRef.value.dischPos = fncStatsRef.value.dischPos ?? 50
    billetInterval.value = (fncDivWidth - 20) / fncStatsRef.value.dischPos
}

// monitor main window resize
// update title
eventBus.on('main-window-resize', () => {
    updateRatio()
})

let refreshTimer: NodeJS.Timeout | null = null
onMounted(async () => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'verbose',
        'Workspace FURNACE mounted',
    )

    // set window title
    eventBus.emit('update-window-title', '加热炉')

    // get init display ratio
    updateRatio()

    // set auto load
    refreshData()

    // set auto refresh
    refreshTimer = setInterval(refreshData, 5000)
})

onUnmounted(() => {
    if (refreshTimer) {
        clearInterval(refreshTimer)
        refreshTimer = null
    }
})
</script>

<style lang="scss">
.workspace {
    flex: 1;
    overflow: hidden;
    height: calc(100vh - var(--excluding-size));
}

.panel {
    padding: 10px;
    margin: 0;
    height: 100%;
    box-sizing: border-box;
}

.fnc-card {
    height: 320px;
}

.fncDiv {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
}

.bottom {
    height: calc(100vh - var(--excluding-size) - 450px);
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 8px 8px;

    .billet-card1 {
        width: 400px;
        flex-shrink: 0;
    }

    .billet-card2 {
        flex-grow: 1;
    }

    .billet-card3 {
        width: 300px;
        flex-shrink: 0;
    }
}

.n-card {
    .n-card__content {
        padding-right: 6px;
    }
}
</style>
