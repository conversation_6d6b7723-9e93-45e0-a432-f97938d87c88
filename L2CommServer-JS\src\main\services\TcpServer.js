import net from 'net';
import EventEmitter from 'events';
import log from 'electron-log';
import { TelegramHeader, EventTypes } from '../../common/interfaces.js';

/**
 * TCP Session class - handles individual client connections
 */
class TcpSession extends EventEmitter {
    constructor(socket, server) {
        super();
        this.socket = socket;
        this.server = server;
        this.id = this.generateSessionId();
        this.peerIp = socket.remoteAddress;
        this.peerPort = socket.remotePort;
        this.connected = true;
        this.connectionTime = new Date();

        this.setupSocketEvents();
        log.info(
            `TCP client from ${this.peerIp}:${this.peerPort} connected. Session ID: ${this.id}`
        );
    }

    generateSessionId() {
        return Math.random().toString(36).substr(2, 9);
    }

    setupSocketEvents() {
        this.socket.on('data', (data) => {
            this.onReceived(data);
        });

        this.socket.on('error', (error) => {
            this.onError(error);
        });

        this.socket.on('close', () => {
            this.onDisconnected();
        });

        this.socket.on('timeout', () => {
            log.warn(`TCP session ${this.id} timed out`);
            this.socket.destroy();
        });
    }

    onReceived(buffer) {
        try {
            // Process received telegram asynchronously
            setImmediate(() => {
                this.processReceivedData(buffer);
            });
        } catch (error) {
            log.error(
                `Error processing received data in session ${this.id}:`,
                error
            );
        }
    }

    async processReceivedData(buffer) {
        try {
            // Import telegram processor dynamically to avoid circular dependencies
            const { TelegramProcessor } = require('./TelegramProcessor');
            const processor = new TelegramProcessor(this.server.pluginManager);

            // Set up send request callback
            processor.setSendRequestCallback((responseData) => {
                this.onSendRequest(responseData);
            });

            // Process the received telegram
            const processedCount = await processor.processReceived(
                buffer,
                0,
                buffer.length,
                this.peerIp
            );

            // Emit telegram count event
            this.emit('telegramProcessed', {
                type: 'received',
                count: processedCount,
                sessionId: this.id,
            });
        } catch (error) {
            log.error(
                `Error in processReceivedData for session ${this.id}:`,
                error
            );
        }
    }

    sendAsync(data) {
        return new Promise((resolve, reject) => {
            if (!this.connected || this.socket.destroyed) {
                reject(new Error('Socket is not connected'));
                return;
            }

            this.socket.write(data, (error) => {
                if (error) {
                    reject(error);
                } else {
                    resolve();
                }
            });
        });
    }

    onSendRequest(telegramData) {
        this.sendAsync(telegramData)
            .then(() => {
                // Emit telegram sent event
                this.emit('telegramProcessed', {
                    type: 'sent',
                    count: 1,
                    sessionId: this.id,
                });

                log.debug(`Telegram sent to session ${this.id}`);
            })
            .catch((error) => {
                log.error(
                    `Failed to send telegram to session ${this.id}:`,
                    error
                );
            });
    }

    onError(error) {
        log.error(`TCP session ${this.id} error:`, error);
        this.emit('sessionError', {
            sessionId: this.id,
            error: error,
            peerIp: this.peerIp,
        });
    }

    onDisconnected() {
        this.connected = false;
        log.info(`TCP client with session ID ${this.id} disconnected`);

        this.emit('sessionDisconnected', {
            sessionId: this.id,
            peerIp: this.peerIp,
            connectionTime: this.connectionTime,
        });
    }

    disconnect() {
        if (this.socket && !this.socket.destroyed) {
            this.socket.destroy();
        }
    }
}

/**
 * TCP Server class - main server implementation
 */
class TcpServerService extends EventEmitter {
    constructor() {
        super();
        this.server = null;
        this.sessions = new Map();
        this.isRunning = false;
        this.pluginManager = null;
        this.config = {
            ip: '127.0.0.1',
            port: 1111,
        };
        this.statistics = {
            connections: 0,
            telegramsReceived: 0,
            telegramsSent: 0,
            startTime: null,
        };
    }

    /**
     * Set plugin manager
     * @param {PluginManager} pluginManager - Plugin manager instance
     */
    setPluginManager(pluginManager) {
        this.pluginManager = pluginManager;
    }

    async start(ip = this.config.ip, port = this.config.port) {
        if (this.isRunning) {
            throw new Error('Server is already running');
        }

        return new Promise((resolve, reject) => {
            this.server = net.createServer();

            this.server.on('connection', (socket) => {
                this.onConnection(socket);
            });

            this.server.on('error', (error) => {
                this.onError(error);
                reject(error);
            });

            this.server.on('listening', () => {
                this.isRunning = true;
                this.statistics.startTime = new Date();
                this.config.ip = ip;
                this.config.port = port;

                log.info(`TCP server started on ${ip}:${port}`);
                this.emit('serverStarted', { ip, port });
                resolve();
            });

            this.server.listen(port, ip);
        });
    }

    async stop() {
        if (!this.isRunning) {
            return;
        }

        return new Promise((resolve) => {
            // Disconnect all sessions
            for (const session of this.sessions.values()) {
                session.disconnect();
            }
            this.sessions.clear();

            this.server.close(() => {
                this.isRunning = false;
                this.statistics.startTime = null;
                log.info('TCP server stopped');
                this.emit('serverStopped');
                resolve();
            });
        });
    }

    onConnection(socket) {
        const session = new TcpSession(socket, this);
        this.sessions.set(session.id, session);
        this.statistics.connections++;

        // Set up session event handlers
        session.on('sessionDisconnected', (data) => {
            this.sessions.delete(data.sessionId);
            this.statistics.connections--;
            this.emit('connectionChanged', {
                connected: false,
                sessionId: data.sessionId,
                peerIp: data.peerIp,
                type: 'P', // Passive connection
            });
        });

        session.on('sessionError', (data) => {
            this.emit('sessionError', data);
        });

        session.on('telegramProcessed', (data) => {
            if (data.type === 'received') {
                this.statistics.telegramsReceived += data.count;
            } else if (data.type === 'sent') {
                this.statistics.telegramsSent += data.count;
            }
            this.emit('telegramProcessed', data);
        });

        // Emit connection event
        this.emit('connectionChanged', {
            connected: true,
            sessionId: session.id,
            peerIp: session.peerIp,
            type: 'P', // Passive connection
        });
    }

    onError(error) {
        log.error('TCP server error:', error);
        this.emit('serverError', error);
    }

    getStatus() {
        return {
            isRunning: this.isRunning,
            config: this.config,
            statistics: {
                ...this.statistics,
                connections: this.sessions.size,
                uptime: this.statistics.startTime
                    ? Math.floor(
                          (Date.now() - this.statistics.startTime.getTime()) /
                              1000
                      )
                    : 0,
            },
            activeSessions: Array.from(this.sessions.values()).map(
                (session) => ({
                    id: session.id,
                    peerIp: session.peerIp,
                    peerPort: session.peerPort,
                    connectionTime: session.connectionTime,
                })
            ),
        };
    }

    sendToSession(sessionId, data) {
        const session = this.sessions.get(sessionId);
        if (session) {
            session.onSendRequest(data);
        } else {
            throw new Error(`Session ${sessionId} not found`);
        }
    }

    sendToAll(data) {
        for (const session of this.sessions.values()) {
            session.onSendRequest(data);
        }
    }

    getSessionCount() {
        return this.sessions.size;
    }

    getSessions() {
        return Array.from(this.sessions.values());
    }
}

export { TcpServerService, TcpSession };
