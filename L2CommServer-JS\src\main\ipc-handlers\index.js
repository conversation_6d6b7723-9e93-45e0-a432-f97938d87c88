import setupLogHandlers from './logs.js';

/**
 * Initialize all IPC handlers
 * @param {BrowserWindow} mainWindow - Main window instance
 */
function initializeIpcHandlers(mainWindow) {
    // Setup logging handlers
    setupLogHandlers(mainWindow);

    // Add other handler modules here as needed
    // setupServerHandlers(mainWindow);
    // setupPluginHandlers(mainWindow);
    // setupDatabaseHandlers(mainWindow);
}

export default initializeIpcHandlers;
