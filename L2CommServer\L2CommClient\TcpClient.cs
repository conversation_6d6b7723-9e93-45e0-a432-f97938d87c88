﻿using System;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using TcpClient = NetCoreServer.TcpClient;
using Serilog;
using Telegrams;

namespace L2CommClient
{
    class TCPClient : TcpClient
    {
        public TCPClient(string address, int port) : base(address, port) {}

        public event EventHandler<ConnEventArgs> ConnectionChanged = delegate { };

        public void DisconnectAndStop()
        {
            _stop = true;
            DisconnectAsync();
            while (IsConnected)
                Thread.Yield();
        }

        protected override void OnConnected()
        {
            Log.Information($"TCP TCP client connected a new session with Id {Id}");
            ConnectionChanged(this, new ConnEventArgs() { OnConnect = true });
        }

        protected override void OnDisconnected()
        {
            Log.Information($"TCP TCP client disconnected a session with Id {Id}");
            ConnectionChanged(this, new ConnEventArgs() { OnConnect = false });
            // Wait for a while...
            Thread.Sleep(1000);

            // Try to connect again
            if (!_stop)
                ConnectAsync();
        }

        protected override void OnReceived(byte[] buffer, long offset, long size)
        {
            Log.Information("Raw message received from server. Length {0}", (int)size);
            TelegramUtils.DebugRawTelegram(buffer, (int)size);
        }

        protected override void OnError(SocketError error)
        {
            Log.Information($"TCP client caught an error with code {error}");
        }

        private bool _stop;
    }

    public class ConnEventArgs : EventArgs
    {
        public bool OnConnect { get; set; }
    }

}
