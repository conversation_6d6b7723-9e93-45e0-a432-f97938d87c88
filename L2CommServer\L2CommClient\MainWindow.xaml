﻿<Window x:Class="L2CommClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.modernwpf.com/2019"
        ui:WindowHelper.UseModernWindowStyle="True"
        xmlns:local="clr-namespace:L2CommClient"
        mc:Ignorable="d"
        Title="MainWindow" MinHeight="700" MinWidth="1000" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen" Closing="Window_Closing" >
    <DockPanel x:Name="Main" LastChildFill="True">
        <DockPanel x:Name="panelTitleBar" Height="150" DockPanel.Dock="Top" Background="#FFF0F0F0" LastChildFill="True">
            <DockPanel HorizontalAlignment="Left" 
                       DockPanel.Dock="Left"
                       Width="100" Margin="5,0,0,0">
                <ui:FontIcon Glyph="&#xE74C;" FontSize="42"
                                 FontFamily="Segoe Fluent Icons"
                                 FontWeight="ExtraBold"
                                 Margin="0"/>
            </DockPanel>
            <DockPanel   HorizontalAlignment="Left"
                         DockPanel.Dock="Left">
                <GroupBox Grid.Row="1" Grid.Column="0" 
                          Width="220" Height="145" 
                          Header="服务器" Margin="5"
                         >
                    <StackPanel x:Name="ServerInfo">
                        <StackPanel Orientation="Horizontal" Margin="5,0,0,0" Height="40">
                            <Label Content="地址" Width="50" Margin="5,10,0,0"/>
                            <TextBox Margin="0,5,0,0" Height="20"
                                             Width="125" VerticalAlignment="Center"
                                             FontSize="13" Text="{Binding IpAddress}"
                                             IsEnabled="{Binding IsEnabledIpAddress}" />
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="5,0,0,0" Height="40">
                            <Label Content="地址" Width="50" Margin="5,10,0,0"/>
                            <TextBox Margin="0,5,0,0" 
                                             Width="125" VerticalAlignment="Center"
                                             FontSize="13"  Text="{Binding Port}"
                                            IsEnabled="{Binding IsEnabledPort}" />
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
            </DockPanel>
            <DockPanel  DockPanel.Dock="Left">
                <GroupBox Grid.Row="1" Grid.Column="0" 
                          Width="240" Height="145" 
                          Header="控制" Margin="5"
                            >
                    <DockPanel  HorizontalAlignment="Left">
                        <DockPanel x:Name="ServerState" Width="50" Margin="15,0,0,0" DockPanel.Dock="Left"
                                HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ui:FontIcon x:Name="ServerStateIcon" Glyph="{Binding ServerStateIcon}" FontSize="50"
                                 FontWeight="ExtraBold"
                                 FontFamily="Segoe Fluent Icons"
                                 Foreground="{Binding ServerStateIconColor}"
                                 Margin="0"/>
                        </DockPanel>
                        <DockPanel>
                            <StackPanel Orientation="Vertical" DockPanel.Dock="Left" Margin="5,0,0,0">
                                <StackPanel Orientation="Horizontal" DockPanel.Dock="Top" Margin="10,0,0,0" Height="40">
                                    <CheckBox x:Name="tbLogDebug"
                                    Margin="10,0,0,0"
                                    HorizontalAlignment="Left" Width="140"
                                     FontSize="13"
                                    Command="{Binding CommandToggleLogLevel}"
                                    IsChecked="{Binding IsCheckedLogLevel}" >
                                        显示DEBUG信息
                                    </CheckBox>
                                </StackPanel>
                                <StackPanel Orientation="Vertical" Margin="20,0,0,0">
                                    <Button x:Name="btnClearLog" 
                                        DockPanel.Dock="Bottom" 
                                        Width="130" Height="35" 
                                        BorderThickness="1"
                                        Margin="0" HorizontalAlignment="Left"
                                        Click="btnClearLog_Click">
                                        <StackPanel Orientation="Horizontal" Margin="10,0,10,0">
                                            <ui:FontIcon Glyph="&#xE75C;" FontSize="16"
                                 FontFamily="Segoe Fluent Icons"
                                 FontWeight="ExtraBold"
                                 Margin="0"/>
                                            <TextBlock Margin="10,2,0,3" VerticalAlignment="Center"
                                                       Width="60" Text="清除LOG"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </DockPanel>
                    </DockPanel>
                </GroupBox>
            </DockPanel>
            <DockPanel DockPanel.Dock="Left" HorizontalAlignment="Left" LastChildFill="True">
                <GroupBox Grid.Row="1" Grid.Column="0" 
                          Height="145" 
                          Header="发送" Margin="5"
                            >
                        <StackPanel Orientation="Vertical" DockPanel.Dock="Left" Margin="5,0,0,0">
                            <StackPanel HorizontalAlignment="Left" DockPanel.Dock="Top" Width="150"  
                                        Margin="10,0,0,0" Height="30">
                                <Button x:Name="btnSend1001" DockPanel.Dock="Bottom" 
                                                    Height="30" BorderThickness="1" 
                                                    Margin="7,0,0,0" HorizontalAlignment="Left"
                                                    Click="btnSend_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:FontIcon Glyph="&#xEDDA;" 
                                                 FontSize="14"
                                                 FontFamily="Segoe Fluent Icons"
                                                 FontWeight="ExtraBold"
                                                 Foreground="MediumBlue"
                                                 Margin="0"/>
                                        <TextBlock Margin="10,0,0,3" VerticalAlignment="Center"
                                                       Width="100"
                                                       Text="发送报文 201"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                            <StackPanel HorizontalAlignment="Left" DockPanel.Dock="Top" Width="150" 
                                        Margin="10,10,0,0" Height="30">
                                <Button x:Name="btnSend103" DockPanel.Dock="Bottom" 
                                                    Height="30" BorderThickness="1" 
                                                    Margin="7,0,0,0" HorizontalAlignment="Left"
                                                    Click="btnSend103_Click">
                                <StackPanel Orientation="Horizontal">
                                    <ui:FontIcon Glyph="&#xEDDA;" 
                                                 FontSize="14"
                                                 FontFamily="Segoe Fluent Icons"
                                                 FontWeight="ExtraBold"
                                                 Foreground="Green"
                                                 Margin="0"/>
                                    <TextBlock Margin="10,0,0,3" VerticalAlignment="Center"
                                                       Width="100"
                                                       Text="发送报文 103"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
            </DockPanel>

        </DockPanel>

        <DockPanel x:Name="panelLogWindow" Background="#FFF0F0F0">
            <Border BorderThickness="1" BorderBrush="#FF363636" Margin="5,0,0,0">
                <DockPanel Background="#FFF0F0F0">
                    <RichTextBox x:Name="LogRichTextBox"
                            Background="#FFFAFAFA" Foreground="Gray"
                            FontFamily="Cascadia Mono, Consolas, Courier New, monospace"
                            FontSize="14"
                            VerticalScrollBarVisibility="Visible" 
                            ScrollViewer.CanContentScroll="True" 
                            TextChanged="LogRichTextBox_TextChanged"/>
                </DockPanel>
            </Border>
        </DockPanel>

    </DockPanel>
</Window>
