import { BrowserWindow, ipcMain } from 'electron'
import log, { LevelOption } from 'electron-log'

export default (_mainWindow: BrowserWindow) => {
    ipcMain.handle('get-log-level', () => {
        return log.transports.console.level
    })

    ipcMain.handle('set-log-level', (_, level: LevelOption) => {
        log.transports.console.level = level
        log.transports.ipc.level = level
        return true
    })

    const logTypes = ['error', 'warn', 'info', 'verbose', 'debug']
    ipcMain.handle('add-log', (_, level: LevelOption, ...args: any[]) => {
        if (logTypes.includes(level as string)) {
            ;(log[level as keyof typeof log] as Function)(...args)
        } else {
            log.info(...args)
        }
        return true
    })
}
