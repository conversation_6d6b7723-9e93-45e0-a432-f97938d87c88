{"format": 1, "restore": {"D:\\Dev\\L2CommServer\\L2CommClient\\L2CommClient.csproj": {}}, "projects": {"D:\\Dev\\L2CommServer\\L2CommClient\\L2CommClient.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Dev\\L2CommServer\\L2CommClient\\L2CommClient.csproj", "projectName": "L2CommClient", "projectPath": "D:\\Dev\\L2CommServer\\L2CommClient\\L2CommClient.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Dev\\L2CommServer\\L2CommClient\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows10.0.18362.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows10.0.18362": {"targetAlias": "net6.0-windows10.0.18362.0", "projectReferences": {"D:\\Dev\\L2CommServer\\Services\\Services.csproj": {"projectPath": "D:\\Dev\\L2CommServer\\Services\\Services.csproj"}, "D:\\Dev\\L2CommServer\\Telegrams\\Telegrams.csproj": {"projectPath": "D:\\Dev\\L2CommServer\\Telegrams\\Telegrams.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows10.0.18362": {"targetAlias": "net6.0-windows10.0.18362.0", "dependencies": {"ModernWpfUI": {"target": "Package", "version": "[0.9.4, )"}, "NetCoreServer": {"target": "Package", "version": "[5.1.0, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.Sinks.RichTextBox.Wpf": {"target": "Package", "version": "[1.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.18362.24, 10.0.18362.24]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Dev\\L2CommServer\\S7.Net\\S7.Net.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Dev\\L2CommServer\\S7.Net\\S7.Net.csproj", "projectName": "S7netplus", "projectPath": "D:\\Dev\\L2CommServer\\S7.Net\\S7.Net.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Dev\\L2CommServer\\S7.Net\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0", "net6.0", "netstandard1.3", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}, "net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "netstandard1.3": {"targetAlias": "netstandard1.3", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.SourceLink.GitHub": {"suppressParent": "All", "target": "Package", "version": "[1.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[5.0.0, 5.0.0]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[5.0.0, 5.0.0]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[5.0.0, 5.0.0]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.202\\RuntimeIdentifierGraph.json"}, "net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.SourceLink.GitHub": {"suppressParent": "All", "target": "Package", "version": "[1.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.202\\RuntimeIdentifierGraph.json"}, "netstandard1.3": {"targetAlias": "netstandard1.3", "dependencies": {"Microsoft.SourceLink.GitHub": {"suppressParent": "All", "target": "Package", "version": "[1.0.0, )"}, "NETStandard.Library": {"target": "Package", "version": "[1.6.1, )", "autoReferenced": true}, "System.Memory": {"target": "Package", "version": "[4.5.5, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.202\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.SourceLink.GitHub": {"suppressParent": "All", "target": "Package", "version": "[1.0.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.Memory": {"target": "Package", "version": "[4.5.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Dev\\L2CommServer\\Services\\Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Dev\\L2CommServer\\Services\\Services.csproj", "projectName": "Services", "projectPath": "D:\\Dev\\L2CommServer\\Services\\Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Dev\\L2CommServer\\Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.66, )"}, "Microsoft.Composition": {"target": "Package", "version": "[1.0.31, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.1, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.202\\RuntimeIdentifierGraph.json"}}}, "D:\\Dev\\L2CommServer\\Telegrams\\Telegrams.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Dev\\L2CommServer\\Telegrams\\Telegrams.csproj", "projectName": "Telegrams", "projectPath": "D:\\Dev\\L2CommServer\\Telegrams\\Telegrams.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Dev\\L2CommServer\\Telegrams\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\Dev\\L2CommServer\\S7.Net\\S7.Net.csproj": {"projectPath": "D:\\Dev\\L2CommServer\\S7.Net\\S7.Net.csproj"}, "D:\\Dev\\L2CommServer\\Services\\Services.csproj": {"projectPath": "D:\\Dev\\L2CommServer\\Services\\Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.202\\RuntimeIdentifierGraph.json"}}}}}