export interface ShortcutRecord {
    event: string
    title: string
    keys: string[]
    /** Needed for default shortcuts */
    os: NodeJS.Platform[]
}

/**
 * Default shortcuts
 */
const shortcuts: ShortcutRecord[] = [
    {
        event: 'save-content',
        title: '保存标签内容',
        keys: ['CommandOrControl+S'],
        os: ['darwin', 'linux', 'win32'],
    },
    {
        event: 'close-tab',
        title: '关闭标签',
        keys: ['CommandOrControl+W'],
        os: ['darwin', 'linux', 'win32'],
    },
    {
        event: 'next-tab',
        title: '下一个标签',
        keys: ['CommandOrControl+Right'],
        os: ['darwin', 'win32'],
    },
    {
        event: 'prev-tab',
        title: '上一个标签',
        keys: ['CommandOrControl+Left'],
        os: ['darwin', 'win32'],
    },
]

export { shortcuts }
