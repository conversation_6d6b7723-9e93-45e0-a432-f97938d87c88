﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:tb="http://www.hardcodet.net/taskbar"
                    xmlns:local="clr-namespace:L2CommServer">
    <!-- The taskbar context menu - the first row is a dummy to show off simple data binding -->
    <!--
        The "shared" directive is needed if we reopen the sample window a few times - WPF will otherwise
        reuse the same context menu (which is a resource) again (which will have its DataContext set to the old TaskbarIcon)
  -->
    <ContextMenu x:Shared="false" x:Key="SysTrayMenu">
        <MenuItem Header="显示窗口" Command="{Binding ShowWindowCommand}" />
        <MenuItem Header="隐藏窗口" Command="{Binding HideWindowCommand}" />
        <Separator />
        <MenuItem Header="退出" Command="{Binding ExitApplicationCommand}" />
    </ContextMenu>

    <!-- the application's NotifyIcon - started from App.xaml.cs. Declares its own view model. -->
    <tb:TaskbarIcon x:Key="NotifyIcon"
                    IconSource="/Resources/server.ico"
                    ToolTipText="Double-click for window, right-click for menu"
                    DoubleClickCommand="{Binding ShowWindowCommand}"
                    ContextMenu="{StaticResource SysTrayMenu}">

        <!-- self-assign a data context (could also be done programmatically) -->
        <tb:TaskbarIcon.DataContext>
            <local:NotifyIconViewModel />
        </tb:TaskbarIcon.DataContext>
    </tb:TaskbarIcon>


</ResourceDictionary>   