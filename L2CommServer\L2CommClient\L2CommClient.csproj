﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows10.0.18362.0</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>Resources\windows_client.ico</ApplicationIcon>
	  <SatelliteResourceLanguages>zh-CN</SatelliteResourceLanguages>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ModernWpfUI" Version="0.9.4" />
    <PackageReference Include="NetCoreServer" Version="5.1.0" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Sinks.RichTextBox.Wpf" Version="1.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Services\Services.csproj" />
    <ProjectReference Include="..\Telegrams\Telegrams.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="libinidotnet">
      <HintPath>..\AddiFiles\libinidotnet.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
