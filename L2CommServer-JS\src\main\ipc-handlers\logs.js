import { ipcMain } from 'electron';
import { loggingService } from '../services/LoggingService.js';

/**
 * IPC handlers for logging functionality
 * Based on FS-PSTF-L2 implementation
 * @param {BrowserWindow} mainWindow - Main window instance
 */
function setupLogHandlers(mainWindow) {
    // Set main window for logging service
    loggingService.setMainWindow(mainWindow);

    /**
     * Get current log level
     */
    ipcMain.handle('get-log-level', () => {
        return loggingService.getLogLevel();
    });

    /**
     * Set log level
     */
    ipcMain.handle('set-log-level', (_, level) => {
        loggingService.setLogLevel(level);
        return true;
    });

    /**
     * Add log entry from renderer process
     */
    ipcMain.handle('add-log', (_, level, ...args) => {
        loggingService.log(level, ...args);
        return true;
    });

    /**
     * Clear log buffer
     */
    ipcMain.handle('clear-logs', () => {
        loggingService.clearBuffer();
        return true;
    });

    /**
     * Get buffered logs
     */
    ipcMain.handle('get-buffered-logs', () => {
        return loggingService.getBufferedLogs();
    });
}

export default setupLogHandlers;
