{"name": "fs-pstf-l2", "productName": "抚顺特钢板材厂固溶炉 L2", "version": "0.2.0", "description": "抚顺特钢板材厂固溶炉 L2", "main": "./out/main/index.js", "author": "<PERSON>", "homepage": "https://www.tuo-bang.cn", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:win": "npm run build && electron-builder --win --config", "test": "npx playwright test", "codegen": "npx playwright codegen --electron out/main/index.js"}, "dependencies": {"@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "electron-log": "^5.4.1", "electron-store": "^8.2.0", "knex": "^3.1.0", "mitt": "^3.0.1", "pinia": "^3.0.3", "tedious": "^18.6.1"}, "devDependencies": {"@electron-toolkit/tsconfig": "^1.0.1", "@iconify/json": "^2.2.359", "@playwright/test": "^1.47.0", "@rushstack/eslint-patch": "^1.11.0", "@types/node": "^20.16.5", "@unocss/eslint-config": "^66.3.3", "@unocss/preset-rem-to-px": "^66.3.3", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "electron": "^35.2.1", "electron-builder": "^26.0.12", "electron-vite": "^3.1.0", "eslint": "^9.31.0", "eslint-plugin-vue": "^10.3.0", "naive-ui": "^2.42.0", "playwright": "^1.47.0", "sass": "^1.89.2", "typescript": "^5.8.3", "unocss": "^66.3.3", "vite": "^6.3.3", "vue": "^3.5.17", "vue-tsc": "^3.0.1"}}