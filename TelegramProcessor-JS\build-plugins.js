const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');

/**
 * Plugin Build System
 * Compiles plugins into single JavaScript files for easy deployment
 */
class PluginBuilder {
    constructor() {
        this.pluginsDir = path.join(__dirname, 'plugins');
        this.distDir = path.join(__dirname, 'dist');
        this.baseFile = path.join(__dirname, 'plugin-base.js');
        this.isWatching = false;
    }
    
    /**
     * Initialize the builder
     */
    async init() {
        // Ensure directories exist
        if (!fs.existsSync(this.distDir)) {
            fs.mkdirSync(this.distDir, { recursive: true });
        }
        
        console.log('Plugin Builder initialized');
        console.log(`Plugins directory: ${this.pluginsDir}`);
        console.log(`Output directory: ${this.distDir}`);
    }
    
    /**
     * Build all plugins
     */
    async buildAll() {
        console.log('Building all plugins...');
        
        try {
            const pluginFiles = fs.readdirSync(this.pluginsDir)
                .filter(file => file.endsWith('.js'))
                .filter(file => !file.startsWith('_')); // Ignore files starting with _
            
            console.log(`Found ${pluginFiles.length} plugin files`);
            
            for (const file of pluginFiles) {
                await this.buildPlugin(file);
            }
            
            console.log('All plugins built successfully');
            
        } catch (error) {
            console.error('Error building plugins:', error);
        }
    }
    
    /**
     * Build a single plugin
     * @param {string} filename - Plugin filename
     */
    async buildPlugin(filename) {
        try {
            const pluginPath = path.join(this.pluginsDir, filename);
            const outputPath = path.join(this.distDir, filename);
            
            console.log(`Building plugin: ${filename}`);
            
            // Read plugin base
            const baseContent = fs.readFileSync(this.baseFile, 'utf8');
            
            // Read plugin file
            const pluginContent = fs.readFileSync(pluginPath, 'utf8');
            
            // Create standalone plugin
            const standalonePlugin = this.createStandalonePlugin(baseContent, pluginContent, filename);
            
            // Write output file
            fs.writeFileSync(outputPath, standalonePlugin, 'utf8');
            
            console.log(`✓ Built: ${filename} -> dist/${filename}`);
            
        } catch (error) {
            console.error(`✗ Failed to build ${filename}:`, error.message);
        }
    }
    
    /**
     * Create standalone plugin by combining base and plugin code
     * @param {string} baseContent - Plugin base content
     * @param {string} pluginContent - Plugin content
     * @param {string} filename - Plugin filename
     * @returns {string} - Standalone plugin code
     */
    createStandalonePlugin(baseContent, pluginContent, filename) {
        // Remove require statements from plugin content
        const cleanPluginContent = pluginContent
            .replace(/const\s+\{[^}]+\}\s*=\s*require\([^)]+\);?\s*/g, '')
            .replace(/require\([^)]+\);?\s*/g, '');
        
        // Create standalone version
        const standalone = `/**
 * Standalone Plugin: ${filename}
 * Built on: ${new Date().toISOString()}
 * 
 * This file contains both the plugin base classes and the plugin implementation
 * It can be deployed as a single file without external dependencies
 */

// ============================================================================
// PLUGIN BASE CLASSES AND UTILITIES
// ============================================================================

${baseContent.replace(/module\.exports\s*=\s*\{[^}]+\};?\s*$/, '')}

// ============================================================================
// PLUGIN IMPLEMENTATION
// ============================================================================

${cleanPluginContent}

// ============================================================================
// STANDALONE EXPORT
// ============================================================================

// Make sure the plugin is properly exported for standalone use
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    if (typeof exportPlugin === 'function') {
        // Plugin uses exportPlugin helper
        // The plugin should already be exported via exportPlugin()
    } else {
        console.warn('Plugin does not use exportPlugin() - manual export may be required');
    }
} else if (typeof window !== 'undefined') {
    // Browser environment
    window.TelegramPlugin = module.exports;
}
`;
        
        return standalone;
    }
    
    /**
     * Start watching for changes
     */
    startWatching() {
        if (this.isWatching) {
            console.log('Already watching for changes');
            return;
        }
        
        console.log('Starting watch mode...');
        
        const watcher = chokidar.watch(path.join(this.pluginsDir, '*.js'), {
            ignored: /node_modules/,
            persistent: true,
            ignoreInitial: true
        });
        
        watcher.on('change', (filePath) => {
            const filename = path.basename(filePath);
            console.log(`Plugin changed: ${filename}`);
            this.buildPlugin(filename);
        });
        
        watcher.on('add', (filePath) => {
            const filename = path.basename(filePath);
            console.log(`New plugin added: ${filename}`);
            this.buildPlugin(filename);
        });
        
        watcher.on('unlink', (filePath) => {
            const filename = path.basename(filePath);
            const outputPath = path.join(this.distDir, filename);
            
            if (fs.existsSync(outputPath)) {
                fs.unlinkSync(outputPath);
                console.log(`Removed built plugin: ${filename}`);
            }
        });
        
        this.isWatching = true;
        console.log('Watching for plugin changes... Press Ctrl+C to stop');
    }
    
    /**
     * Clean build directory
     */
    clean() {
        console.log('Cleaning build directory...');
        
        if (fs.existsSync(this.distDir)) {
            const files = fs.readdirSync(this.distDir);
            for (const file of files) {
                fs.unlinkSync(path.join(this.distDir, file));
            }
            console.log(`Removed ${files.length} built files`);
        }
    }
    
    /**
     * Validate plugin file
     * @param {string} filename - Plugin filename
     * @returns {boolean} - True if valid
     */
    validatePlugin(filename) {
        try {
            const pluginPath = path.join(this.pluginsDir, filename);
            const content = fs.readFileSync(pluginPath, 'utf8');
            
            // Basic validation
            if (!content.includes('exportPlugin')) {
                console.warn(`Warning: ${filename} does not use exportPlugin()`);
                return false;
            }
            
            if (!content.includes('telegramNo')) {
                console.warn(`Warning: ${filename} does not define telegramNo`);
                return false;
            }
            
            return true;
            
        } catch (error) {
            console.error(`Error validating ${filename}:`, error.message);
            return false;
        }
    }
    
    /**
     * Get build statistics
     */
    getStats() {
        const pluginFiles = fs.existsSync(this.pluginsDir) ? 
            fs.readdirSync(this.pluginsDir).filter(f => f.endsWith('.js')) : [];
        
        const builtFiles = fs.existsSync(this.distDir) ? 
            fs.readdirSync(this.distDir).filter(f => f.endsWith('.js')) : [];
        
        return {
            totalPlugins: pluginFiles.length,
            builtPlugins: builtFiles.length,
            pluginsDir: this.pluginsDir,
            distDir: this.distDir
        };
    }
}

// ============================================================================
// CLI INTERFACE
// ============================================================================

async function main() {
    const args = process.argv.slice(2);
    const builder = new PluginBuilder();
    
    await builder.init();
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
Plugin Builder Usage:

  node build-plugins.js [options]

Options:
  --single <filename>    Build a single plugin file
  --watch               Watch for changes and rebuild automatically
  --clean               Clean the build directory
  --stats               Show build statistics
  --help, -h            Show this help message

Examples:
  node build-plugins.js                    # Build all plugins
  node build-plugins.js --single plugin.js # Build specific plugin
  node build-plugins.js --watch            # Watch mode
  node build-plugins.js --clean            # Clean build directory
        `);
        return;
    }
    
    if (args.includes('--clean')) {
        builder.clean();
        return;
    }
    
    if (args.includes('--stats')) {
        const stats = builder.getStats();
        console.log('Build Statistics:');
        console.log(`  Total plugins: ${stats.totalPlugins}`);
        console.log(`  Built plugins: ${stats.builtPlugins}`);
        console.log(`  Plugins directory: ${stats.pluginsDir}`);
        console.log(`  Output directory: ${stats.distDir}`);
        return;
    }
    
    const singleIndex = args.indexOf('--single');
    if (singleIndex !== -1 && args[singleIndex + 1]) {
        const filename = args[singleIndex + 1];
        await builder.buildPlugin(filename);
        return;
    }
    
    if (args.includes('--watch')) {
        await builder.buildAll();
        builder.startWatching();
        
        // Keep process alive
        process.on('SIGINT', () => {
            console.log('\nStopping watch mode...');
            process.exit(0);
        });
        
        // Prevent process from exiting
        setInterval(() => {}, 1000);
        
    } else {
        await builder.buildAll();
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = PluginBuilder;
