import { executeQuery } from '@/models/dbUtils'

export interface Pdi {
    id: number | null

    prg_no: string // 轧制计划号
    seq_no: number // 轧制顺序号
    mat_no: string // 板坯号
    steel_grade: string // 钢种
    slab_width: number // 板坯宽度
    slab_thick: number // 板坯厚度
    slab_length: number // 板坯长度
    slab_weight: number // 板坯重量

    record_time: Date // 创建时间

    // extra keys
    isEdit: boolean
    isDeletable: boolean
}

export interface PdiFilterOptions {
    prg_no: string | null
    steel_grade: string | null
}
// extra keys that does not belong to table
//const extraKeys: (keyof Pdi)[] = [
//    <keyof Pdi>'isEdit',
//    <keyof Pdi>'isDeletable',
//`]

const TableName = 'PDI'

// log function stub
async function fetchById(id: number): Promise<Pdi | undefined> {
    if (id > 0) {
        try {
            const result = await executeQuery(
                'SELECT * FROM ' + TableName + ' WHERE id = ? ',
                [id],
            )
            return result[0] as Pdi
        } catch (err) {
            window.electron.ipcRenderer.invoke('add-log', 'error', err)
            return undefined
        }
    } else {
        window.electron.ipcRenderer.invoke(
            'add-log',
            'warn',
            'Fetch Pdi by id, but id is 0.',
        )

        return undefined
    }
}

// fetch all Pdis
async function fetch(
    pageStart: number,
    pageSize: number,
    order: string,
    filter: PdiFilterOptions,
): Promise<Pdi[]> {
    try {
        let query = 'SELECT * FROM ' + TableName + ' WHERE 1=1'
        const params: (string | number)[] = []

        // filters
        if (typeof filter?.prg_no === 'string' && filter.prg_no.length > 0) {
            query += ` AND prg_no like '${filter.prg_no}%'`
        }
        if (
            typeof filter?.steel_grade === 'string' &&
            filter.steel_grade.length > 0
        ) {
            query += ` AND steel_grade like '%${filter.steel_grade}%'`
        }

        query += ` ORDER BY id ${order} OFFSET ? ROWS FETCH NEXT ? ROWS ONLY`
        params.push(pageStart, pageSize)

        const telegrams = await executeQuery(query, params)
        return telegrams as Pdi[]
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return []
    }
}

// get total record count
async function count(filter?: PdiFilterOptions): Promise<number> {
    try {
        let query = 'SELECT COUNT(*) as count FROM ' + TableName + ' WHERE 1=1'
        const params: string[] = []

        // filters
        if (typeof filter?.prg_no === 'string' && filter.prg_no.length > 0) {
            query += ` AND prg_no = '${filter.prg_no}'`
        }
        if (
            typeof filter?.steel_grade === 'string' &&
            filter.steel_grade.length > 0
        ) {
            query += ` AND steel_grade = '${filter.steel_grade}'`
        }

        const result = await executeQuery(query, params)
        return result[0].count as number
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// add a new Pdi
async function add(pdi: Pdi): Promise<number> {
    try {
        const result = await executeQuery(
            'INSERT INTO ' +
                TableName +
                ' (prg_no, seq_no, mat_no, steel_grade, slab_width, slab_thick, slab_length, slab_weight, shift_no, shift_group, used) ' +
                ' VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?); ',
            [
                pdi.prg_no,
                pdi.seq_no,
                pdi.mat_no,
                pdi.steel_grade,
                pdi.slab_width,
                pdi.slab_thick,
                pdi.slab_length,
                pdi.slab_weight,
                0, // fixed data for now
                0, // fixed data for now
                0, // fixed data for now
            ],
        )
        return result.affectedRows
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// update a Pdi
async function update(id: number, pdi: Pdi): Promise<number> {
    try {
        const result = await executeQuery(
            'UPDATE ' +
                TableName +
                ' SET prg_no = ?, seq_no = ?, mat_no = ?, steel_grade = ?, slab_width = ?, slab_thick = ?, slab_length = ?, slab_weight = ? WHERE id = ?',
            [
                pdi.prg_no,
                pdi.seq_no,
                pdi.mat_no,
                pdi.steel_grade,
                pdi.slab_width,
                pdi.slab_thick,
                pdi.slab_length,
                pdi.slab_weight,
                id,
            ],
        )
        return result.affectedRows
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// delete a Pdi
async function del(id: number): Promise<number> {
    try {
        const result = await executeQuery(
            'DELETE FROM ' + TableName + ' WHERE id = ?',
            [id],
        )
        return result.affectedRows
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// export functions
export default {
    fetchById,
    fetch,
    count,
    add,
    update,
    del,
}
