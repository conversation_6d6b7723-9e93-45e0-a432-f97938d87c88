export function useElectronStore(storeName: string) {
    return {
        get: (key: string, defaultValue?: any) => {
            try {
                return window.store.get(storeName, key, defaultValue)
            } catch (error) {
                console.error(`获取存储值时出错: ${error}`)
                return defaultValue
            }
        },
        set: (key: string, value: any) => {
            try {
                // 确保值可以被序列化
                const serializedValue = JSON.parse(JSON.stringify(value))
                return window.store.set(storeName, key, serializedValue)
            } catch (error) {
                console.error(`设置存储值时出错: ${error}`)
            }
        },
        delete: (key: string) => {
            try {
                return window.store.delete(storeName, key)
            } catch (error) {
                console.error(`删除存储值时出错: ${error}`)
            }
        },
    }
}
