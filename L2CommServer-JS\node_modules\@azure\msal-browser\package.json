{"name": "@azure/msal-browser", "author": {"name": "Microsoft", "email": "<EMAIL>", "url": "https://www.microsoft.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/AzureAD/microsoft-authentication-library-for-js.git"}, "version": "4.15.0", "description": "Microsoft Authentication Library for js", "keywords": ["implicit", "authorization code", "PKCE", "js", "AAD", "msal", "o<PERSON>h"], "type": "module", "sideEffects": false, "main": "./lib/msal-browser.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./custom-auth": {"import": {"types": "./dist/custom-auth-path/custom_auth/index.d.ts", "default": "./dist/custom-auth-path/custom_auth/index.mjs"}, "require": {"types": "./lib/custom-auth-path/types/custom_auth/index.d.ts", "default": "./lib/custom-auth-path/msal-custom-auth.cjs"}}, ".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./lib/types/index.d.ts", "default": "./lib/msal-browser.cjs"}}, "./package.json": "./package.json"}, "engines": {"node": ">=0.8.0"}, "beachball": {"disallowedChangeTypes": ["major"]}, "directories": {"test": "test"}, "files": ["dist", "lib", "src"], "scripts": {"clean": "shx rm -rf dist lib", "clean:coverage": "rimraf ../../.nyc_output/*", "lint": "eslint src --ext .ts", "lint:fix": "npm run lint -- --fix", "test": "jest --force<PERSON>xit", "test:coverage": "jest --coverage", "test:coverage:only": "npm run clean:coverage && npm run test:coverage", "build:all": "cd ../.. && npm run build --workspace=@azure/msal-common --workspace=@azure/msal-browser", "build:modules": "rollup -c --strictDeprecations --bundleConfigAsCjs", "build:modules:watch": "rollup -cw --bundleConfigAsCjs", "build": "npm run clean && npm run build:modules", "prepack": "npm run build:all", "format:check": "prettier --ignore-path .gitignore --check src test", "format:fix": "prettier --ignore-path .gitignore --write src test", "apiExtractor": "api-extractor run"}, "devDependencies": {"@azure/storage-blob": "^12.2.1", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/preset-env": "^7.7.1", "@babel/preset-typescript": "^7.7.2", "@microsoft/api-extractor": "^7.43.4", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.5.0", "@types/node": "^20.3.1", "dotenv": "^8.2.0", "eslint-config-msal": "file:../../shared-configs/eslint-config-msal", "fake-indexeddb": "^3.1.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-junit": "^16.0.0", "msal-test-utils": "file:../../shared-test-utils", "prettier": "^2.8.7", "rimraf": "^3.0.0", "rollup": "^4.22.4", "rollup-msal": "file:../../shared-configs/rollup-msal", "shx": "^0.3.2", "ssri": "^8.0.1", "ts-jest": "^29.2.5", "ts-jest-resolver": "^2.0.1", "tslib": "^1.10.0", "typescript": "^4.9.5"}, "dependencies": {"@azure/msal-common": "15.8.1"}}