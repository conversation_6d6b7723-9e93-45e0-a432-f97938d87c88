/**
 * Common interfaces and type definitions for L2CommServer-JS
 */

/**
 * Base telegram header structure
 */
class TelegramHeader {
    constructor() {
        this.telNo = 0; // Telegram number (2 bytes)
        this.telLen = 0; // Telegram length (2 bytes)
        this.telCounter = 0; // Telegram counter (2 bytes)
        this.peerIp = ''; // Peer IP address
        this.direction = ''; // 'R' for received, 'S' for sent
        this.headerType = ''; // Header type identifier
    }

    /**
     * Get header length based on telegram number
     * @param {number} telNo - Telegram number
     * @returns {number} - Header length in bytes
     */
    static getHeaderLength(telNo) {
        return telNo < 1000 ? 20 : 8;
    }

    /**
     * Determine header type based on telegram number
     * @param {number} telNo - Telegram number
     * @returns {string} - Header type ('TelegramHeader20' or 'TelegramHeader8')
     */
    static getHeaderType(telNo) {
        return telNo < 1000 ? 'TelegramHeader20' : 'TelegramHeader8';
    }
}

/**
 * 20-byte telegram header structure (for telNo < 1000)
 * Format: telNo(2) + telLen(2) + sendId(2) + recId(2) + createTime(8) + telCounter(2) + spare(2)
 */
class TelegramHeader20 extends TelegramHeader {
    constructor() {
        super();
        this.sendId = ''; // Sender ID (2 bytes)
        this.recId = ''; // Receiver ID (2 bytes)
        this.createTime = null; // Creation timestamp (8 bytes)
        this.spare = 0; // Spare bytes (2 bytes)
        this.headerType = 'TelegramHeader20';
    }

    static HEADER_LENGTH = 20;
}

/**
 * 8-byte telegram header structure (for telNo >= 1000)
 * Format: telNo(2) + telLen(2) + telCounter(2) + spare(2)
 */
class TelegramHeader8 extends TelegramHeader {
    constructor() {
        super();
        this.spare = 0; // Spare bytes (2 bytes)
        this.headerType = 'TelegramHeader8';
    }

    static HEADER_LENGTH = 8;
}

/**
 * Server configuration structure
 */
class ServerConfig {
    constructor() {
        this.server = {
            ip: '127.0.0.1',
            port: 1111,
            title: 'L2 Communication Server',
        };

        this.database = {
            host: 'localhost',
            name: 'L2',
            user: 'sa',
            password: '',
        };

        this.clients = [];
        this.cyclicSends = [];
    }
}

/**
 * Client connection configuration
 */
class ClientConfig {
    constructor() {
        this.ip = '';
        this.port = 80;
    }
}

/**
 * Cyclic send configuration
 */
class CyclicSendConfig {
    constructor() {
        this.peerIp = '';
        this.telNo = 0;
        this.interval = 5; // seconds
    }
}

/**
 * Connection information
 */
class ConnectionInfo {
    constructor() {
        this.sessionId = '';
        this.peerIp = '';
        this.connected = false;
        this.connectionTime = null;
        this.type = 'P'; // 'P' for passive, 'A' for active
    }
}

/**
 * Statistics data
 */
class Statistics {
    constructor() {
        this.connections = 0;
        this.telegramsReceived = 0;
        this.telegramsSent = 0;
        this.uptime = 0;
        this.startTime = null;
    }
}

/**
 * Log message structure
 */
class LogMessage {
    constructor(level, message, timestamp = null) {
        this.level = level; // 'debug', 'info', 'warn', 'error'
        this.message = message;
        this.timestamp = timestamp || new Date();
    }
}

/**
 * Plugin information
 */
class PluginInfo {
    constructor() {
        this.name = '';
        this.telegramNo = 0;
        this.type = ''; // 'receive' or 'send'
        this.status = 'loaded'; // 'loaded', 'error', 'disabled'
        this.filePath = '';
    }
}

/**
 * Event types for the event aggregator
 */
const EventTypes = {
    SERVER_STARTED: 'server:started',
    SERVER_STOPPED: 'server:stopped',
    CONNECTION_CHANGED: 'connection:changed',
    TELEGRAM_RECEIVED: 'telegram:received',
    TELEGRAM_SENT: 'telegram:sent',
    LOG_MESSAGE: 'log:message',
    PLUGIN_LOADED: 'plugin:loaded',
    PLUGIN_ERROR: 'plugin:error',
};

export {
    TelegramHeader,
    TelegramHeader20,
    TelegramHeader8,
    IMessagePlugin,
    IReceiveMessagePlugin,
    ISendMessagePlugin,
    ServerConfig,
    ClientConfig,
    CyclicSendConfig,
    ConnectionInfo,
    Statistics,
    LogMessage,
    PluginInfo,
    EventTypes,
};
