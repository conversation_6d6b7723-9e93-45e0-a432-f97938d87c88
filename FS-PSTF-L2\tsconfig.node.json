{
    "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
    "include": [
        "electron.vite.config.*", 
        "src/main/**/*", 
        "src/preload/*",
        "src/common/**/*",
    ],
    "compilerOptions": {
        "types": ["electron-vite/node"],
        "baseUrl": ".",
        "paths": {
            "@common/*": ["src/common/*"],
            "@images/*": ["src/renderer/src/images/*"],
        },
        "esModuleInterop": true
    }
}
