// file handling
import * as fs from 'fs'
import { ipcMain, dialog, BrowserWindow } from 'electron'

export default (mainWindow: BrowserWindow) => {
    // 监听渲染进程发出的save-file事件
    ipcMain.handle(
        'save-file',
        async (_event, savePath: string, _content: string) => {
            //const fileArr = path.split('/')
            //const ext = path.extname(savePath)
            //const ext = '.txt'
            const filters = [{ name: '文本文件', extensions: ['txt', '*'] }]
            /*
            if (ext) {
                filters.unshift({
                    name: '',
                    extensions: [ext.match(/[a-zA-Z]+$/)[0]],
                })
            }
            */
            let result
            // Perform the file saving action here
            const res = await dialog
                .showSaveDialog(mainWindow, {
                    title: '保存文件',
                    defaultPath: savePath,
                    filters,
                })
                .catch((err) => {
                    console.log(err)
                    result = {
                        message: '打开文件对话框时发生错误',
                        type: 'error',
                    }
                })

            if (res?.filePath) {
                /*
                fs.writeFile(filePath, content, (err) => {
                    if (err) {
                        result = 'error occurred'
                    } else {
                        result = 'success'
                    }
                    */
                result = {
                    message: '文件保存成功',
                    type: 'success',
                }
            } else {
                result = {
                    message: '放弃保存',
                    type: 'info',
                }
            }

            return result
        },
    )

    // check if the db folder exists，create one if not
    ipcMain.handle('check-db-folder', (_event, filePath) => {
        if (!fs.existsSync(filePath)) {
            fs.mkdirSync(filePath, { recursive: true })
        }
    })

    // check a folder exists
    ipcMain.handle('check-folder-exists', (_event, path) => {
        if (fs.existsSync(path)) {
            return true
        } else {
            return false
        }
    })
}
