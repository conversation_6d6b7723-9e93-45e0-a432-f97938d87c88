<template>
    <n-drawer
        v-model:show="isVisible"
        :height="480"
        placement="bottom"
        @after-leave="closeViewer"
    >
        <n-drawer-content>
            <template #header>
                <n-button @click="isVisible = false"> 关闭 </n-button>
            </template>
            <n-descriptions
                label-placement="left"
                bordered
                :label-style="{ width: '120px' }"
                :column="5"
            >
                <n-descriptions-item label="板坯号">
                    {{ row?.mat_no }}
                </n-descriptions-item>

                <n-descriptions-item label="班次">
                    {{ row?.shift_no }}
                </n-descriptions-item>
                <n-descriptions-item label="班组">
                    {{ row?.shift_group }}
                </n-descriptions-item>
                <n-descriptions-item label="进入1区时间">
                    {{ row?.fnc_z1_time }}
                </n-descriptions-item>
                <n-descriptions-item label="进入2区时间">
                    {{ row?.fnc_z2_time }}
                </n-descriptions-item>
                <n-descriptions-item label="进入3区时间">
                    {{ row?.fnc_z3_time }}
                </n-descriptions-item>
                <n-descriptions-item label="进入4区时间">
                    {{ row?.fnc_z4_time }}
                </n-descriptions-item>
                <n-descriptions-item label="进入5区时间">
                    {{ row?.fnc_z5_time }}
                </n-descriptions-item>
                <n-descriptions-item label="进入6区时间">
                    {{ row?.fnc_z6_time }}
                </n-descriptions-item>
                <n-descriptions-item label="Z1最低温度">
                    {{ row?.fnc_min_temp_z1 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z1最高温度">
                    {{ row?.fnc_max_temp_z1 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z2最低温度">
                    {{ row?.fnc_min_temp_z2 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z2最高温度">
                    {{ row?.fnc_max_temp_z2 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z3最低温度">
                    {{ row?.fnc_min_temp_z3 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z3最高温度">
                    {{ row?.fnc_max_temp_z3 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z4最低温度">
                    {{ row?.fnc_min_temp_z4 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z4最高温度">
                    {{ row?.fnc_max_temp_z4 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z5最低温度">
                    {{ row?.fnc_min_temp_z5 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z5最高温度">
                    {{ row?.fnc_max_temp_z5 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z6最低温度">
                    {{ row?.fnc_min_temp_z6 }}
                </n-descriptions-item>
                <n-descriptions-item label="Z6最高温度">
                    {{ row?.fnc_max_temp_z6 }}
                </n-descriptions-item>
                <n-descriptions-item label="实际加热时间">
                    {{ row?.total_heat_time }}
                </n-descriptions-item>
                <n-descriptions-item label="创建时间" :span="2">
                    {{
                        row?.create_time.toLocaleString('zh-CN', {
                            timeZone: 'UTC',
                        })
                    }}
                </n-descriptions-item>
            </n-descriptions>
        </n-drawer-content>
    </n-drawer>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue'
import {
    NDrawer,
    NDrawerContent,
    NDescriptions,
    NDescriptionsItem,
    NButton,
} from 'naive-ui'
import { dbTelegram111, Telegram111 } from '@/models'

// 接收父组件传递过来的值
const props = defineProps({
    viewingId: Number,
})

const emits = defineEmits(['finish-view'])
const isVisible = ref(false)

// fetch data from database
const row = ref<Telegram111 | null>(null)

watch(
    () => props.viewingId,
    async (newId) => {
        if (newId && newId > 0) {
            row.value = (await dbTelegram111.fetchById(newId)) as Telegram111
            isVisible.value = true
        } else {
            isVisible.value = false
        }
    },
    { immediate: true },
)

const closeViewer = () => {
    emits('finish-view')
}
</script>

<style scoped>
:deep(.n-drawer-header__main) {
    display: flex;
    justify-content: flex-end;
    width: 100%;
}
</style>
