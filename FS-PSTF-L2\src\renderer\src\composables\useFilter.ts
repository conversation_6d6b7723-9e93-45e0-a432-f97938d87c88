import { ref, watch } from 'vue'

export function useFilter<T>(
    onFilterChange: () => void,
    debounceMs: number = 300,
) {
    // initialize filter
    const filter = ref<T>({} as T)

    let debounceTimer: NodeJS.Timeout | null = null

    const debouncedFilterChange = () => {
        if (debounceTimer) {
            clearTimeout(debounceTimer)
        }
        debounceTimer = setTimeout(() => {
            onFilterChange()
        }, debounceMs)
    }

    watch(
        () => filter.value,
        () => debouncedFilterChange(),
        { deep: true },
    )

    const updateFilter = (key: keyof T, value: any) => {
        // if filter has key
        if (key in filter.value) {
            filter.value[key] = value
        }
    }

    return {
        filter,
        updateFilter,
    }
}
