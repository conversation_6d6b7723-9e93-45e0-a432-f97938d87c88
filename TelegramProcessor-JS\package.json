{"name": "telegram-processor-js", "version": "1.0.0", "description": "Telegram Processing Plugins for L2CommServer-JS", "main": "plugin-base.js", "scripts": {"build": "vite build", "build:legacy": "node build-plugins.js", "build:single": "node build-plugins.js --single", "watch": "vite build --watch", "watch:legacy": "node build-plugins.js --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["telegram", "plugin", "l2", "communication", "server"], "author": "L2CommServer Team", "license": "MIT", "dependencies": {}, "devDependencies": {"vite": "^6.3.3", "rimraf": "^5.0.5", "chokidar": "^3.5.3"}}