; All comments start with ;
;DATABASE defines the database to use
[DATABASE]
; Database server host, default is localhost
DB_HOST=***************
; Database name, default is L2
DB_NAME=FSSLRF
; Database user, default is sa
DB_USER=sa
; Database password, default is empty
DB_PASSWORD="h3r3w3g0#"

; SERVER defines ip address and port the server listens on
[SERVER]
IP=***************
PORT=2000
TITLE=L2 Communication Server

;CLIENT defines actively connected peers
;multiple CLIENT sections allowed
[CLIENT]
IP=**************
PORT=80

;CYCLIC_SEND defined cyclic (periodic) telegrams send out
;telegram to be sent is defined by TEL_NO
;telegram processor is called from Plugins
;multiple CYCLIC_SEND sections allowed
[CYCLIC_SEND]
PEER_IP=127.0.0.1
TEL_NO=21
INTERVAL=5
