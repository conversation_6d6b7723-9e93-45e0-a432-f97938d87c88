﻿using Serilog;
using Serilog.Core;
using System;
using System.Windows;

namespace L2CommClient
{
    public class UICommands 
    {
        // properties        
        public bool IsCheckedLogLevel { get; set; }

        // define UI commands
        public Command CommandToggleLogLevel{ get; private set; }
        public LoggingLevelSwitch LogLevelSwitch { get; private set; }


        // constructor
        public UICommands()
        {
            // define actions like:
            CommandToggleLogLevel = new Command(ToggleLogLevel);
            LogLevelSwitch = new LoggingLevelSwitch();
            // default toggle status is Checked
            LogLevelSwitch.MinimumLevel = Serilog.Events.LogEventLevel.Debug;
            IsCheckedLogLevel = true;
        }

        // Actions for the command
        private void ToggleLogLevel()
        {
            // toggle log level
            if (IsCheckedLogLevel)
            {
                // set log level to debug
                Log.Information("Change log level to DEBUG.");
                Application.Current.Dispatcher.Invoke((Action)(() =>
                {
                    LogLevelSwitch.MinimumLevel = Serilog.Events.LogEventLevel.Debug;
                }));
            } else
            {
                Log.Information("Change log level to INFO.");
                Application.Current.Dispatcher.Invoke((Action)(() =>
                {
                    LogLevelSwitch.MinimumLevel = Serilog.Events.LogEventLevel.Information;
                }));
            }
        }

    }
}
