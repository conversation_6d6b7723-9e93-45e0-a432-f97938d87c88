﻿#pragma checksum "..\..\..\..\..\..\ViewServer\ServerView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4FC5BEE1783DCE545674599EC53F2D23362CF0BD"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using L2CommServer;
using ModernWpf;
using ModernWpf.Controls;
using ModernWpf.Controls.Primitives;
using ModernWpf.DesignTime;
using ModernWpf.Markup;
using ModernWpf.Media.Animation;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace L2CommServer {
    
    
    /// <summary>
    /// ServerView
    /// </summary>
    public partial class ServerView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 11 "..\..\..\..\..\..\ViewServer\ServerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DockPanel panelParam;
        
        #line default
        #line hidden
        
        
        #line 15 "..\..\..\..\..\..\ViewServer\ServerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ServerInfo;
        
        #line default
        #line hidden
        
        
        #line 18 "..\..\..\..\..\..\ViewServer\ServerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbxIpAddress;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\..\ViewServer\ServerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnStartStop;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\..\..\ViewServer\ServerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DockPanel panelStatus;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\..\ViewServer\ServerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel Statistics;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\..\ViewServer\ServerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView ConnectionInfo;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/L2CommServer;V0.3.0.0;component/viewserver/serverview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\ViewServer\ServerView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.panelParam = ((System.Windows.Controls.DockPanel)(target));
            return;
            case 2:
            this.ServerInfo = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.cbxIpAddress = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.btnStartStop = ((System.Windows.Controls.Button)(target));
            
            #line 43 "..\..\..\..\..\..\ViewServer\ServerView.xaml"
            this.btnStartStop.Click += new System.Windows.RoutedEventHandler(this.btnStartStop_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.panelStatus = ((System.Windows.Controls.DockPanel)(target));
            return;
            case 6:
            this.Statistics = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.ConnectionInfo = ((System.Windows.Controls.ListView)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

