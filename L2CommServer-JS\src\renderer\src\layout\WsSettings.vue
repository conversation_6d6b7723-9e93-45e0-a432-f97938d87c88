<template>
    <div class="panel">
        <n-card hoverable title="设置" class="w-650 mt-20">
            <n-tabs type="card" default-value="general" size="medium">
                <n-tab-pane name="general" tab="常用">
                    <SettingsApp />
                </n-tab-pane>
                <n-tab-pane name="database" tab="数据库">
                    <SettingsDb />
                </n-tab-pane>
            </n-tabs>
        </n-card>
    </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, onMounted } from 'vue'
import eventBus from '@common/libs/eventBus'

const SettingsApp = defineAsyncComponent(() => import('./WsSettingsApp.vue'))
const SettingsDb = defineAsyncComponent(
    () => import('./WsSettingsDatabase.vue'),
)

onMounted(() => {
    window.electron.ipcRenderer.invoke('add-log', 'verbose', '设置页面已加载')
    // set window title
    eventBus.emit('update-window-title', '设置')
})
</script>

<style lang="scss" scoped>
.panel {
    display: flex;
    justify-content: center;
    margin: 20px 0;
    margin: 0 auto;
    height: calc(100vh - 90px);
}

.n-card-header__main {
    font-size: 1rem;
    font-weight: 800;
}
</style>
