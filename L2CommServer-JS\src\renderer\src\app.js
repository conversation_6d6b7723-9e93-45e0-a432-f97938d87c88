// Main application logic for the renderer process

class L2CommServerApp {
    constructor() {
        this.serverStatus = 'stopped';
        this.stats = {
            connections: 0,
            telegramsReceived: 0,
            telegramsSent: 0,
            uptime: 0
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupNavigation();
        this.loadInitialData();
        this.startStatsUpdate();
    }
    
    setupEventListeners() {
        // Server control buttons
        document.getElementById('start-server').addEventListener('click', () => {
            this.startServer();
        });
        
        document.getElementById('stop-server').addEventListener('click', () => {
            this.stopServer();
        });
        
        document.getElementById('reload-plugins').addEventListener('click', () => {
            this.reloadPlugins();
        });
        
        // IPC event listeners
        if (window.api) {
            window.api.onServerEvent((_, data) => {
                this.handleServerEvent(data);
            });

            window.api.onTelegramReceived((_, data) => {
                this.handleTelegramReceived(data);
            });

            window.api.onConnectionChanged((_, data) => {
                this.handleConnectionChanged(data);
            });

            window.api.onLogMessage((_, logData) => {
                this.addLogMessage({
                    level: logData.level,
                    message: Array.isArray(logData.data) ? logData.data.join(' ') : logData.data
                });
            });
        }
    }
    
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        const views = document.querySelectorAll('.view');
        
        navItems.forEach(item => {
            item.addEventListener('click', () => {
                // Remove active class from all nav items
                navItems.forEach(nav => nav.classList.remove('active'));
                // Add active class to clicked item
                item.classList.add('active');
                
                // Hide all views
                views.forEach(view => view.classList.add('hidden'));
                
                // Show selected view
                const viewName = item.getAttribute('data-view');
                const targetView = document.getElementById(`${viewName}-view`);
                if (targetView) {
                    targetView.classList.remove('hidden');
                }
                
                // Load view-specific data
                this.loadViewData(viewName);
            });
        });
    }
    
    async loadInitialData() {
        try {
            if (window.api) {
                // Load server status
                const status = await window.api.getServerStatus();
                this.updateServerStatus(status);
                
                // Load statistics
                const stats = await window.api.getStatistics();
                this.updateStats(stats);
            }
        } catch (error) {
            console.error('Failed to load initial data:', error);
            this.addLogMessage({ level: 'error', message: `Failed to load initial data: ${error.message}` });
        }
    }
    
    async loadViewData(viewName) {
        try {
            switch (viewName) {
                case 'history':
                    await this.loadTelegramHistory();
                    break;
                case 'plugins':
                    await this.loadPlugins();
                    break;
                case 'config':
                    await this.loadConfiguration();
                    break;
            }
        } catch (error) {
            console.error(`Failed to load ${viewName} data:`, error);
        }
    }
    
    async startServer() {
        try {
            if (window.api) {
                await window.api.startServer();
                this.addLogMessage({ level: 'info', message: 'Server start requested' });
            }
        } catch (error) {
            console.error('Failed to start server:', error);
            this.addLogMessage({ level: 'error', message: `Failed to start server: ${error.message}` });
        }
    }
    
    async stopServer() {
        try {
            if (window.api) {
                await window.api.stopServer();
                this.addLogMessage({ level: 'info', message: 'Server stop requested' });
            }
        } catch (error) {
            console.error('Failed to stop server:', error);
            this.addLogMessage({ level: 'error', message: `Failed to stop server: ${error.message}` });
        }
    }
    
    async reloadPlugins() {
        try {
            if (window.api) {
                await window.api.reloadPlugins();
                this.addLogMessage({ level: 'info', message: 'Plugins reloaded' });
            }
        } catch (error) {
            console.error('Failed to reload plugins:', error);
            this.addLogMessage({ level: 'error', message: `Failed to reload plugins: ${error.message}` });
        }
    }
    
    updateServerStatus(status) {
        this.serverStatus = status;
        const indicator = document.getElementById('status-indicator');
        
        if (status === 'running') {
            indicator.className = 'status-indicator status-running';
        } else {
            indicator.className = 'status-indicator status-stopped';
        }
    }
    
    updateStats(stats) {
        if (stats) {
            this.stats = { ...this.stats, ...stats };
        }
        
        document.getElementById('connections-count').textContent = this.stats.connections;
        document.getElementById('telegrams-received').textContent = this.stats.telegramsReceived;
        document.getElementById('telegrams-sent').textContent = this.stats.telegramsSent;
        document.getElementById('uptime').textContent = this.formatUptime(this.stats.uptime);
    }
    
    formatUptime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    addLogMessage(logData) {
        const logContainer = document.getElementById('log-container');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        
        const levelColor = {
            'error': '#e74c3c',
            'warn': '#f39c12',
            'info': '#3498db',
            'debug': '#95a5a6'
        };
        
        logEntry.innerHTML = `<span style="color: #7f8c8d">[${timestamp}]</span> <span style="color: ${levelColor[logData.level] || '#ecf0f1'}">[${logData.level?.toUpperCase() || 'INFO'}]</span> ${logData.message}`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
        
        // Keep only last 100 log entries
        while (logContainer.children.length > 100) {
            logContainer.removeChild(logContainer.firstChild);
        }
    }
    
    async loadTelegramHistory() {
        try {
            if (window.api) {
                const history = await window.api.getTelegramHistory(50);
                const container = document.getElementById('history-container');
                
                if (history && history.length > 0) {
                    container.innerHTML = history.map(item => 
                        `<div class="telegram-entry">
                            <strong>Telegram ${item.telNo}</strong> - ${item.timestamp} - ${item.direction}
                            <br><small>${item.peerIp}</small>
                        </div>`
                    ).join('');
                } else {
                    container.innerHTML = '<p>No telegram history available.</p>';
                }
            }
        } catch (error) {
            console.error('Failed to load telegram history:', error);
        }
    }
    
    async loadPlugins() {
        try {
            if (window.api) {
                const pluginData = await window.api.getPluginList();
                const container = document.getElementById('plugins-container');

                if (pluginData && pluginData.total > 0) {
                    let html = '<div class="plugin-stats">';
                    html += `<h3>Plugin Statistics</h3>`;
                    html += `<p>Total Plugins: ${pluginData.total}</p>`;
                    html += `<p>Receive Plugins: ${pluginData.receive.length}</p>`;
                    html += `<p>Send Plugins: ${pluginData.send.length}</p>`;
                    html += '</div>';

                    if (pluginData.receive.length > 0) {
                        html += '<div class="plugin-section">';
                        html += '<h4>Receive Plugins</h4>';
                        html += pluginData.receive.map(plugin =>
                            `<div class="plugin-entry">
                                <strong>${plugin.pluginName}</strong> - Telegram ${plugin.telegramNo}
                                <br><small>Version: ${plugin.version}</small>
                                <br><small>Author: ${plugin.author}</small>
                                <br><small>Description: ${plugin.description}</small>
                            </div>`
                        ).join('');
                        html += '</div>';
                    }

                    if (pluginData.send.length > 0) {
                        html += '<div class="plugin-section">';
                        html += '<h4>Send Plugins</h4>';
                        html += pluginData.send.map(plugin =>
                            `<div class="plugin-entry">
                                <strong>${plugin.pluginName}</strong> - Telegram ${plugin.telegramNo}
                                <br><small>Version: ${plugin.version}</small>
                                <br><small>Author: ${plugin.author}</small>
                                <br><small>Description: ${plugin.description}</small>
                                <div class="plugin-actions">
                                    <button class="button" onclick="app.sendTestTelegram(${plugin.telegramNo})">Send Test</button>
                                </div>
                            </div>`
                        ).join('');
                        html += '</div>';
                    }

                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<p>No plugins loaded. Check the TelegramProcessor-JS/plugins directory.</p>';
                }
            }
        } catch (error) {
            console.error('Failed to load plugins:', error);
            document.getElementById('plugins-container').innerHTML = '<p>Error loading plugins.</p>';
        }
    }

    async sendTestTelegram(telegramNo) {
        try {
            if (window.api) {
                const result = await window.api.sendTelegramWithPlugin(telegramNo);
                if (result.success) {
                    this.addLogMessage({ level: 'info', message: `Test telegram ${telegramNo} sent successfully` });
                } else {
                    this.addLogMessage({ level: 'error', message: `Failed to send test telegram ${telegramNo}: ${result.error || 'Unknown error'}` });
                }
            }
        } catch (error) {
            console.error('Failed to send test telegram:', error);
            this.addLogMessage({ level: 'error', message: `Error sending test telegram ${telegramNo}: ${error.message}` });
        }
    }
    
    async loadConfiguration() {
        try {
            if (window.api) {
                const config = await window.api.loadConfig();
                const container = document.getElementById('config-container');
                
                if (config) {
                    container.innerHTML = `
                        <div class="config-section">
                            <h3>Server Configuration</h3>
                            <p><strong>IP:</strong> ${config.server?.ip || 'Not configured'}</p>
                            <p><strong>Port:</strong> ${config.server?.port || 'Not configured'}</p>
                        </div>
                        <div class="config-section">
                            <h3>Database Configuration</h3>
                            <p><strong>Host:</strong> ${config.database?.host || 'Not configured'}</p>
                            <p><strong>Database:</strong> ${config.database?.name || 'Not configured'}</p>
                        </div>
                    `;
                } else {
                    container.innerHTML = '<p>Configuration not available.</p>';
                }
            }
        } catch (error) {
            console.error('Failed to load configuration:', error);
        }
    }
    
    handleServerEvent(data) {
        this.updateServerStatus(data.status);
        this.addLogMessage({ level: 'info', message: `Server ${data.status}` });
    }
    
    handleTelegramReceived(data) {
        this.stats.telegramsReceived++;
        this.updateStats();
        this.addLogMessage({ 
            level: 'info', 
            message: `Telegram ${data.telNo} received from ${data.peerIp}` 
        });
    }
    
    handleConnectionChanged(data) {
        if (data.connected) {
            this.stats.connections++;
            this.addLogMessage({ 
                level: 'info', 
                message: `Client connected: ${data.peerIp}` 
            });
        } else {
            this.stats.connections--;
            this.addLogMessage({ 
                level: 'info', 
                message: `Client disconnected: ${data.sessionId}` 
            });
        }
        this.updateStats();
    }
    
    startStatsUpdate() {
        setInterval(() => {
            if (this.serverStatus === 'running') {
                this.stats.uptime++;
                this.updateStats();
            }
        }, 1000);
    }
}

// Initialize the application when DOM is loaded
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new L2CommServerApp();
    window.app = app; // Make app globally accessible for button handlers
});
