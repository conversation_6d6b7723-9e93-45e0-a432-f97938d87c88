<template>
    <div class="billet" :id="`billet${idx}`">
        <span class="billet-id">{{ billet?.Billet_ID }}</span>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { BilletData } from '@common/interfaces/tcData'

// define props
const props = defineProps({
    // billet
    billet: {
        type: Object as () => BilletData,
        required: true,
    },
    // index
    idx: {
        type: Number,
        required: true,
    },
    // x ratio, pixel/mm
    xRatio: {
        type: Number,
        required: true,
    },
    // y ratio, pixel/mm
    yRatio: {
        type: Number,
        required: true,
    },
    // billet position interval
    billetInterval: {
        type: Number,
        required: true,
    },
})

const billetAttr = computed(() => {
    // calculate billet attributes
    // 全炉长32000
    const left = props.idx * props.billetInterval + 10
    const top = 10
    const width = Math.max(props.billet.Billet_Width * props.xRatio, 8)
    let height = Math.max(props.billet.Billet_Length * 0.05, 80)

    return {
        left: left.toString() + 'px',
        top: top.toString() + 'px',
        width: width.toString() + 'px',
        height: height.toString() + 'px',
    }
})
</script>

<style scoped>
.billet {
    border: solid 1px black;
    display: block;
    position: absolute;
    writing-mode: tb-rl;
    left: v-bind('billetAttr.left');
    top: v-bind('billetAttr.top');
    width: v-bind('billetAttr.width');
    height: v-bind('billetAttr.height');
}

.billet-id {
    position: absolute;
    left: -3px;
    top: 2px;
    font-size: 9px;
}
</style>
