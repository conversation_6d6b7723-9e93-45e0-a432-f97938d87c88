# TelegramProcessor-JS

This directory contains telegram processing plugins for L2CommServer-JS. Plugins are independently developed and can be hot-reloaded without restarting the server. Each plugin is identified by its telegram number and can handle both receive and send operations.

## Plugin Development

### Directory Structure

```
TelegramProcessor-JS/
├── plugin-base.js          # Base classes and utilities
├── plugins/                # All plugins in single directory
│   ├── TeleReceive101.js   # Receive plugin for telegram 101
│   ├── TeleReceive103.js   # Receive plugin for telegram 103
│   ├── TeleSend21.js       # Send plugin for telegram 21
│   ├── TeleBoth50.js       # Plugin handling both send/receive for telegram 50
│   └── ...
├── dist/                   # Built/compiled plugins (auto-generated)
├── build-plugins.js        # Build system for standalone plugins
├── package.json
└── README.md
```

### Creating a Plugin

Plugins are identified by their telegram number and can handle receive operations, send operations, or both.

1. Create a new JavaScript file in the `plugins/` directory
2. Import the base classes from `plugin-base.js`
3. Extend `ReceivePluginBase`, `SendPluginBase`, or both
4. Implement required methods
5. Export your plugin using `exportPlugin()`

### Plugin Types

-   **Receive Plugin**: Handles incoming telegrams (implements `parse()` and `process()`)
-   **Send Plugin**: Composes outgoing telegrams (implements `compose()` or `composeBody()`)
-   **Both Plugin**: Handles both operations (implements all methods)

Example:

```javascript
const {
    ReceivePluginBase,
    exportPlugin,
    DataTypes,
} = require('../../plugin-base');

class TeleReceive101 extends ReceivePluginBase {
    constructor() {
        super();
        this.telegramNo = 101;
        this.pluginName = 'TeleReceive101';
        this.version = '1.0.0';
        this.description = 'Process telegram 101 - Distance measurement';
        this.author = 'Your Name';
        this.messageLength = 4; // Expected message length in bytes

        // Plugin-specific properties
        this.distance = 0.0;
    }

    parse(body) {
        try {
            if (body.length >= this.messageLength) {
                // Parse distance value (4 bytes, float)
                this.distance = DataTypes.bufferToReal(body.slice(0, 4));
                return true;
            }
            return false;
        } catch (error) {
            console.error(`[${this.pluginName}] Parse error:`, error);
            return false;
        }
    }

    async process(body) {
        console.log(
            `[${this.pluginName}] Processing distance: ${this.distance}`
        );

        // Your processing logic here
        // You can:
        // - Save data to database
        // - Send response telegrams
        // - Trigger other actions

        // Example: Send response if distance is too high
        if (this.distance > 100.0) {
            // Compose response telegram (example)
            const responseBody = Buffer.alloc(4);
            DataTypes.realToBuffer(this.distance).copy(responseBody, 0);

            // Send response using plugin system
            // this.sendRequest(responseData);
        }
    }

    debugOutput() {
        console.log(`[${this.pluginName}] Distance: ${this.distance}`);
    }
}

module.exports = exportPlugin(TeleReceive101);
```

### Creating a Send Plugin

1. Create a new JavaScript file in the `plugins/send/` directory
2. Import the base classes from `plugin-base.js`
3. Extend `SendPluginBase`
4. Implement required methods
5. Export your plugin using `exportPlugin()`

Example:

```javascript
const {
    SendPluginBase,
    exportPlugin,
    DataTypes,
} = require('../../plugin-base');

class TeleSend21 extends SendPluginBase {
    constructor() {
        super();
        this.telegramNo = 21;
        this.pluginName = 'TeleSend21';
        this.version = '1.0.0';
        this.description = 'Send telegram 21 - Status update';
        this.author = 'Your Name';

        // Plugin-specific properties
        this.status = 1;
        this.temperature = 25.0;
    }

    composeBody() {
        // Create message body (8 bytes: 4 for status, 4 for temperature)
        const body = Buffer.alloc(8);

        DataTypes.dIntToBuffer(this.status).copy(body, 0);
        DataTypes.realToBuffer(this.temperature).copy(body, 4);

        return body;
    }

    compose() {
        const body = this.composeBody();
        return this.composeWithHeader(body);
    }

    debugOutput() {
        console.log(
            `[${this.pluginName}] Status: ${this.status}, Temperature: ${this.temperature}`
        );
    }

    // Helper methods to set data
    setStatus(status) {
        this.status = status;
    }

    setTemperature(temperature) {
        this.temperature = temperature;
    }
}

module.exports = exportPlugin(TeleSend21);
```

### Data Types

The plugin system provides S7-compatible data type conversion utilities using proper Siemens S7 big-endian format:

#### S7DataTypes (Recommended)

The `S7DataTypes` class provides authentic Siemens S7 data type conversions based on the nodes7 library foundation:

-   `bufferToReal(buffer)` / `realToBuffer(value)` - 32-bit float (big-endian)
-   `bufferToInt(buffer)` / `intToBuffer(value)` - 16-bit integer (big-endian)
-   `bufferToDInt(buffer)` / `dIntToBuffer(value)` - 32-bit integer (big-endian)
-   `bufferToWord(buffer)` / `wordToBuffer(value)` - 16-bit unsigned integer
-   `bufferToDWord(buffer)` / `dWordToBuffer(value)` - 32-bit unsigned integer
-   `bufferToBool(buffer, bit)` / `boolToBuffer(value, bit)` - Boolean

#### String Types

-   `bufferToS7String(buffer)` / `s7StringToBuffer(value, maxLength)` - S7 string format
-   `bufferToFixedString(buffer, encoding)` / `fixedStringToBuffer(value, length, encoding)` - Fixed-length strings

#### Time and Date Types

-   `bufferToTime(buffer)` / `timeToBuffer(milliseconds)` - S7 Time format
-   `bufferToDate(buffer)` / `dateToBuffer(date)` - S7 Date format

#### Utility Functions

-   `bufferToByteArray(buffer)` / `byteArrayToBuffer(array)` - Byte array conversion
-   `bufferToHexString(buffer, separator)` - Hex string representation

#### Legacy DataTypes (Deprecated)

The `DataTypes` class is maintained for backward compatibility but inherits from `S7DataTypes`.

#### S7 String Format

S7 strings use a special format: `[max_length][actual_length][string_data...]`

```javascript
// Reading S7 string (recommended)
const text = S7DataTypes.bufferToS7String(buffer);

// Writing S7 string (recommended)
const buffer = S7DataTypes.s7StringToBuffer('Hello World', 50);

// Legacy usage (deprecated)
const legacyBuffer = DataTypes.s7StringToBuffer('Hello World', 50);
```

### Plugin Properties

#### Required Properties

-   `telegramNo` - Unique telegram number
-   `pluginName` - Human-readable plugin name

#### Optional Properties

-   `version` - Plugin version
-   `description` - Plugin description
-   `author` - Plugin author
-   `messageLength` - Expected message length (0 = variable)
-   `isSaveToDb` - Whether to save to database (receive plugins)

### Hot Reloading

Plugins are automatically reloaded when:

-   A plugin file is modified
-   A new plugin file is added
-   A plugin file is deleted

The server will log plugin loading/reloading events and any errors.

### Best Practices

1. **Error Handling**: Always wrap parsing and processing logic in try-catch blocks
2. **Validation**: Validate input data before processing
3. **Logging**: Use descriptive log messages with plugin name
4. **Performance**: Keep processing logic efficient for real-time operation
5. **Testing**: Test plugins thoroughly before deployment

## Build System

Plugins can be compiled into standalone JavaScript files for easy deployment using the built-in build system.

### Building Plugins

```bash
# Install dependencies
npm install

# Build all plugins
npm run build

# Build a specific plugin
npm run build:single TeleReceive101.js

# Watch for changes and auto-rebuild
npm run watch

# Clean build directory
npm run clean
```

### Build Output

Built plugins are saved to the `dist/` directory as standalone files that include:

-   Plugin base classes and utilities
-   Plugin implementation
-   No external dependencies

### Deployment Options

#### Option 1: Source Deployment

1. Copy your plugin file directly to the `plugins/` directory
2. The plugin will be automatically loaded by the hot-reload system

#### Option 2: Built Deployment

1. Build your plugin: `npm run build:single YourPlugin.js`
2. Copy the built file from `dist/YourPlugin.js` to the target `plugins/` directory
3. The standalone file contains everything needed to run

### Development Workflow

1. Create your plugin in the `plugins/` directory
2. Test locally with the development server
3. Build for production deployment: `npm run build:single YourPlugin.js`
4. Deploy the built file to target systems

### Debugging

-   Enable debug logging in the server to see detailed plugin execution
-   Use `debugOutput()` method to log plugin-specific information
-   Check server logs for plugin loading and execution errors
-   Use the server UI to monitor plugin status and statistics
