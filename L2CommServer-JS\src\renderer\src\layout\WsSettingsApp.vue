<template>
    <n-form
        ref="formRef"
        :rules="rules"
        :model="appSettings"
        :show-require-mark="false"
        label-placement="left"
        label-width="auto"
        size="medium"
    >
        <n-grid :cols="24">
            <n-form-item-gi
                label="数据记录列表宽度"
                path="datalogExplorebarSize"
                :span="9"
            >
                <n-input-number
                    v-model:value="appSettings.datalogExplorebarSize"
                    step="5"
                    placeholder="250"
                    data-testid="datalog-explorebar-size"
                />
            </n-form-item-gi>
            <n-gi :offset="1" :span="14">
                <n-text tag="div">
                    200-300px. 也可以在数据记录窗口中拖拽分格线来调整
                </n-text>
            </n-gi>
        </n-grid>
        <div style="display: flex; justify-content: flex-end">
            <n-button secondary type="primary" @click="handleSave">
                保存
            </n-button>
        </div>
    </n-form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useSettingsStore } from '@/stores/settings'
import { FormInst, FormItemRule, useMessage } from 'naive-ui'

const formRef = ref<FormInst | null>(null)
const message = useMessage()

const settingsStore = useSettingsStore()
const { appSettings } = storeToRefs(settingsStore)

// form rules
const rules = {
    datalogExplorebarSize: {
        type: 'number',
        required: true,
        trigger: ['blur', 'change'],
        message: '请输入正确的基础数据列表宽度',
        validator(_rule: FormItemRule, value: number) {
            return value >= 200 && value <= 300
        },
    },
}
const handleSave = (e: MouseEvent) => {
    window.electron.ipcRenderer.invoke(
        'add-log',
        'verbose',
        'Save general settings',
    )
    e.preventDefault()
    formRef.value?.validate((errors) => {
        if (!errors) {
            // 保存设置
            settingsStore.setAppSettings(appSettings.value)
            message.success('保存成功')
        }
    })
}
</script>

<style lang="scss" scoped>
.n-text {
    margin: 6px;
    font-style: italic;
    font-size: 0.8rem;
}
</style>
