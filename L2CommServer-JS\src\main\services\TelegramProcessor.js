import log from 'electron-log';
import {
    TelegramHeader,
    TelegramHeader20,
    TelegramHeader8,
} from '../../common/interfaces.js';
import { PluginManager } from './PluginManager.js';
import { TelegramHistoryService } from './TelegramHistoryService.js';
import S7DataTypes from '../../common/S7DataTypes.js';

/**
 * Telegram Processor - handles telegram parsing and processing with plugin support
 * Supports multiple header formats: TelegramHeader20 (telNo < 1000) and TelegramHeader8 (telNo >= 1000)
 */
class TelegramProcessor {
    constructor(pluginManager = null) {
        this.pluginManager = pluginManager;
        // Header length is now dynamic based on telegram number
    }

    /**
     * Set plugin manager
     * @param {PluginManager} pluginManager - Plugin manager instance
     */
    setPluginManager(pluginManager) {
        this.pluginManager = pluginManager;
    }

    /**
     * Process received telegram data
     * @param {Buffer} buffer - Raw telegram data
     * @param {number} offset - Offset in buffer
     * @param {number} size - Size of data
     * @param {string} peerIp - IP address of sender
     * @returns {number} - Number of processed telegrams
     */
    async processReceived(buffer, offset, size, peerIp) {
        try {
            log.info(
                `Telegram(s) received. Buffer size ${size}, offset: ${offset} from ${peerIp}`
            );

            // Extract the relevant portion of the buffer
            const telegramData = buffer.subarray(offset, offset + size);
            let processedCount = 0;
            let currentOffset = 0;

            // Process potentially multiple telegrams in the buffer
            while (currentOffset < telegramData.length) {
                // First, read telegram number to determine header type
                if (currentOffset + 2 > telegramData.length) {
                    log.warn('Incomplete telegram number in buffer');
                    break;
                }

                // Read telegram number (first 2 bytes, S7 big-endian format)
                const telNo = S7DataTypes.bufferToWord(
                    telegramData.subarray(currentOffset, currentOffset + 2)
                );

                // Determine header length based on telegram number
                const headerLength = TelegramHeader.getHeaderLength(telNo);

                // Check if we have enough data for the complete header
                if (currentOffset + headerLength > telegramData.length) {
                    log.warn(
                        `Incomplete telegram header received. Need ${headerLength} bytes, have ${
                            telegramData.length - currentOffset
                        }`
                    );
                    break;
                }

                // Parse telegram header with appropriate length
                const header = this.parseHeader(
                    telegramData.subarray(
                        currentOffset,
                        currentOffset + headerLength
                    ),
                    telNo
                );
                if (!header) {
                    log.error('Failed to parse telegram header');
                    break;
                }

                // Check if we have the complete telegram
                if (currentOffset + header.telLen > telegramData.length) {
                    log.warn(
                        `Incomplete telegram received. Expected ${
                            header.telLen
                        } bytes, got ${telegramData.length - currentOffset}`
                    );
                    break;
                }

                // Extract complete telegram
                const completeTelegram = telegramData.subarray(
                    currentOffset,
                    currentOffset + header.telLen
                );

                // Log telegram info
                log.info(
                    `Telegram ${header.telNo} (${header.headerType}) received from ${peerIp}`
                );

                // Set additional header properties
                header.peerIp = peerIp;
                header.direction = 'R'; // Received

                // TODO: Save to database (will be implemented in database integration task)
                // await this.saveTelegramToDatabase(header);

                // Extract message body (telegram without header)
                const messageBody = completeTelegram.subarray(headerLength);

                // Process message through plugin system
                await this.processMessageThroughPlugins(
                    header,
                    messageBody,
                    peerIp
                );

                // Debug output
                this.debugTelegram(completeTelegram, header.telLen);

                processedCount++;
                currentOffset += header.telLen;
            }

            return processedCount;
        } catch (error) {
            log.error('Error processing received telegram:', error);
            return 0;
        }
    }

    /**
     * Parse telegram header from buffer
     * @param {Buffer} headerBuffer - Header buffer (8 or 20 bytes)
     * @param {number} telNo - Telegram number (already read)
     * @returns {TelegramHeader20|TelegramHeader8|null} - Parsed header or null if parsing failed
     */
    parseHeader(headerBuffer, telNo) {
        try {
            const headerType = TelegramHeader.getHeaderType(telNo);
            const expectedLength = TelegramHeader.getHeaderLength(telNo);

            if (headerBuffer.length < expectedLength) {
                log.error(
                    `Invalid header length: ${headerBuffer.length}, expected ${expectedLength} for ${headerType}`
                );
                return null;
            }

            if (headerType === 'TelegramHeader20') {
                return this.parseHeader20(headerBuffer);
            } else {
                return this.parseHeader8(headerBuffer);
            }
        } catch (error) {
            log.error('Error parsing telegram header:', error);
            return null;
        }
    }

    /**
     * Parse 20-byte telegram header (telNo < 1000)
     * Format: telNo(2) + telLen(2) + sendId(2) + recId(2) + createTime(8) + telCounter(2) + spare(2)
     * @param {Buffer} headerBuffer - 20-byte header buffer
     * @returns {TelegramHeader20|null} - Parsed header or null if parsing failed
     */
    parseHeader20(headerBuffer) {
        try {
            const header = new TelegramHeader20();
            let offset = 0;

            // Parse telegram number (2 bytes) - S7 big-endian format
            header.telNo = S7DataTypes.bufferToWord(
                headerBuffer.subarray(offset, offset + 2)
            );
            offset += 2;

            // Parse telegram length (2 bytes) - S7 big-endian format
            header.telLen = S7DataTypes.bufferToWord(
                headerBuffer.subarray(offset, offset + 2)
            );
            offset += 2;

            // Parse sender ID (2 bytes) - Fixed string
            header.sendId = S7DataTypes.bufferToFixedString(
                headerBuffer.subarray(offset, offset + 2)
            );
            offset += 2;

            // Parse receiver ID (2 bytes) - Fixed string
            header.recId = S7DataTypes.bufferToFixedString(
                headerBuffer.subarray(offset, offset + 2)
            );
            offset += 2;

            // Parse timestamp (8 bytes) - S7 DateTime format
            const timestampBuffer = headerBuffer.subarray(offset, offset + 8);
            try {
                header.createTime =
                    S7DataTypes.bufferToDateTime(timestampBuffer);
            } catch (tsError) {
                // Fallback to current time if timestamp parsing fails
                log.warn(
                    'Failed to parse S7 timestamp, using current time:',
                    tsError
                );
                header.createTime = new Date();
            }
            offset += 8;

            // Parse telegram counter (2 bytes) - S7 big-endian format
            header.telCounter = S7DataTypes.bufferToWord(
                headerBuffer.subarray(offset, offset + 2)
            );
            offset += 2;

            // Parse spare bytes (2 bytes)
            header.spare = S7DataTypes.bufferToWord(
                headerBuffer.subarray(offset, offset + 2)
            );

            // Debug output
            this.debugHeader(header);

            return header;
        } catch (error) {
            log.error('Error parsing 20-byte telegram header:', error);
            return null;
        }
    }

    /**
     * Parse 8-byte telegram header (telNo >= 1000)
     * Format: telNo(2) + telLen(2) + telCounter(2) + spare(2)
     * @param {Buffer} headerBuffer - 8-byte header buffer
     * @returns {TelegramHeader8|null} - Parsed header or null if parsing failed
     */
    parseHeader8(headerBuffer) {
        try {
            const header = new TelegramHeader8();
            let offset = 0;

            // Parse telegram number (2 bytes) - S7 big-endian format
            header.telNo = S7DataTypes.bufferToWord(
                headerBuffer.subarray(offset, offset + 2)
            );
            offset += 2;

            // Parse telegram length (2 bytes) - S7 big-endian format
            header.telLen = S7DataTypes.bufferToWord(
                headerBuffer.subarray(offset, offset + 2)
            );
            offset += 2;

            // Parse telegram counter (2 bytes) - S7 big-endian format
            header.telCounter = S7DataTypes.bufferToWord(
                headerBuffer.subarray(offset, offset + 2)
            );
            offset += 2;

            // Parse spare bytes (2 bytes)
            header.spare = S7DataTypes.bufferToWord(
                headerBuffer.subarray(offset, offset + 2)
            );

            // Debug output
            this.debugHeader(header);

            return header;
        } catch (error) {
            log.error('Error parsing 8-byte telegram header:', error);
            return null;
        }
    }

    /**
     * Compose telegram header to buffer using S7 format
     * @param {TelegramHeader20|TelegramHeader8} header - Telegram header
     * @returns {Buffer} - Header buffer (8 or 20 bytes)
     */
    composeHeader(header) {
        try {
            // Determine header type based on telegram number if not set
            if (!header.headerType) {
                header.headerType = TelegramHeader.getHeaderType(header.telNo);
            }

            if (header.headerType === 'TelegramHeader20') {
                return this.composeHeader20(header);
            } else {
                return this.composeHeader8(header);
            }
        } catch (error) {
            log.error('Error composing telegram header:', error);
            return null;
        }
    }

    /**
     * Compose 20-byte telegram header (telNo < 1000)
     * @param {TelegramHeader20} header - Telegram header
     * @returns {Buffer} - 20-byte header buffer
     */
    composeHeader20(header) {
        try {
            const headerBuffer = Buffer.alloc(20);
            let offset = 0;

            // Write telegram number (2 bytes, big-endian)
            S7DataTypes.wordToBuffer(header.telNo).copy(headerBuffer, offset);
            offset += 2;

            // Write telegram length (2 bytes, big-endian)
            S7DataTypes.wordToBuffer(header.telLen).copy(headerBuffer, offset);
            offset += 2;

            // Write sender ID (2 bytes, fixed string)
            S7DataTypes.fixedStringToBuffer(
                header.sendId || 'L2',
                2,
                'ascii'
            ).copy(headerBuffer, offset);
            offset += 2;

            // Write receiver ID (2 bytes, fixed string)
            S7DataTypes.fixedStringToBuffer(
                header.recId || 'PL',
                2,
                'ascii'
            ).copy(headerBuffer, offset);
            offset += 2;

            // Write timestamp (8 bytes) - S7 DateTime format
            const createTime = header.createTime || new Date();
            const timestampBuffer = S7DataTypes.dateTimeToBuffer(createTime);
            timestampBuffer.copy(headerBuffer, offset);
            offset += 8;

            // Write telegram counter (2 bytes, big-endian)
            S7DataTypes.wordToBuffer(header.telCounter || 0).copy(
                headerBuffer,
                offset
            );
            offset += 2;

            // Write spare bytes (2 bytes)
            S7DataTypes.wordToBuffer(header.spare || 0).copy(
                headerBuffer,
                offset
            );

            return headerBuffer;
        } catch (error) {
            log.error('Error composing 20-byte telegram header:', error);
            return null;
        }
    }

    /**
     * Compose 8-byte telegram header (telNo >= 1000)
     * @param {TelegramHeader8} header - Telegram header
     * @returns {Buffer} - 8-byte header buffer
     */
    composeHeader8(header) {
        try {
            const headerBuffer = Buffer.alloc(8);
            let offset = 0;

            // Write telegram number (2 bytes, big-endian)
            S7DataTypes.wordToBuffer(header.telNo).copy(headerBuffer, offset);
            offset += 2;

            // Write telegram length (2 bytes, big-endian)
            S7DataTypes.wordToBuffer(header.telLen).copy(headerBuffer, offset);
            offset += 2;

            // Write telegram counter (2 bytes, big-endian)
            S7DataTypes.wordToBuffer(header.telCounter || 0).copy(
                headerBuffer,
                offset
            );
            offset += 2;

            // Write spare bytes (2 bytes)
            S7DataTypes.wordToBuffer(header.spare || 0).copy(
                headerBuffer,
                offset
            );

            return headerBuffer;
        } catch (error) {
            log.error('Error composing 8-byte telegram header:', error);
            return null;
        }
    }

    /**
     * Debug output for telegram header
     * @param {TelegramHeader} header - Telegram header
     */
    debugHeader(header) {
        log.debug(
            `Telegram Header - No: ${header.telNo}, Len: ${header.telLen}, ` +
                `Send: ${header.sendId}, Rec: ${header.recId}, Counter: ${header.telCounter}, ` +
                `Time: ${
                    header.createTime ? header.createTime.toISOString() : 'N/A'
                }`
        );
    }

    /**
     * Debug output for raw telegram data
     * @param {Buffer} telegram - Raw telegram data
     * @param {number} length - Telegram length
     */
    debugTelegram(telegram, length) {
        if (log.transports.file.level === 'debug') {
            const hexString = telegram
                .slice(0, Math.min(length, 64))
                .toString('hex');
            log.debug(
                `Raw telegram (${length} bytes): ${hexString}${
                    length > 64 ? '...' : ''
                }`
            );
        }
    }

    /**
     * Compose a telegram with header and body
     * @param {number} telNo - Telegram number
     * @param {string} sendId - Sender ID
     * @param {string} recId - Receiver ID
     * @param {Buffer} messageBody - Message body
     * @param {number} telCounter - Telegram counter
     * @returns {Buffer} - Complete telegram
     */
    composeTelegram(telNo, sendId, recId, messageBody, telCounter = 0) {
        try {
            const totalLength = this.headerLength + messageBody.length;
            const telegram = Buffer.alloc(totalLength);

            // Write header
            telegram.writeUInt16LE(telNo, 0); // Telegram number
            telegram.writeUInt16LE(totalLength, 2); // Telegram length
            telegram.write(sendId.padEnd(2, '\0'), 4, 2, 'ascii'); // Sender ID
            telegram.write(recId.padEnd(2, '\0'), 6, 2, 'ascii'); // Receiver ID

            // Write timestamp (8 bytes) - simplified for now
            const now = Date.now();
            telegram.writeBigUInt64LE(BigInt(now), 8);

            telegram.writeUInt16LE(telCounter, 16); // Telegram counter

            // Reserved bytes (2 bytes)
            telegram.writeUInt16LE(0, 18);

            // Write message body
            messageBody.copy(telegram, this.headerLength);

            return telegram;
        } catch (error) {
            log.error('Error composing telegram:', error);
            throw error;
        }
    }

    /**
     * Process message through plugin system
     * @param {TelegramHeader} header - Telegram header
     * @param {Buffer} messageBody - Message body
     * @param {string} peerIp - Peer IP address
     */
    async processMessageThroughPlugins(header, messageBody, peerIp) {
        try {
            if (!this.pluginManager) {
                log.warn(
                    `No plugin manager available for telegram ${header.telNo}`
                );
                return;
            }

            // Get receive plugin for this telegram number
            const plugin = this.pluginManager.getReceivePlugin(header.telNo);

            if (!plugin) {
                log.warn(`No plugin found for telegram ${header.telNo}`);
                return;
            }

            log.debug(
                `Processing telegram ${header.telNo} with plugin: ${plugin.pluginName}`
            );

            // Set up send request callback for the plugin
            plugin.setSendRequestCallback((responseData) => {
                this.handlePluginSendRequest(responseData, peerIp);
            });

            // Parse the message
            const parseSuccess = plugin.parse(messageBody);

            if (parseSuccess) {
                log.debug(`Telegram ${header.telNo} parsed successfully`);

                // Debug output
                plugin.debugOutput();

                // Save to database if enabled
                if (plugin.isSaveToDb) {
                    try {
                        await plugin.saveToDb();
                        log.debug(`Telegram ${header.telNo} saved to database`);
                    } catch (error) {
                        log.error(
                            `Failed to save telegram ${header.telNo} to database:`,
                            error
                        );
                    }
                }

                // Process the message
                await plugin.process(messageBody);

                log.debug(`Telegram ${header.telNo} processing completed`);
            } else {
                log.error(`Failed to parse telegram ${header.telNo}`);
            }
        } catch (error) {
            log.error(
                `Error processing telegram ${header.telNo} through plugins:`,
                error
            );
        }
    }

    /**
     * Handle send request from plugin
     * @param {Buffer} responseData - Response telegram data
     * @param {string} peerIp - Target peer IP
     */
    handlePluginSendRequest(responseData, peerIp) {
        try {
            // This will be handled by the calling TCP session/client
            // For now, just emit an event that can be caught by the server
            if (this.sendRequestCallback) {
                this.sendRequestCallback(responseData);
            } else {
                log.warn('No send request callback available');
            }
        } catch (error) {
            log.error('Error handling plugin send request:', error);
        }
    }

    /**
     * Set send request callback
     * @param {Function} callback - Callback function
     */
    setSendRequestCallback(callback) {
        this.sendRequestCallback = callback;
    }

    /**
     * Compose telegram using send plugin
     * @param {number} telegramNo - Telegram number
     * @returns {Buffer|null} - Composed telegram or null if no plugin found
     */
    async composeWithPlugin(telegramNo) {
        try {
            if (!this.pluginManager) {
                log.warn('No plugin manager available for composing telegram');
                return null;
            }

            const plugin = this.pluginManager.getSendPlugin(telegramNo);

            if (!plugin) {
                log.warn(`No send plugin found for telegram ${telegramNo}`);
                return null;
            }

            log.debug(
                `Composing telegram ${telegramNo} with plugin: ${plugin.pluginName}`
            );

            // Compose the telegram
            const telegram = plugin.compose();

            // Increment counter for next use
            plugin.incrementCounter();

            log.debug(`Telegram ${telegramNo} composed successfully`);

            return telegram;
        } catch (error) {
            log.error(`Error composing telegram ${telegramNo}:`, error);
            return null;
        }
    }

    /**
     * Create a send request event handler
     * @param {Function} sendCallback - Callback function to send telegram
     * @returns {Function} - Event handler function
     */
    createSendRequestHandler(sendCallback) {
        return (telegramData) => {
            try {
                if (sendCallback && typeof sendCallback === 'function') {
                    sendCallback(telegramData);
                } else {
                    log.warn(
                        'No send callback provided for telegram send request'
                    );
                }
            } catch (error) {
                log.error('Error in send request handler:', error);
            }
        };
    }
}

export { TelegramProcessor };
