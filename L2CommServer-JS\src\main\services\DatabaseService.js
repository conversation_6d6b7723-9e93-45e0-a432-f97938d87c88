import { knex } from 'knex';
import log from 'electron-log';

/**
 * Database Service using Knex with MSSQL
 * Based on FS-PSTF-L2 implementation
 */
class DatabaseService {
    constructor() {
        this.knex = null;
        this.heartbeatInterval = null;
        this.isConnected = false;
    }

    /**
     * Initialize database connection
     * @param {Object} dbSettings - Database configuration
     */
    async initialize(dbSettings) {
        this.knex = knex({
            client: 'mssql',
            connection: {
                server:
                    dbSettings.serverAddress || dbSettings.host || 'localhost',
                port: dbSettings.serverPort || dbSettings.port || 1433,
                database: dbSettings.database || 'L2',
                user: dbSettings.username || dbSettings.user || 'sa',
                password: dbSettings.password || '',
                connectionTimeout: 5000,
                options: {
                    encrypt: false,
                    trustServerCertificate: true,
                    ...dbSettings.options,
                },
            },
        });

        // Check db connection
        if (this.knex?.client?.pool) {
            log.verbose('Knex initialized.');

            // Log all queries (optional)
            // this.knex.on('query', log.debug);

            // Start heartbeat check
            this.startHeartbeat();
        } else {
            log.error(
                'Knex connection failed. Please check database connection settings.'
            );
        }
    }

    /**
     * Get Knex instance
     * @returns {Object} - Knex instance
     */
    getKnex() {
        if (!this.knex) {
            throw new Error('Database not initialized');
        }
        return this.knex;
    }

    /**
     * Close database connection
     */
    async close() {
        if (this.knex) {
            await this.knex.destroy();
            this.knex = null;
        }

        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * Get connection status
     * @returns {boolean} - Connection status
     */
    getConnectionStatus() {
        return this.isConnected;
    }

    /**
     * Start heartbeat check
     */
    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }

        this.heartbeatInterval = setInterval(async () => {
            if (this.knex) {
                try {
                    await this.knex.raw('SELECT 1');
                    this.isConnected = true;
                } catch (error) {
                    this.isConnected = false;
                    log.error('Database connection error:', error);
                }
            }
        }, 5000); // Check every 5 seconds
    }
}

// Create and export singleton instance
const databaseService = new DatabaseService();

export { DatabaseService, databaseService };
