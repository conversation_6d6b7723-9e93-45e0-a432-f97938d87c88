import { BrowserWindow, ipcMain } from 'electron'
import { PlcService } from '../services/PlcService'

let plcService: PlcService

export default (mainWindow: BrowserWindow) => {
    plcService = new PlcService(mainWindow)

    console.log('connect to PLC')

    // 连接到 PLC
    plcService.connect()

    ipcMain.handle('get-plc-connection-status', () => {
        return plcService.getConnectionStatus()
    })

    // 监听渲染进程发出的事件
    // 访问传动PLC, 获取物料跟踪数据
    ipcMain.handle('load-tc-tracking', async (_event) => {
        await plcService.loadTCData()
    })
}
