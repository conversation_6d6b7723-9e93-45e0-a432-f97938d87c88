<template>
    <n-drawer
        v-model:show="isVisible"
        :height="300"
        placement="bottom"
        @after-leave="closeViewer"
    >
        <n-drawer-content>
            <template #header>
                <n-button @click="isVisible = false"> 关闭 </n-button>
            </template>
            <n-descriptions
                label-placement="left"
                bordered
                :label-style="{ width: '125px' }"
                :column="4"
            >
                <n-descriptions-item label="1区设定温度">
                    {{ row?.z1_temp_sp.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="2区设定温度">
                    {{ row?.z2_temp_sp.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="3区设定温度">
                    {{ row?.z3_temp_sp.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="4区设定温度">
                    {{ row?.z4_temp_sp.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="5区设定温度">
                    {{ row?.z5_temp_sp.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="6区设定温度">
                    {{ row?.z6_temp_sp.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="1区实际温度">
                    {{ row?.z1_temp_av.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="2区实际温度">
                    {{ row?.z2_temp_av.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="3区实际温度">
                    {{ row?.z3_temp_av.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="4区实际温度">
                    {{ row?.z4_temp_av.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="5区实际温度">
                    {{ row?.z5_temp_av.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="6区实际温度">
                    {{ row?.z6_temp_av.toFixed(2) }}
                </n-descriptions-item>
                <n-descriptions-item label="天然气累计">
                    {{ row?.ng_consumption.toFixed(0) }}
                </n-descriptions-item>
                <n-descriptions-item label="记录时间" :span="2">
                    {{
                        row?.record_time.toLocaleString('zh-CN', {
                            timeZone: 'UTC',
                        })
                    }}
                </n-descriptions-item>
            </n-descriptions>
        </n-drawer-content>
    </n-drawer>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue'
import {
    NDrawer,
    NDrawerContent,
    NDescriptions,
    NDescriptionsItem,
    NButton,
} from 'naive-ui'
import { dbTelegram121, Telegram121 } from '@/models'

// 接收父组件传递过来的值
const props = defineProps({
    viewingId: Number,
})

const emits = defineEmits(['finish-view'])
const isVisible = ref(false)

// fetch data from database
const row = ref<Telegram121 | null>(null)

watch(
    () => props.viewingId,
    async (newId) => {
        if (newId && newId > 0) {
            row.value = (await dbTelegram121.fetchById(newId)) as Telegram121
            isVisible.value = true
        } else {
            isVisible.value = false
        }
    },
    { immediate: true },
)

const closeViewer = () => {
    emits('finish-view')
}
</script>

<style scoped>
:deep(.n-drawer-header__main) {
    display: flex;
    justify-content: flex-end;
    width: 100%;
}
</style>
