/**
 * S7 Data Types - Siemens S7 compatible data type conversions
 * Based on nodes7.js library for authentic S7 format handling
 *
 * Supported S7 Data Types:
 * - REAL (4 bytes) - 32-bit float
 * - LREAL (8 bytes) - 64-bit double
 * - DINT (4 bytes) - 32-bit signed integer
 * - INT (2 bytes) - 16-bit signed integer
 * - DWORD (4 bytes) - 32-bit unsigned integer
 * - WORD (2 bytes) - 16-bit unsigned integer
 * - BYTE (1 byte) - 8-bit unsigned integer
 * - BOOL/X (1 bit) - Boolean
 * - STRING/S (variable) - S7 string format
 * - CHAR/C (1 byte) - Character
 * - DT/DTZ (8 bytes) - DateTime in BCD format
 * - DTL/DTLZ (12 bytes) - DateTime Long format
 * - TIMER (2 bytes) - Timer value
 * - COUNTER (2 bytes) - Counter value
 * - LINT (8 bytes) - 64-bit signed integer
 */

/**
 * S7 Data Types utility class
 * Provides Siemens S7 compatible data type conversions with proper big-endian format
 */
class S7DataTypes {
    /**
     * Convert buffer to 32-bit float (Real) - S7 big-endian format
     * @param {Buffer} buffer - 4-byte buffer
     * @returns {number} - Float value
     */
    static bufferToReal(buffer) {
        if (buffer.length < 4) {
            throw new Error('Buffer too short for Real (requires 4 bytes)');
        }
        return buffer.readFloatBE(0); // S7 uses big-endian
    }

    /**
     * Convert 32-bit float to buffer - S7 big-endian format
     * @param {number} value - Float value
     * @returns {Buffer} - 4-byte buffer
     */
    static realToBuffer(value) {
        const buffer = Buffer.alloc(4);
        buffer.writeFloatBE(value, 0); // S7 uses big-endian
        return buffer;
    }

    /**
     * Convert buffer to 64-bit double (LReal) - S7 big-endian format
     * @param {Buffer} buffer - 8-byte buffer
     * @returns {number} - Double value
     */
    static bufferToLReal(buffer) {
        if (buffer.length < 8) {
            throw new Error('Buffer too short for LReal (requires 8 bytes)');
        }
        return buffer.readDoubleBE(0); // S7 uses big-endian
    }

    /**
     * Convert 64-bit double to buffer - S7 big-endian format
     * @param {number} value - Double value
     * @returns {Buffer} - 8-byte buffer
     */
    static lRealToBuffer(value) {
        const buffer = Buffer.alloc(8);
        buffer.writeDoubleBE(value, 0); // S7 uses big-endian
        return buffer;
    }

    /**
     * Convert buffer to 16-bit signed integer (Int) - S7 big-endian format
     * @param {Buffer} buffer - 2-byte buffer
     * @returns {number} - Integer value
     */
    static bufferToInt(buffer) {
        if (buffer.length < 2) {
            throw new Error('Buffer too short for Int (requires 2 bytes)');
        }
        return buffer.readInt16BE(0); // S7 uses big-endian
    }

    /**
     * Convert 16-bit signed integer to buffer - S7 big-endian format
     * @param {number} value - Integer value
     * @returns {Buffer} - 2-byte buffer
     */
    static intToBuffer(value) {
        const buffer = Buffer.alloc(2);
        buffer.writeInt16BE(value, 0); // S7 uses big-endian
        return buffer;
    }

    /**
     * Convert buffer to 32-bit signed integer (DInt) - S7 big-endian format
     * @param {Buffer} buffer - 4-byte buffer
     * @returns {number} - Integer value
     */
    static bufferToDInt(buffer) {
        if (buffer.length < 4) {
            throw new Error('Buffer too short for DInt (requires 4 bytes)');
        }
        return buffer.readInt32BE(0); // S7 uses big-endian
    }

    /**
     * Convert 32-bit signed integer to buffer - S7 big-endian format
     * @param {number} value - Integer value
     * @returns {Buffer} - 4-byte buffer
     */
    static dIntToBuffer(value) {
        const buffer = Buffer.alloc(4);
        buffer.writeInt32BE(value, 0); // S7 uses big-endian
        return buffer;
    }

    /**
     * Convert buffer to 16-bit unsigned integer (Word) - S7 big-endian format
     * @param {Buffer} buffer - 2-byte buffer
     * @returns {number} - Unsigned integer value
     */
    static bufferToWord(buffer) {
        if (buffer.length < 2) {
            throw new Error('Buffer too short for Word (requires 2 bytes)');
        }
        return buffer.readUInt16BE(0); // S7 uses big-endian
    }

    /**
     * Convert 16-bit unsigned integer to buffer - S7 big-endian format
     * @param {number} value - Unsigned integer value
     * @returns {Buffer} - 2-byte buffer
     */
    static wordToBuffer(value) {
        const buffer = Buffer.alloc(2);
        buffer.writeUInt16BE(value, 0); // S7 uses big-endian
        return buffer;
    }

    /**
     * Convert buffer to 32-bit unsigned integer (DWord) - S7 big-endian format
     * @param {Buffer} buffer - 4-byte buffer
     * @returns {number} - Unsigned integer value
     */
    static bufferToDWord(buffer) {
        if (buffer.length < 4) {
            throw new Error('Buffer too short for DWord (requires 4 bytes)');
        }
        return buffer.readUInt32BE(0); // S7 uses big-endian
    }

    /**
     * Convert 32-bit unsigned integer to buffer - S7 big-endian format
     * @param {number} value - Unsigned integer value
     * @returns {Buffer} - 4-byte buffer
     */
    static dWordToBuffer(value) {
        const buffer = Buffer.alloc(4);
        buffer.writeUInt32BE(value, 0); // S7 uses big-endian
        return buffer;
    }

    /**
     * Convert buffer to boolean (single bit)
     * @param {Buffer} buffer - 1-byte buffer
     * @param {number} bit - Bit position (0-7)
     * @returns {boolean} - Boolean value
     */
    static bufferToBool(buffer, bit = 0) {
        if (buffer.length < 1) {
            throw new Error('Buffer too short for Bool (requires 1 byte)');
        }
        if (bit < 0 || bit > 7) {
            throw new Error('Bit position must be between 0 and 7');
        }
        return (buffer[0] & (1 << bit)) !== 0;
    }

    /**
     * Convert boolean to buffer (single bit)
     * @param {boolean} value - Boolean value
     * @param {number} bit - Bit position (0-7)
     * @returns {Buffer} - 1-byte buffer
     */
    static boolToBuffer(value, bit = 0) {
        if (bit < 0 || bit > 7) {
            throw new Error('Bit position must be between 0 and 7');
        }
        const buffer = Buffer.alloc(1);
        if (value) {
            buffer[0] |= 1 << bit;
        }
        return buffer;
    }

    /**
     * Convert buffer to byte (8-bit unsigned integer)
     * @param {Buffer} buffer - 1-byte buffer
     * @returns {number} - Byte value (0-255)
     */
    static bufferToByte(buffer) {
        if (buffer.length < 1) {
            throw new Error('Buffer too short for Byte (requires 1 byte)');
        }
        return buffer.readUInt8(0);
    }

    /**
     * Convert byte to buffer
     * @param {number} value - Byte value (0-255)
     * @returns {Buffer} - 1-byte buffer
     */
    static byteToBuffer(value) {
        if (value < 0 || value > 255) {
            throw new Error('Byte value must be between 0 and 255');
        }
        const buffer = Buffer.alloc(1);
        buffer.writeUInt8(value, 0);
        return buffer;
    }

    /**
     * Convert buffer to character
     * @param {Buffer} buffer - 1-byte buffer
     * @returns {string} - Character
     */
    static bufferToChar(buffer) {
        if (buffer.length < 1) {
            throw new Error('Buffer too short for Char (requires 1 byte)');
        }
        return String.fromCharCode(buffer.readUInt8(0));
    }

    /**
     * Convert character to buffer
     * @param {string} value - Character
     * @returns {Buffer} - 1-byte buffer
     */
    static charToBuffer(value) {
        if (typeof value !== 'string' || value.length !== 1) {
            throw new Error('Value must be a single character');
        }
        const buffer = Buffer.alloc(1);
        buffer.writeUInt8(value.charCodeAt(0), 0);
        return buffer;
    }

    /**
     * Convert buffer to S7 string
     * S7 string format: [max_length][actual_length][string_data...]
     * @param {Buffer} buffer - Buffer containing S7 string
     * @returns {string} - Decoded string
     */
    static bufferToS7String(buffer) {
        try {
            if (buffer.length < 2) {
                throw new Error(
                    'Buffer too short for S7 string (minimum 2 bytes)'
                );
            }

            const maxLength = buffer[0];
            const actualLength = buffer[1];

            if (actualLength > maxLength) {
                throw new Error(
                    `Invalid S7 string: actual length (${actualLength}) > max length (${maxLength})`
                );
            }

            if (buffer.length < 2 + actualLength) {
                throw new Error(
                    `Buffer too short for S7 string data (expected ${
                        2 + actualLength
                    }, got ${buffer.length})`
                );
            }

            // Extract string data
            const stringData = buffer.subarray(2, 2 + actualLength);
            return stringData.toString('ascii');
        } catch (error) {
            console.error('Error decoding S7 string:', error);
            return '';
        }
    }

    /**
     * Convert string to S7 string buffer
     * @param {string} value - String value
     * @param {number} maxLength - Maximum string length (default: 254)
     * @returns {Buffer} - S7 string buffer
     */
    static s7StringToBuffer(value, maxLength = 254) {
        try {
            const stringValue = value || '';
            const actualLength = Math.min(stringValue.length, maxLength);
            const buffer = Buffer.alloc(2 + maxLength);

            // Write max length
            buffer[0] = maxLength;

            // Write actual length
            buffer[1] = actualLength;

            // Write string data
            if (actualLength > 0) {
                buffer.write(
                    stringValue.substring(0, actualLength),
                    2,
                    actualLength,
                    'ascii'
                );
            }

            // Fill remaining bytes with zeros
            if (actualLength < maxLength) {
                buffer.fill(0, 2 + actualLength, 2 + maxLength);
            }

            return buffer;
        } catch (error) {
            console.error('Error encoding S7 string:', error);
            const buffer = Buffer.alloc(2 + maxLength);
            buffer[0] = maxLength;
            buffer[1] = 0;
            return buffer;
        }
    }

    /**
     * Convert buffer to fixed-length string (null-terminated)
     * @param {Buffer} buffer - Buffer containing string data
     * @param {string} encoding - String encoding (default: 'ascii')
     * @returns {string} - Decoded string
     */
    static bufferToFixedString(buffer, encoding = 'ascii') {
        try {
            // Find null terminator
            let length = buffer.length;
            for (let i = 0; i < buffer.length; i++) {
                if (buffer[i] === 0) {
                    length = i;
                    break;
                }
            }

            return buffer.subarray(0, length).toString(encoding);
        } catch (error) {
            console.error('Error decoding fixed string:', error);
            return '';
        }
    }

    /**
     * Convert string to fixed-length buffer (null-terminated)
     * @param {string} value - String value
     * @param {number} length - Fixed buffer length
     * @param {string} encoding - String encoding (default: 'ascii')
     * @returns {Buffer} - Fixed-length string buffer
     */
    static fixedStringToBuffer(value, length, encoding = 'ascii') {
        try {
            const buffer = Buffer.alloc(length);
            const stringValue = value || '';
            const maxStringLength = length - 1; // Reserve space for null terminator

            if (stringValue.length > 0) {
                const truncatedValue = stringValue.substring(
                    0,
                    maxStringLength
                );
                buffer.write(
                    truncatedValue,
                    0,
                    truncatedValue.length,
                    encoding
                );
            }

            // Null terminator is already set by Buffer.alloc (fills with zeros)
            return buffer;
        } catch (error) {
            console.error('Error encoding fixed string:', error);
            return Buffer.alloc(length);
        }
    }

    /**
     * Convert buffer to byte array (for debugging)
     * @param {Buffer} buffer - Buffer to convert
     * @returns {Array} - Array of byte values
     */
    static bufferToByteArray(buffer) {
        return Array.from(buffer);
    }

    /**
     * Convert byte array to buffer
     * @param {Array} byteArray - Array of byte values
     * @returns {Buffer} - Buffer
     */
    static byteArrayToBuffer(byteArray) {
        return Buffer.from(byteArray);
    }

    /**
     * Get hex string representation of buffer
     * @param {Buffer} buffer - Buffer to convert
     * @param {string} separator - Separator between bytes (default: ' ')
     * @returns {string} - Hex string
     */
    static bufferToHexString(buffer, separator = ' ') {
        return Array.from(buffer)
            .map((byte) => byte.toString(16).padStart(2, '0').toUpperCase())
            .join(separator);
    }

    /**
     * Convert buffer to Time (S7 Time format - 32-bit milliseconds)
     * @param {Buffer} buffer - 4-byte buffer
     * @returns {number} - Time in milliseconds
     */
    static bufferToTime(buffer) {
        if (buffer.length < 4) {
            throw new Error('Buffer too short for Time (requires 4 bytes)');
        }
        return buffer.readUInt32BE(0); // S7 uses big-endian
    }

    /**
     * Convert time to buffer (S7 Time format)
     * @param {number} milliseconds - Time in milliseconds
     * @returns {Buffer} - 4-byte buffer
     */
    static timeToBuffer(milliseconds) {
        const buffer = Buffer.alloc(4);
        buffer.writeUInt32BE(milliseconds, 0); // S7 uses big-endian
        return buffer;
    }

    /**
     * Convert buffer to Date (S7 Date format)
     * @param {Buffer} buffer - 2-byte buffer (days since 1990-01-01)
     * @returns {Date} - Date object
     */
    static bufferToDate(buffer) {
        if (buffer.length < 2) {
            throw new Error('Buffer too short for Date (requires 2 bytes)');
        }
        const days = buffer.readUInt16BE(0);
        const baseDate = new Date('1990-01-01');
        return new Date(baseDate.getTime() + days * 24 * 60 * 60 * 1000);
    }

    /**
     * Convert date to buffer (S7 Date format)
     * @param {Date} date - Date object
     * @returns {Buffer} - 2-byte buffer
     */
    static dateToBuffer(date) {
        const baseDate = new Date('1990-01-01');
        const diffTime = date.getTime() - baseDate.getTime();
        const days = Math.floor(diffTime / (24 * 60 * 60 * 1000));

        const buffer = Buffer.alloc(2);
        buffer.writeUInt16BE(days, 0);
        return buffer;
    }

    /**
     * Convert BCD byte to decimal
     * @param {number} n - BCD byte
     * @returns {number} - Decimal value
     */
    static fromBCD(n) {
        return (n >> 4) * 10 + (n & 0xf);
    }

    /**
     * Convert decimal to BCD byte
     * @param {number} n - Decimal value
     * @returns {number} - BCD byte
     */
    static toBCD(n) {
        return ((n / 10) << 4) | n % 10;
    }

    /**
     * Convert buffer to S7 DateTime (8 bytes)
     * S7 DateTime format: 8 bytes in BCD format
     * Byte 0: Year (2 digits BCD, 00-99)
     * Byte 1: Month (BCD, 01-12)
     * Byte 2: Day (BCD, 01-31)
     * Byte 3: Hour (BCD, 00-23)
     * Byte 4: Minute (BCD, 00-59)
     * Byte 5: Second (BCD, 00-59)
     * Byte 6: Milliseconds tens (BCD, 00-99)
     * Byte 7: Milliseconds units + weekday (BCD)
     * @param {Buffer} buffer - 8-byte buffer
     * @param {boolean} isUTC - Whether to interpret as UTC (default: false)
     * @returns {Date} - Date object
     */
    static bufferToDateTime(buffer, isUTC = false) {
        if (buffer.length < 8) {
            throw new Error('Buffer too short for DateTime (requires 8 bytes)');
        }

        try {
            const year = this.fromBCD(buffer.readUInt8(0));
            const month = this.fromBCD(buffer.readUInt8(1));
            const day = this.fromBCD(buffer.readUInt8(2));
            const hour = this.fromBCD(buffer.readUInt8(3));
            const min = this.fromBCD(buffer.readUInt8(4));
            const sec = this.fromBCD(buffer.readUInt8(5));
            const ms_1 = this.fromBCD(buffer.readUInt8(6));
            const ms_2 = this.fromBCD(buffer.readUInt8(7) & 0xf0);

            let date;
            if (isUTC) {
                date = new Date(
                    Date.UTC(
                        (year > 89 ? 1900 : 2000) + year,
                        month - 1,
                        day,
                        hour,
                        min,
                        sec,
                        ms_1 * 10 + ms_2 / 10
                    )
                );
            } else {
                date = new Date(
                    (year > 89 ? 1900 : 2000) + year,
                    month - 1,
                    day,
                    hour,
                    min,
                    sec,
                    ms_1 * 10 + ms_2 / 10
                );
            }

            return date;
        } catch (error) {
            console.error('Error parsing S7 DateTime:', error);
            return new Date(); // Return current time as fallback
        }
    }

    /**
     * Convert Date to S7 DateTime buffer (8 bytes)
     * S7 DateTime format: 8 bytes in BCD format
     * @param {Date} date - Date object
     * @param {boolean} isUTC - Whether to encode as UTC (default: false)
     * @returns {Buffer} - 8-byte buffer
     */
    static dateTimeToBuffer(date, isUTC = false) {
        try {
            if (!(date instanceof Date)) {
                if (date > 631152000000 && date < 3786911999999) {
                    // is between "1990-01-01T00:00:00.000Z" and "2089-12-31T23:59:59.999Z" in JS epoch
                    // as per data type's range definition
                    date = new Date(date);
                } else {
                    console.error('Unsupported value for S7 DateTime:', date);
                    date = new Date(); // Use current time as fallback
                }
            }

            const buffer = Buffer.alloc(8);

            if (isUTC) {
                buffer.writeUInt8(this.toBCD(date.getUTCFullYear() % 100), 0);
                buffer.writeUInt8(this.toBCD(date.getUTCMonth() + 1), 1);
                buffer.writeUInt8(this.toBCD(date.getUTCDate()), 2);
                buffer.writeUInt8(this.toBCD(date.getUTCHours()), 3);
                buffer.writeUInt8(this.toBCD(date.getUTCMinutes()), 4);
                buffer.writeUInt8(this.toBCD(date.getUTCSeconds()), 5);
                buffer.writeUInt8(
                    this.toBCD((date.getUTCMilliseconds() / 10) >> 0),
                    6
                );
                buffer.writeUInt8(
                    this.toBCD(
                        (date.getUTCMilliseconds() % 10) * 10 +
                            (date.getUTCDay() + 1)
                    ),
                    7
                );
            } else {
                buffer.writeUInt8(this.toBCD(date.getFullYear() % 100), 0);
                buffer.writeUInt8(this.toBCD(date.getMonth() + 1), 1);
                buffer.writeUInt8(this.toBCD(date.getDate()), 2);
                buffer.writeUInt8(this.toBCD(date.getHours()), 3);
                buffer.writeUInt8(this.toBCD(date.getMinutes()), 4);
                buffer.writeUInt8(this.toBCD(date.getSeconds()), 5);
                buffer.writeUInt8(
                    this.toBCD((date.getMilliseconds() / 10) >> 0),
                    6
                );
                buffer.writeUInt8(
                    this.toBCD(
                        (date.getMilliseconds() % 10) * 10 + (date.getDay() + 1)
                    ),
                    7
                );
            }

            return buffer;
        } catch (error) {
            console.error('Error encoding S7 DateTime:', error);
            // Return epoch time as fallback
            const buffer = Buffer.alloc(8);
            buffer.fill(0);
            return buffer;
        }
    }

    /**
     * Convert buffer to S7 DateTime Long (DTL) - 12 bytes
     * DTL format: Year(2) Month(1) Day(1) Weekday(1) Hour(1) Minute(1) Second(1) Nanoseconds(4)
     * @param {Buffer} buffer - 12-byte buffer
     * @param {boolean} isUTC - Whether to interpret as UTC (default: false)
     * @returns {Date} - Date object
     */
    static bufferToDateTimeLong(buffer, isUTC = false) {
        if (buffer.length < 12) {
            throw new Error(
                'Buffer too short for DateTime Long (requires 12 bytes)'
            );
        }

        try {
            const year = buffer.readUInt16BE(0);
            const month = buffer.readUInt8(2);
            const day = buffer.readUInt8(3);
            // const weekday = buffer.readUInt8(4); // Not used in Date construction
            const hour = buffer.readUInt8(5);
            const min = buffer.readUInt8(6);
            const sec = buffer.readUInt8(7);
            const ns = buffer.readUInt32BE(8);

            let date;
            if (isUTC) {
                date = new Date(
                    Date.UTC(year, month - 1, day, hour, min, sec, ns / 1e6)
                );
            } else {
                date = new Date(year, month - 1, day, hour, min, sec, ns / 1e6);
            }

            return date;
        } catch (error) {
            console.error('Error parsing S7 DateTime Long:', error);
            return new Date(); // Return current time as fallback
        }
    }

    /**
     * Convert Date to S7 DateTime Long buffer (DTL) - 12 bytes
     * @param {Date} date - Date object
     * @param {boolean} isUTC - Whether to encode as UTC (default: false)
     * @returns {Buffer} - 12-byte buffer
     */
    static dateTimeLongToBuffer(date, isUTC = false) {
        try {
            if (!(date instanceof Date)) {
                if (date >= 0 && date < 9223382836854) {
                    // is between "1970-01-01T00:00:00.000Z" and "2262-04-11T23:47:16.854Z" in JS epoch
                    // as per data type's range definition
                    date = new Date(date);
                } else {
                    console.error(
                        'Unsupported value for S7 DateTime Long:',
                        date
                    );
                    date = new Date(); // Use current time as fallback
                }
            }

            const buffer = Buffer.alloc(12);

            if (isUTC) {
                buffer.writeUInt16BE(date.getUTCFullYear(), 0);
                buffer.writeUInt8(date.getUTCMonth() + 1, 2);
                buffer.writeUInt8(date.getUTCDate(), 3);
                buffer.writeUInt8(date.getUTCDay() + 1, 4); // Weekday (1=Sunday, 7=Saturday)
                buffer.writeUInt8(date.getUTCHours(), 5);
                buffer.writeUInt8(date.getUTCMinutes(), 6);
                buffer.writeUInt8(date.getUTCSeconds(), 7);
                buffer.writeUInt32BE(date.getUTCMilliseconds() * 1e6, 8); // Convert ms to ns
            } else {
                buffer.writeUInt16BE(date.getFullYear(), 0);
                buffer.writeUInt8(date.getMonth() + 1, 2);
                buffer.writeUInt8(date.getDate(), 3);
                buffer.writeUInt8(date.getDay() + 1, 4); // Weekday (1=Sunday, 7=Saturday)
                buffer.writeUInt8(date.getHours(), 5);
                buffer.writeUInt8(date.getMinutes(), 6);
                buffer.writeUInt8(date.getSeconds(), 7);
                buffer.writeUInt32BE(date.getMilliseconds() * 1e6, 8); // Convert ms to ns
            }

            return buffer;
        } catch (error) {
            console.error('Error encoding S7 DateTime Long:', error);
            // Return epoch time as fallback
            const buffer = Buffer.alloc(12);
            buffer.fill(0);
            return buffer;
        }
    }

    /**
     * Convert buffer to Timer value (2 bytes)
     * S7 Timer format: 16-bit signed integer
     * @param {Buffer} buffer - 2-byte buffer
     * @returns {number} - Timer value
     */
    static bufferToTimer(buffer) {
        if (buffer.length < 2) {
            throw new Error('Buffer too short for Timer (requires 2 bytes)');
        }
        return buffer.readInt16BE(0); // S7 uses big-endian
    }

    /**
     * Convert timer value to buffer
     * @param {number} value - Timer value
     * @returns {Buffer} - 2-byte buffer
     */
    static timerToBuffer(value) {
        const buffer = Buffer.alloc(2);
        buffer.writeInt16BE(value, 0); // S7 uses big-endian
        return buffer;
    }

    /**
     * Convert buffer to Counter value (2 bytes)
     * S7 Counter format: 16-bit signed integer
     * @param {Buffer} buffer - 2-byte buffer
     * @returns {number} - Counter value
     */
    static bufferToCounter(buffer) {
        if (buffer.length < 2) {
            throw new Error('Buffer too short for Counter (requires 2 bytes)');
        }
        return buffer.readInt16BE(0); // S7 uses big-endian
    }

    /**
     * Convert counter value to buffer
     * @param {number} value - Counter value
     * @returns {Buffer} - 2-byte buffer
     */
    static counterToBuffer(value) {
        const buffer = Buffer.alloc(2);
        buffer.writeInt16BE(value, 0); // S7 uses big-endian
        return buffer;
    }
}

export default S7DataTypes;
