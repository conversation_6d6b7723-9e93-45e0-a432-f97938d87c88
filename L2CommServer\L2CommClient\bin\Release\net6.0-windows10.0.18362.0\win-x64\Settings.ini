; All comments start with ;
; SERVER defines ip address and port the server listens on
[SERVER]
IP=127.0.0.1
PORT=1111
TITLE=L2 Communication Server

;CLIENT defines actively connected peers
;multiple CLIENT sections allowed
[CLIENT]
IP=**************
PORT=80

;[CLIENT]
;IP=***************
;PORT=80
;CYCLIC_SEND defined cyclic (periodic) telegrams send out
;telegram to be sent is defined by TEL_NO
;telegram processor is called from Plugins
;multiple CYCLIC_SEND sections allowed
[CYCLIC_SEND]
PEER_IP=127.0.0.1
TEL_NO=21
INTERVAL=5

[CYCLIC_SEND2]
PEER_IP=127.0.0.1
TEL_NO=1002
INTERVAL=10
