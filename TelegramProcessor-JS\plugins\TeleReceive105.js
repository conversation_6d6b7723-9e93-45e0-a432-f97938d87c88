const {
    ReceivePluginBase,
    exportPlugin,
    S7DataTypes,
} = require('../plugin-base');

/**
 * TeleReceive105 - String message telegram processor
 * Demonstrates S7 string processing capabilities
 * Processes telegram 105 containing string data and numeric values
 */
class TeleReceive105 extends ReceivePluginBase {
    constructor() {
        super();
        this.telegramNo = 105;
        this.pluginName = 'TeleReceive105';
        this.version = '1.0.0';
        this.description =
            'Process telegram 105 - String message with S7 string format';
        this.author = 'L2CommServer Team';
        this.messageLength = 0; // Variable length due to string content

        // Plugin-specific properties
        this.messageText = '';
        this.messageId = 0;
        this.priority = 0;
        this.timestamp = null;
    }

    /**
     * Parse message body
     * Message format:
     * - Message ID (4 bytes, DInt)
     * - Priority (4 bytes, DInt)
     * - Message Text (S7 String, max 100 chars)
     * - Reserved (4 bytes)
     *
     * @param {<PERSON>uffer} body - Message body
     * @returns {boolean} - True if parsing was successful
     */
    parse(body) {
        try {
            if (body.length < 12) {
                // Minimum: 4 + 4 + 2 (empty S7 string) + 4
                console.warn(
                    `[${this.pluginName}] Message too short. Expected at least 12 bytes, got ${body.length}`
                );
                return false;
            }

            let offset = 0;

            // Parse Message ID (4 bytes)
            this.messageId = S7DataTypes.bufferToDInt(
                body.subarray(offset, offset + 4)
            );
            offset += 4;

            // Parse Priority (4 bytes)
            this.priority = S7DataTypes.bufferToDInt(
                body.subarray(offset, offset + 4)
            );
            offset += 4;

            // Parse S7 String (variable length)
            // First, check if we have enough bytes for S7 string header
            if (body.length < offset + 2) {
                console.warn(
                    `[${this.pluginName}] Not enough data for S7 string header`
                );
                return false;
            }

            // Get S7 string max length and actual length
            const maxLength = body[offset];
            const actualLength = body[offset + 1];

            // Check if we have enough data for the complete string
            if (body.length < offset + 2 + maxLength) {
                console.warn(
                    `[${this.pluginName}] Not enough data for complete S7 string`
                );
                return false;
            }

            // Extract S7 string
            const stringBuffer = body.subarray(offset, offset + 2 + maxLength);
            this.messageText = S7DataTypes.bufferToS7String(stringBuffer);
            offset += 2 + maxLength;

            // Parse reserved bytes (4 bytes) - skip if present
            if (body.length >= offset + 4) {
                // Reserved bytes - we can ignore them
                offset += 4;
            }

            this.timestamp = new Date();

            // Validate parsed data
            if (this.messageId < 0) {
                console.warn(
                    `[${this.pluginName}] Invalid message ID: ${this.messageId}`
                );
                return false;
            }

            if (this.priority < 0 || this.priority > 10) {
                console.warn(
                    `[${this.pluginName}] Priority out of range: ${this.priority}`
                );
                // Don't fail parsing, just warn
            }

            return true;
        } catch (error) {
            console.error(`[${this.pluginName}] Parse error:`, error);
            return false;
        }
    }

    /**
     * Process the parsed message
     * @param {Buffer} body - Message body
     */
    async process(body) {
        try {
            console.log(
                `[${this.pluginName}] Processing string message telegram ${this.telegramNo}`
            );

            // Log message details
            console.log(`[${this.pluginName}] Message ID: ${this.messageId}`);
            console.log(
                `[${this.pluginName}] Priority: ${
                    this.priority
                } (${this.getPriorityText()})`
            );
            console.log(`[${this.pluginName}] Message: "${this.messageText}"`);

            // Process based on priority
            if (this.priority >= 8) {
                console.warn(
                    `[${this.pluginName}] HIGH PRIORITY MESSAGE: ${this.messageText}`
                );
                await this.sendAcknowledgment(true);
            } else if (this.priority >= 5) {
                console.log(
                    `[${this.pluginName}] Medium priority message received`
                );
                await this.sendAcknowledgment(false);
            } else {
                console.log(
                    `[${this.pluginName}] Low priority message received`
                );
            }

            // Check for specific message content
            if (this.messageText.toLowerCase().includes('alarm')) {
                console.warn(
                    `[${this.pluginName}] ALARM detected in message: ${this.messageText}`
                );
                await this.handleAlarmMessage();
            }

            if (this.messageText.toLowerCase().includes('shutdown')) {
                console.warn(
                    `[${this.pluginName}] SHUTDOWN command detected: ${this.messageText}`
                );
                await this.handleShutdownMessage();
            }

            console.log(
                `[${this.pluginName}] String message processing completed`
            );
        } catch (error) {
            console.error(`[${this.pluginName}] Process error:`, error);
        }
    }

    /**
     * Send acknowledgment telegram
     * @param {boolean} isHighPriority - Whether this is high priority
     */
    async sendAcknowledgment(isHighPriority) {
        try {
            // Compose acknowledgment message
            const ackText = isHighPriority ? 'HIGH_PRIORITY_ACK' : 'ACK';
            const ackBody = Buffer.alloc(12 + 2 + 20); // 4+4+4 + S7String(max 20)

            let offset = 0;

            // Original Message ID (4 bytes)
            S7DataTypes.dIntToBuffer(this.messageId).copy(ackBody, offset);
            offset += 4;

            // Acknowledgment Code (4 bytes) - 1000 = ACK, 1001 = HIGH_PRIORITY_ACK
            const ackCode = isHighPriority ? 1001 : 1000;
            S7DataTypes.dIntToBuffer(ackCode).copy(ackBody, offset);
            offset += 4;

            // Status (4 bytes) - 1 = OK
            S7DataTypes.dIntToBuffer(1).copy(ackBody, offset);
            offset += 4;

            // Acknowledgment text (S7 String, max 20 chars)
            const ackStringBuffer = S7DataTypes.s7StringToBuffer(ackText, 20);
            ackStringBuffer.copy(ackBody, offset);

            // Create complete telegram (response telegram number: 205)
            const ackTelegram = this.composeResponseTelegram(205, ackBody);

            // Send acknowledgment
            this.sendRequest(ackTelegram);

            console.log(
                `[${this.pluginName}] Acknowledgment sent for message ${this.messageId}`
            );
        } catch (error) {
            console.error(
                `[${this.pluginName}] Failed to send acknowledgment:`,
                error
            );
        }
    }

    /**
     * Handle alarm message
     */
    async handleAlarmMessage() {
        console.warn(
            `[${this.pluginName}] ALARM HANDLER: Processing alarm message`
        );
        // Add alarm-specific processing here
    }

    /**
     * Handle shutdown message
     */
    async handleShutdownMessage() {
        console.warn(
            `[${this.pluginName}] SHUTDOWN HANDLER: Processing shutdown command`
        );
        // Add shutdown-specific processing here
    }

    /**
     * Get priority text description
     * @returns {string} - Priority description
     */
    getPriorityText() {
        if (this.priority >= 8) return 'HIGH';
        if (this.priority >= 5) return 'MEDIUM';
        if (this.priority >= 1) return 'LOW';
        return 'INFO';
    }

    /**
     * Debug output for the plugin
     */
    debugOutput() {
        console.log(`[${this.pluginName}] Debug Info:`);
        console.log(`  Message ID: ${this.messageId}`);
        console.log(`  Priority: ${this.priority} (${this.getPriorityText()})`);
        console.log(`  Message Text: "${this.messageText}"`);
        console.log(`  Text Length: ${this.messageText.length} characters`);
        console.log(
            `  Timestamp: ${
                this.timestamp ? this.timestamp.toISOString() : 'None'
            }`
        );
    }

    /**
     * Compose response telegram with header
     * @param {number} responseTelemNo - Response telegram number
     * @param {Buffer} responseBody - Response message body
     * @returns {Buffer} - Complete telegram with header
     */
    composeResponseTelegram(responseTelemNo, responseBody) {
        const headerLength = 20;
        const totalLength = headerLength + responseBody.length;
        const telegram = Buffer.alloc(totalLength);

        // Write header
        telegram.writeUInt16LE(responseTelemNo, 0); // Telegram number
        telegram.writeUInt16LE(totalLength, 2); // Telegram length
        telegram.write('L2'.padEnd(2, '\0'), 4, 2, 'ascii'); // Sender ID
        telegram.write('PL'.padEnd(2, '\0'), 6, 2, 'ascii'); // Receiver ID

        // Write timestamp (8 bytes)
        const now = Date.now();
        telegram.writeBigUInt64LE(BigInt(now), 8);

        telegram.writeUInt16LE(0, 16); // Telegram counter
        telegram.writeUInt16LE(0, 18); // Reserved

        // Write message body
        responseBody.copy(telegram, headerLength);

        return telegram;
    }

    /**
     * Get current message data
     * @returns {Object} - Current message data
     */
    getMessageData() {
        return {
            messageId: this.messageId,
            priority: this.priority,
            priorityText: this.getPriorityText(),
            messageText: this.messageText,
            timestamp: this.timestamp,
        };
    }
}

// Export the plugin
module.exports = exportPlugin(TeleReceive105);
