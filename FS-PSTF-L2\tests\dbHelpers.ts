import knex, { K<PERSON> } from 'knex'

function knexConnect(): Knex {
    // create knex and connect to server
    const knexInstance: Knex = knex({
        client: 'mssql',
        connection: {
            server: '192.168.124.131',
            port: 1433,
            database: 'FS-PSTF-L2',
            user: 'sa',
            password: 'h3r3w3g0#',
            connectionTimeout: 10000,
            options: {
                appName: 'test',
                encrypt: true,
                trustServerCertificate: true,
            },
        },
    })

    return knexInstance
}

// delete all records from table
async function purgeTable(knex: Knex, tableName: string) {
    await knex(tableName).truncate()
}

// populate table (batch insert)
async function populateTable(knex: Knex, tableName: string, records: any[]) {
    await knex(tableName).insert(records)
}

export default {
    knexConnect,
    purgeTable,
    populateTable,
}
