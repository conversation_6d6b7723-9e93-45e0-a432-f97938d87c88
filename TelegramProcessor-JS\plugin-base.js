/**
 * Base classes and interfaces for telegram processing plugins
 * This file should be used by plugin developers as a reference
 */

/**
 * Base class for all telegram plugins
 */
class PluginBase {
    constructor() {
        this.telegramNo = 0;
        this.pluginName = '';
        this.version = '1.0.0';
        this.description = '';
        this.author = '';
    }

    /**
     * Get plugin metadata
     * @returns {Object} Plugin metadata
     */
    getMetadata() {
        return {
            telegramNo: this.telegramNo,
            pluginName: this.pluginName,
            version: this.version,
            description: this.description,
            author: this.author,
            type: this.getPluginType(),
        };
    }

    /**
     * Get plugin type based on implemented methods
     * @returns {string} Plugin type ('receive', 'send', or 'both')
     */
    getPluginType() {
        const hasReceiveMethods =
            typeof this.parse === 'function' &&
            typeof this.process === 'function';
        const hasSendMethods =
            typeof this.compose === 'function' ||
            typeof this.composeBody === 'function';

        if (hasReceiveMethods && hasSendMethods) {
            return 'both';
        } else if (hasReceiveMethods) {
            return 'receive';
        } else if (hasSendMethods) {
            return 'send';
        } else {
            return 'unknown';
        }
    }

    /**
     * Debug output message content
     * Override this method in your plugin
     */
    debugOutput() {
        console.log(`[${this.pluginName}] Debug output not implemented`);
    }

    /**
     * Validate plugin implementation
     * @returns {Array} Array of validation errors
     */
    validate() {
        const errors = [];

        if (!this.telegramNo || this.telegramNo <= 0) {
            errors.push('telegramNo must be a positive number');
        }

        if (!this.pluginName || this.pluginName.trim() === '') {
            errors.push('pluginName is required');
        }

        return errors;
    }
}

/**
 * Base class for receive message plugins
 */
class ReceivePluginBase extends PluginBase {
    constructor() {
        super();
        this.sendRequestCallback = null;
        this.isSaveToDb = true;
        this.messageLength = 0; // Expected message length (0 = variable length)
    }

    /**
     * Set callback for send requests
     * @param {Function} callback - Callback function to send telegrams
     */
    setSendRequestCallback(callback) {
        this.sendRequestCallback = callback;
    }

    /**
     * Send a telegram as response
     * @param {Buffer} telegramData - Complete telegram data
     */
    sendRequest(telegramData) {
        if (
            this.sendRequestCallback &&
            typeof this.sendRequestCallback === 'function'
        ) {
            this.sendRequestCallback(telegramData);
        } else {
            console.warn(`[${this.pluginName}] No send callback available`);
        }
    }

    /**
     * Parse message body
     * Override this method in your plugin
     * @param {Buffer} body - Message body (without header)
     * @returns {boolean} - True if parsing was successful
     */
    parse(body) {
        throw new Error(
            `[${this.pluginName}] parse() method must be implemented`
        );
    }

    /**
     * Process message
     * Override this method in your plugin
     * @param {Buffer} body - Message body (without header)
     */
    async process(body) {
        throw new Error(
            `[${this.pluginName}] process() method must be implemented`
        );
    }

    /**
     * Save data to database
     * Override this method if you need custom database operations
     * @returns {number} - Number of affected rows
     */
    async saveToDb() {
        // Default implementation - will be enhanced when database integration is added
        console.log(`[${this.pluginName}] saveToDb() - default implementation`);
        return 1;
    }

    /**
     * Validate receive plugin implementation
     * @returns {Array} Array of validation errors
     */
    validate() {
        const errors = super.validate();

        // Check if required methods are implemented
        if (this.parse === ReceivePluginBase.prototype.parse) {
            errors.push('parse() method must be implemented');
        }

        if (this.process === ReceivePluginBase.prototype.process) {
            errors.push('process() method must be implemented');
        }

        return errors;
    }
}

/**
 * Base class for send message plugins
 */
class SendPluginBase extends PluginBase {
    constructor() {
        super();
        this.sendId = 'L2';
        this.recId = 'PL';
        this.telCounter = 0;
    }

    /**
     * Compose complete telegram (header + body)
     * Override this method in your plugin
     * @returns {Buffer} - Complete telegram data
     */
    compose() {
        throw new Error(
            `[${this.pluginName}] compose() method must be implemented`
        );
    }

    /**
     * Compose message body only
     * Override this method in your plugin
     * @returns {Buffer} - Message body data
     */
    composeBody() {
        throw new Error(
            `[${this.pluginName}] composeBody() method must be implemented`
        );
    }

    /**
     * Helper method to compose complete telegram with header
     * @param {Buffer} messageBody - Message body
     * @returns {Buffer} - Complete telegram
     */
    composeWithHeader(messageBody) {
        const headerLength = 20;
        const totalLength = headerLength + messageBody.length;
        const telegram = Buffer.alloc(totalLength);

        // Write header
        telegram.writeUInt16LE(this.telegramNo, 0); // Telegram number
        telegram.writeUInt16LE(totalLength, 2); // Telegram length
        telegram.write(this.sendId.padEnd(2, '\0'), 4, 2, 'ascii'); // Sender ID
        telegram.write(this.recId.padEnd(2, '\0'), 6, 2, 'ascii'); // Receiver ID

        // Write timestamp (8 bytes)
        const now = Date.now();
        telegram.writeBigUInt64LE(BigInt(now), 8);

        telegram.writeUInt16LE(this.telCounter, 16); // Telegram counter
        telegram.writeUInt16LE(0, 18); // Reserved

        // Write message body
        messageBody.copy(telegram, headerLength);

        return telegram;
    }

    /**
     * Increment telegram counter
     */
    incrementCounter() {
        this.telCounter = (this.telCounter + 1) % 65536; // 16-bit counter
    }

    /**
     * Validate send plugin implementation
     * @returns {Array} Array of validation errors
     */
    validate() {
        const errors = super.validate();

        // Check if required methods are implemented
        if (
            this.compose === SendPluginBase.prototype.compose &&
            this.composeBody === SendPluginBase.prototype.composeBody
        ) {
            errors.push(
                'Either compose() or composeBody() method must be implemented'
            );
        }

        if (!this.sendId || this.sendId.length > 2) {
            errors.push('sendId must be 1-2 characters');
        }

        if (!this.recId || this.recId.length > 2) {
            errors.push('recId must be 1-2 characters');
        }

        return errors;
    }
}

/**
 * Plugin export helper
 * Use this function to export your plugin
 * @param {PluginBase} pluginClass - Your plugin class
 * @returns {Object} - Plugin export object
 */
function exportPlugin(pluginClass) {
    return {
        PluginClass: pluginClass,
        createInstance: () => new pluginClass(),
        metadata: new pluginClass().getMetadata(),
    };
}

// Import S7 DataTypes
const S7DataTypes = require('./S7DataTypes');

/**
 * Data type conversion utilities using S7DataTypes
 * @deprecated Use S7DataTypes directly for new code
 */
class DataTypes extends S7DataTypes {
    // All methods are inherited from S7DataTypes
    // This class exists for backward compatibility
}

module.exports = {
    PluginBase,
    ReceivePluginBase,
    SendPluginBase,
    exportPlugin,
    DataTypes, // @deprecated Use S7DataTypes instead
    S7DataTypes,
};
