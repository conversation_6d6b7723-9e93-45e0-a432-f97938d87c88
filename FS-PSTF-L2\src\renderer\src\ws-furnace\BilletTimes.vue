<template>
    <div>
        <n-scrollbar trigger="none" x-scrollable>
            <n-table striped size="small" :single-line="false">
                <tbody>
                    <tr>
                        <td width="80px">创建时间</td>
                        <td colspan="3"></td>
                    </tr>
                    <tr>
                        <td>进炉时间</td>
                        <td colspan="3"></td>
                    </tr>
                    <!-- title -->
                    <tr align="center">
                        <td></td>
                        <td>进入时温度</td>
                        <td>最高温度</td>
                        <td>计时 (小时)</td>
                    </tr>
                    <tr>
                        <td>预热段</td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>加热一段</td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>加热二段</td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>均热段</td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </n-table>
        </n-scrollbar>
    </div>
</template>

<script setup lang="ts">
import { BilletData } from '@common/interfaces/tcData'

// define props
defineProps({
    // billet
    billet: {
        type: Object as () => BilletData,
        required: true,
    },
})
</script>

<style lang="scss" scoped>
// 注意: deep的对象必须在一个根节点才能使用
:deep(.n-scrollbar) {
    .n-scrollbar-container {
        max-height: calc(100vh - var(--excluding-size) - 520px);
        max-width: calc(100% - 16px);
    }
    .n-scrollbar-rail {
        --n-scrollbar-width: 8px;
        --n-scrollbar-height: 8px;
        --n-scrollbar-border-radius: 6px;
    }
}
</style>
