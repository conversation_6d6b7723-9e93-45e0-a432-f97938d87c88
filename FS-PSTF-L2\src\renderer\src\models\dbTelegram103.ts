import { executeQuery } from '@/models/dbUtils'

export interface Telegram103 {
    id: number | null

    stack_no: string // 堆垛号
    mat_no: string // 板坯号
    stack_pos: number // 堆垛位置
    shift_no: number // 班次
    shift_group: number // 班组
    record_time: Date // 创建时间

    // extra keys
    isEdit: boolean
    isDeletable: boolean
}

export interface Tele103FilterOptions {
    date: number | null
}
// extra keys that does not belong to table
//const extraKeys: (keyof Telegram103)[] = [
//    <keyof Telegram103>'isEdit',
//    <keyof Telegram103>'isDeletable',
//`]

const TableName = 'Tele103_Stack_Info'

// log function stub
async function fetchById(id: number): Promise<Telegram103 | undefined> {
    if (id > 0) {
        try {
            const result = await executeQuery(
                'SELECT * FROM ' + TableName + ' WHERE id = ? ',
                [id],
            )
            return result[0] as Telegram103
        } catch (err) {
            window.electron.ipcRenderer.invoke('add-log', 'error', err)
            return undefined
        }
    } else {
        window.electron.ipcRenderer.invoke(
            'add-log',
            'warn',
            'Fetch Telegram103 by id, but id is 0.',
        )

        return undefined
    }
}

// fetch all Telegram103s, and their quotes
async function fetch(
    pageStart: number,
    pageSize: number,
    order: string,
    filter: Tele103FilterOptions,
): Promise<Telegram103[]> {
    try {
        let query = 'SELECT * FROM ' + TableName + ' WHERE 1=1'
        const params: (string | number)[] = []

        // 2023-01-01 = 1672502400000
        if (typeof filter?.date === 'number' && filter.date >= 1672502400000) {
            query += ` AND DATEDIFF(day, [record_time], '${new Date(
                filter.date,
            ).toLocaleDateString('zh-CN')}')=0`
        }

        query += ` ORDER BY id ${order} OFFSET ? ROWS FETCH NEXT ? ROWS ONLY`
        params.push(pageStart, pageSize)

        const telegrams = await executeQuery(query, params)
        return telegrams as Telegram103[]
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return []
    }
}

// get total record count
async function count(filter?: Tele103FilterOptions): Promise<number> {
    try {
        let query = 'SELECT COUNT(*) as count FROM ' + TableName + ' WHERE 1=1'
        const params: string[] = []

        // 2023-01-01 = 1672502400000
        if (typeof filter?.date === 'number' && filter.date >= 1672502400000) {
            query += ` AND DATEDIFF(day, [record_time], '${new Date(
                filter.date,
            ).toLocaleDateString('zh-CN')}')=0`
        }

        const result = await executeQuery(query, params)
        return result[0].count as number
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// delete a Telegram103
async function del(id: number): Promise<number> {
    try {
        const result = await executeQuery(
            'DELETE FROM ' + TableName + ' WHERE id = ?',
            [id],
        )
        return result.affectedRows
    } catch (err) {
        window.electron.ipcRenderer.invoke('add-log', 'error', err)
        return 0
    }
}

// export functions
export default {
    fetchById,
    fetch,
    count,
    del,
}
