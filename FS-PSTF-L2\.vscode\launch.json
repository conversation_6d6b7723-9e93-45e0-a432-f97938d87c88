{"version": "0.2.0", "configurations": [{"name": "Debug Main Process", "type": "node", "request": "launch", "cwd": "${workspaceRoot}", "runtimeExecutable": "${workspaceRoot}/node_modules/.bin/electron-vite", "windows": {"runtimeExecutable": "${workspaceRoot}/node_modules/.bin/electron-vite.cmd"}, "runtimeArgs": ["--sourcemap"], "env": {"REMOTE_DEBUGGING_PORT": "5173"}}, {"name": "Debug Renderer Process", "port": 5173, "type": "chrome", "request": "attach", "webRoot": "${workspaceFolder}/src/renderer", "presentation": {"hidden": true}}], "compounds": [{"name": "Debug All", "configurations": ["Debug Main Process", "Debug Renderer Process"], "presentation": {"order": 1}}]}