<template>
    <div
        id="sidebar"
        bordered
        collapse-mode="width"
        :collapsed-width="80"
        :width="80"
        :collapsed="true"
    >
        <n-menu
            :collapsed="true"
            :collapsed-width="80"
            :collapsed-icon-size="40"
            :options="menuOptions"
            @update:value="selectPage"
        />
    </div>
</template>

<script setup lang="ts">
import { h } from 'vue'
//import { storeToRefs } from 'pinia'
import { useApplicationStore } from '@/stores/application'
import type { MenuOption } from 'naive-ui'

const applicationStore = useApplicationStore()

const { selectPage } = applicationStore

//const { updateStatus } = storeToRefs(applicationStore)
//const { getSelected: selectedWorkspace } = storeToRefs(workspacesStore)

const menuOptions: MenuOption[] = [
    // {
    //     label: '加热炉',
    //     key: 'FURNACE',
    //     icon: renderIcon(FurnaceIcon),
    // },
    {
        label: '数据记录',
        key: 'DATALOGS',
        icon: () =>
            h('div', {
                class: 'i-material-symbols:library-books-outline',
            }),
    },
    {
        label: '设置',
        key: 'SETTINGS',
        icon: () =>
            h('div', {
                class: 'i-mdi:settings-outline',
            }),
    },
    {
        label: 'Logs',
        key: 'LOGS',
        icon: () =>
            h('div', {
                class: 'i-material-symbols:note-alt-outline',
            }),
    },
    {
        label: '关于',
        key: 'INFO',
        icon: () =>
            h('div', {
                class: 'i-material-symbols:info-outline',
            }),
    },
]
</script>

<style lang="scss" scoped>
#sidebar {
    box-sizing: border-box;
    display: flex;
    margin: 0;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
    flex-direction: column;
    border-right: solid 1px #dedede;
}

:deep(.n-menu .n-menu-item) {
    height: 64px;
}
</style>
