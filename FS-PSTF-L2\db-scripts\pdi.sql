USE [FSPSTF-L2]
GO

/****** Object:  Table [dbo].[PDI] ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[PDI](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[prg_no] [char](20) NULL,
	[seq_no] [smallint] NOT NULL,
	[mat_no] [char](20) NULL,
	[steel_grade] [char](20) NULL,
	[slab_width] [decimal](8, 2) NOT NULL,
	[slab_thick] [decimal](8, 2) NOT NULL,
	[slab_length] [decimal](8, 2) NOT NULL,
	[slab_weight] [decimal](8, 2) NOT NULL,
	[shift_no] tinyint NOT NULL,
	[shift_group] tinyint NOT NULL,
    [used] tinyint NOT NULL,
	[record_time] [datetime] NULL,
 CONSTRAINT [PK_PDI] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[PDI] ADD  CONSTRAINT [DF_PDI_record_time]  DEFAULT (getdate()) FOR [record_time]
GO


