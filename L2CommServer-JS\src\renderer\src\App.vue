<template>
    <n-config-provider
        :locale="zhCN"
        :date-locale="dateZhCN"
        :theme-overrides="naiveThemeOverrides">
        <n-message-provider
            placement="top"
            :duration="5000">
            <div class="flex flex-col h-100vh w-100% overflow-hidden">
                <header class="h-36 w-100%">
                    <TheTitle />
                </header>
                <div class="flex flex-1 overflow-y-hidden">
                    <aside class="w-80 bg-gray-100">
                        <TheSideBar />
                    </aside>
                    <main class="flex-1 overflow-y-auto">
                        <keep-alive>
                            <WsLogs v-if="selectedPage === 'LOGS'" />
                        </keep-alive>
                        <WsInfo v-if="selectedPage === 'INFO'" />
                    </main>
                </div>
                <footer class="h-32 w-100% flex bg-gray-100">
                    <TheFooter />
                </footer>
            </div>
        </n-message-provider>
    </n-config-provider>
</template>

<script setup lang="ts">
import { defineAsyncComponent, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import log from 'electron-log/renderer';
import { zhCN, dateZhCN } from 'naive-ui';
import { useApplicationStore } from '@/stores/application';

// application info
const applicationStore = useApplicationStore();
const { selectedPage, naiveThemeOverrides } = storeToRefs(applicationStore);

const TheTitle = defineAsyncComponent(() => import('@/layout/TheTitle.vue'));
const TheFooter = defineAsyncComponent(() => import('@/layout/TheFooter.vue'));
const TheSideBar = defineAsyncComponent(
    () => import('@/layout/TheSideBar.vue')
);
const WsInfo = defineAsyncComponent(() => import('@/layout/WsInfo.vue'));
const WsLogs = defineAsyncComponent(() => import('@/layout/WsLogs.vue'));

// !!! all async functions should be run in onMounted() !!!
onMounted(async () => {
    const isDev = await window.electron.ipcRenderer.invoke('check-is-dev');

    // collect all unhandled errors/rejections
    log.errorHandler.startCatching({
        showDialog: isDev,
    });

    // db
    let appPath = (
        await window.electron.ipcRenderer.invoke('get-app-path')
    ).replace(/\\/g, '/');
    window.electron.ipcRenderer.invoke(
        'add-log',
        'debug',
        'Application path from renderer:' + appPath
    );

    window.electron.ipcRenderer.invoke('add-log', 'verbose', 'App mounted.');
});
</script>
