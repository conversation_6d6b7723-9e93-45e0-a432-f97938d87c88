import fs from 'fs';
import path from 'path';
import EventEmitter from 'events';
import log from 'electron-log';
import chokidar from 'chokidar';

/**
 * Plugin Manager - handles dynamic loading and hot-reloading of telegram plugins
 */
class PluginManager extends EventEmitter {
    constructor() {
        super();
        this.plugins = new Map(); // telegramNo -> plugin instance
        this.pluginFiles = new Map(); // filePath -> { telegramNo, lastModified, pluginName }
        this.watchers = new Map(); // directory -> chokidar watcher
        this.pluginDirectories = [];
        this.isWatching = false;

        // Default plugin directory (relative to main process)
        this.defaultPluginDir = path.resolve(
            __dirname,
            '../../../../TelegramProcessor-JS/plugins'
        );
    }

    /**
     * Initialize plugin manager and start watching for changes
     * @param {Array} additionalDirs - Additional plugin directories to watch
     */
    async initialize(additionalDirs = []) {
        try {
            // Add default plugin directory
            this.pluginDirectories = [this.defaultPluginDir, ...additionalDirs];

            // Create default directories if they don't exist
            await this.ensureDirectoriesExist();

            // Load all plugins
            await this.loadAllPlugins();

            // Start watching for changes
            this.startWatching();

            log.info('Plugin Manager initialized successfully');
            this.emit('initialized');
        } catch (error) {
            log.error('Failed to initialize Plugin Manager:', error);
            throw error;
        }
    }

    /**
     * Ensure plugin directories exist
     */
    async ensureDirectoriesExist() {
        for (const dir of this.pluginDirectories) {
            try {
                await fs.promises.mkdir(dir, { recursive: true });
            } catch (error) {
                log.warn(`Failed to create plugin directory ${dir}:`, error);
            }
        }
    }

    /**
     * Load all plugins from all directories
     */
    async loadAllPlugins() {
        for (const dir of this.pluginDirectories) {
            await this.loadPluginsFromDirectory(dir);
        }

        log.info(`Loaded ${this.plugins.size} plugins`);
    }

    /**
     * Load plugins from a specific directory
     * @param {string} directory - Plugin directory path
     */
    async loadPluginsFromDirectory(directory) {
        try {
            if (fs.existsSync(directory)) {
                const files = await fs.promises.readdir(directory);
                const jsFiles = files.filter((file) => file.endsWith('.js'));

                for (const file of jsFiles) {
                    const filePath = path.join(directory, file);
                    await this.loadPlugin(filePath);
                }
            }
        } catch (error) {
            log.error(
                `Failed to load plugins from directory ${directory}:`,
                error
            );
        }
    }

    /**
     * Load a single plugin file
     * @param {string} filePath - Plugin file path
     */
    async loadPlugin(filePath) {
        try {
            // Clear require cache for hot reloading
            delete require.cache[require.resolve(filePath)];

            // Load the plugin module
            const pluginModule = require(filePath);

            // Validate plugin export
            if (!pluginModule || !pluginModule.PluginClass) {
                log.warn(`Plugin ${filePath} does not export PluginClass`);
                return null;
            }

            // Create plugin instance
            const pluginInstance = new pluginModule.PluginClass();

            // Validate plugin
            const validationErrors = pluginInstance.validate();
            if (validationErrors.length > 0) {
                log.error(
                    `Plugin ${filePath} validation failed:`,
                    validationErrors
                );
                return null;
            }

            // Check for telegram number conflicts
            const telegramNo = pluginInstance.telegramNo;
            const existingPlugin = this.plugins.get(telegramNo);

            if (existingPlugin) {
                log.warn(
                    `Plugin conflict: Telegram ${telegramNo} already handled by ${existingPlugin.pluginName}`
                );
            }

            // Store plugin
            this.plugins.set(telegramNo, pluginInstance);

            // Track file info
            const stats = await fs.promises.stat(filePath);
            this.pluginFiles.set(filePath, {
                telegramNo,
                lastModified: stats.mtime,
                pluginName: pluginInstance.pluginName,
            });

            const pluginType = pluginInstance.getPluginType();
            log.info(
                `Loaded ${pluginType} plugin: ${pluginInstance.pluginName} (Telegram ${telegramNo})`
            );

            this.emit('pluginLoaded', {
                type: pluginType,
                telegramNo,
                pluginName: pluginInstance.pluginName,
                filePath,
            });

            return pluginInstance;
        } catch (error) {
            log.error(`Failed to load plugin ${filePath}:`, error);
            this.emit('pluginError', {
                filePath,
                error: error.message,
            });
            return null;
        }
    }

    /**
     * Start watching plugin directories for changes
     */
    startWatching() {
        if (this.isWatching) {
            return;
        }

        for (const dir of this.pluginDirectories) {
            if (fs.existsSync(dir)) {
                const watcher = chokidar.watch(path.join(dir, '*.js'), {
                    ignored: /node_modules/,
                    persistent: true,
                    ignoreInitial: true,
                });

                watcher.on('change', (filePath) => {
                    this.handleFileChange(filePath);
                });

                watcher.on('add', (filePath) => {
                    this.handleFileAdd(filePath);
                });

                watcher.on('unlink', (filePath) => {
                    this.handleFileRemove(filePath);
                });

                this.watchers.set(dir, watcher);
                log.info(`Started watching plugin directory: ${dir}`);
            }
        }

        this.isWatching = true;
    }

    /**
     * Stop watching plugin directories
     */
    stopWatching() {
        for (const [dir, watcher] of this.watchers) {
            watcher.close();
            log.info(`Stopped watching plugin directory: ${dir}`);
        }

        this.watchers.clear();
        this.isWatching = false;
    }

    /**
     * Handle file change event
     * @param {string} filePath - Changed file path
     */
    async handleFileChange(filePath) {
        log.info(`Plugin file changed: ${filePath}`);

        const fileInfo = this.pluginFiles.get(filePath);
        if (fileInfo) {
            // Remove old plugin
            this.plugins.delete(fileInfo.telegramNo);
        }

        // Reload plugin
        await this.loadPlugin(filePath);

        this.emit('pluginReloaded', { filePath });
    }

    /**
     * Handle file add event
     * @param {string} filePath - Added file path
     */
    async handleFileAdd(filePath) {
        log.info(`New plugin file added: ${filePath}`);

        // Load new plugin
        await this.loadPlugin(filePath);
    }

    /**
     * Handle file remove event
     * @param {string} filePath - Removed file path
     */
    handleFileRemove(filePath) {
        log.info(`Plugin file removed: ${filePath}`);

        const fileInfo = this.pluginFiles.get(filePath);
        if (fileInfo) {
            // Remove plugin
            this.plugins.delete(fileInfo.telegramNo);
            this.pluginFiles.delete(filePath);

            this.emit('pluginUnloaded', {
                filePath,
                telegramNo: fileInfo.telegramNo,
            });
        }
    }

    /**
     * Get plugin for telegram number
     * @param {number} telegramNo - Telegram number
     * @returns {Object|null} - Plugin instance or null
     */
    getPlugin(telegramNo) {
        return this.plugins.get(telegramNo) || null;
    }

    /**
     * Get receive plugin for telegram number (backward compatibility)
     * @param {number} telegramNo - Telegram number
     * @returns {Object|null} - Plugin instance or null
     */
    getReceivePlugin(telegramNo) {
        const plugin = this.plugins.get(telegramNo);
        if (
            plugin &&
            (plugin.getPluginType() === 'receive' ||
                plugin.getPluginType() === 'both')
        ) {
            return plugin;
        }
        return null;
    }

    /**
     * Get send plugin for telegram number (backward compatibility)
     * @param {number} telegramNo - Telegram number
     * @returns {Object|null} - Plugin instance or null
     */
    getSendPlugin(telegramNo) {
        const plugin = this.plugins.get(telegramNo);
        if (
            plugin &&
            (plugin.getPluginType() === 'send' ||
                plugin.getPluginType() === 'both')
        ) {
            return plugin;
        }
        return null;
    }

    /**
     * Get all loaded plugins
     * @returns {Object} - Object with plugin lists organized by type
     */
    getAllPlugins() {
        const allPlugins = Array.from(this.plugins.entries()).map(
            ([telNo, plugin]) => ({
                telegramNo: telNo,
                pluginName: plugin.pluginName,
                version: plugin.version,
                description: plugin.description,
                author: plugin.author,
                type: plugin.getPluginType(),
            })
        );

        const receiveList = allPlugins.filter(
            (p) => p.type === 'receive' || p.type === 'both'
        );
        const sendList = allPlugins.filter(
            (p) => p.type === 'send' || p.type === 'both'
        );

        return {
            all: allPlugins,
            receive: receiveList,
            send: sendList,
            total: allPlugins.length,
        };
    }

    /**
     * Reload all plugins
     */
    async reloadAllPlugins() {
        log.info('Reloading all plugins...');

        // Clear all plugins
        this.plugins.clear();
        this.pluginFiles.clear();

        // Reload all plugins
        await this.loadAllPlugins();

        this.emit('allPluginsReloaded');
        log.info('All plugins reloaded successfully');
    }

    /**
     * Get plugin statistics
     * @returns {Object} - Plugin statistics
     */
    getStatistics() {
        const allPlugins = this.getAllPlugins();
        return {
            totalPlugins: this.plugins.size,
            receivePlugins: allPlugins.receive.length,
            sendPlugins: allPlugins.send.length,
            bothPlugins: allPlugins.all.filter((p) => p.type === 'both').length,
            watchedDirectories: this.pluginDirectories.length,
            isWatching: this.isWatching,
        };
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        this.stopWatching();
        this.plugins.clear();
        this.pluginFiles.clear();
        this.removeAllListeners();
    }
}

export { PluginManager };
