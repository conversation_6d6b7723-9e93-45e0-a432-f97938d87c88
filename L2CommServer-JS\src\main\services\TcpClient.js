import net from 'net';
import EventEmitter from 'events';
import log from 'electron-log';

/**
 * TCP Client class - for active connections to remote servers
 */
class TcpClientService extends EventEmitter {
    constructor(address, port) {
        super();
        this.address = address;
        this.port = port;
        this.socket = null;
        this.isConnected = false;
        this.shouldStop = false;
        this.reconnectInterval = 5000; // 5 seconds
        this.reconnectTimer = null;
        this.id = this.generateClientId();
        this.connectionTime = null;
        this.autoReconnect = true;
    }

    generateClientId() {
        return `client_${Math.random().toString(36).substr(2, 9)}`;
    }

    async connect() {
        if (this.isConnected || this.shouldStop) {
            return;
        }

        return new Promise((resolve, reject) => {
            this.socket = new net.Socket();

            // Set up socket event handlers
            this.socket.on('connect', () => {
                this.onConnected();
                resolve();
            });

            this.socket.on('data', (data) => {
                this.onReceived(data);
            });

            this.socket.on('error', (error) => {
                this.onError(error);
                reject(error);
            });

            this.socket.on('close', () => {
                this.onDisconnected();
            });

            this.socket.on('timeout', () => {
                log.warn(`TCP client ${this.id} connection timed out`);
                this.socket.destroy();
            });

            // Set connection timeout
            this.socket.setTimeout(10000); // 10 seconds

            // Attempt connection
            this.socket.connect(this.port, this.address);
        });
    }

    async disconnect() {
        this.shouldStop = true;
        this.autoReconnect = false;

        // Clear reconnect timer
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        if (this.socket && !this.socket.destroyed) {
            this.socket.destroy();
        }

        // Wait for disconnection
        while (this.isConnected) {
            await new Promise((resolve) => setTimeout(resolve, 10));
        }
    }

    onConnected() {
        this.isConnected = true;
        this.connectionTime = new Date();
        this.socket.setTimeout(0); // Remove timeout after successful connection

        log.info(
            `TCP client ${this.id} connected to ${this.address}:${this.port}`
        );

        this.emit('connected', {
            clientId: this.id,
            address: this.address,
            port: this.port,
            connectionTime: this.connectionTime,
        });
    }

    onReceived(buffer) {
        try {
            // Process received telegram asynchronously
            setImmediate(() => {
                this.processReceivedData(buffer);
            });
        } catch (error) {
            log.error(
                `Error processing received data in client ${this.id}:`,
                error
            );
        }
    }

    async processReceivedData(buffer) {
        try {
            // Import telegram processor dynamically to avoid circular dependencies
            const { TelegramProcessor } = require('./TelegramProcessor');
            const processor = new TelegramProcessor();

            // Process the received telegram
            const processedCount = await processor.processReceived(
                buffer,
                0,
                buffer.length,
                this.address
            );

            // Emit telegram count event
            this.emit('telegramProcessed', {
                type: 'received',
                count: processedCount,
                clientId: this.id,
            });
        } catch (error) {
            log.error(
                `Error in processReceivedData for client ${this.id}:`,
                error
            );
        }
    }

    async sendAsync(data) {
        if (!this.isConnected || !this.socket || this.socket.destroyed) {
            throw new Error(`TCP client ${this.id} is not connected`);
        }

        return new Promise((resolve, reject) => {
            this.socket.write(data, (error) => {
                if (error) {
                    reject(error);
                } else {
                    // Emit telegram sent event
                    this.emit('telegramProcessed', {
                        type: 'sent',
                        count: 1,
                        clientId: this.id,
                    });
                    resolve();
                }
            });
        });
    }

    onSendRequest(telegramData) {
        this.sendAsync(telegramData)
            .then(() => {
                log.debug(`Telegram sent from client ${this.id}`);
            })
            .catch((error) => {
                log.error(
                    `Failed to send telegram from client ${this.id}:`,
                    error
                );
            });
    }

    onError(error) {
        log.error(`TCP client ${this.id} error:`, error);

        this.emit('error', {
            clientId: this.id,
            error: error,
            address: this.address,
            port: this.port,
        });

        // Don't attempt reconnection on certain errors
        if (error.code === 'ECONNREFUSED' || error.code === 'EHOSTUNREACH') {
            this.scheduleReconnect();
        }
    }

    onDisconnected() {
        const wasConnected = this.isConnected;
        this.isConnected = false;
        this.connectionTime = null;

        if (wasConnected) {
            log.info(
                `TCP client ${this.id} disconnected from ${this.address}:${this.port}`
            );

            this.emit('disconnected', {
                clientId: this.id,
                address: this.address,
                port: this.port,
            });
        }

        // Schedule reconnection if not stopping and auto-reconnect is enabled
        if (!this.shouldStop && this.autoReconnect) {
            this.scheduleReconnect();
        }
    }

    scheduleReconnect() {
        if (this.reconnectTimer || this.shouldStop || !this.autoReconnect) {
            return;
        }

        log.info(
            `TCP client ${this.id} will attempt reconnection in ${this.reconnectInterval}ms`
        );

        this.reconnectTimer = setTimeout(() => {
            this.reconnectTimer = null;
            if (!this.shouldStop && !this.isConnected) {
                this.connect().catch((error) => {
                    log.error(
                        `TCP client ${this.id} reconnection failed:`,
                        error
                    );
                });
            }
        }, this.reconnectInterval);
    }

    getStatus() {
        return {
            id: this.id,
            address: this.address,
            port: this.port,
            isConnected: this.isConnected,
            connectionTime: this.connectionTime,
            autoReconnect: this.autoReconnect,
        };
    }

    setAutoReconnect(enabled) {
        this.autoReconnect = enabled;
        if (!enabled && this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }

    setReconnectInterval(interval) {
        this.reconnectInterval = interval;
    }
}

/**
 * TCP Client Manager - manages multiple client connections
 */
class TcpClientManager extends EventEmitter {
    constructor() {
        super();
        this.clients = new Map();
        this.statistics = {
            totalConnections: 0,
            activeConnections: 0,
            telegramsReceived: 0,
            telegramsSent: 0,
        };
    }

    addClient(address, port, autoConnect = true) {
        const clientId = `${address}:${port}`;

        if (this.clients.has(clientId)) {
            throw new Error(`Client for ${address}:${port} already exists`);
        }

        const client = new TcpClientService(address, port);

        // Set up event handlers
        client.on('connected', (data) => {
            this.statistics.activeConnections++;
            this.emit('clientConnected', data);
        });

        client.on('disconnected', (data) => {
            this.statistics.activeConnections--;
            this.emit('clientDisconnected', data);
        });

        client.on('error', (data) => {
            this.emit('clientError', data);
        });

        client.on('telegramProcessed', (data) => {
            if (data.type === 'received') {
                this.statistics.telegramsReceived += data.count;
            } else if (data.type === 'sent') {
                this.statistics.telegramsSent += data.count;
            }
            this.emit('telegramProcessed', data);
        });

        this.clients.set(clientId, client);
        this.statistics.totalConnections++;

        if (autoConnect) {
            client.connect().catch((error) => {
                log.error(`Failed to connect client ${clientId}:`, error);
            });
        }

        return client;
    }

    removeClient(address, port) {
        const clientId = `${address}:${port}`;
        const client = this.clients.get(clientId);

        if (client) {
            client.disconnect();
            this.clients.delete(clientId);
            this.statistics.totalConnections--;
        }
    }

    getClient(address, port) {
        const clientId = `${address}:${port}`;
        return this.clients.get(clientId);
    }

    getAllClients() {
        return Array.from(this.clients.values());
    }

    async disconnectAll() {
        const disconnectPromises = Array.from(this.clients.values()).map(
            (client) => client.disconnect()
        );

        await Promise.all(disconnectPromises);
        this.clients.clear();
        this.statistics.totalConnections = 0;
        this.statistics.activeConnections = 0;
    }

    getStatistics() {
        return {
            ...this.statistics,
            activeConnections: Array.from(this.clients.values()).filter(
                (client) => client.isConnected
            ).length,
        };
    }

    sendToClient(address, port, data) {
        const client = this.getClient(address, port);
        if (client && client.isConnected) {
            return client.sendAsync(data);
        } else {
            throw new Error(`Client ${address}:${port} is not connected`);
        }
    }

    sendToAllClients(data) {
        const sendPromises = Array.from(this.clients.values())
            .filter((client) => client.isConnected)
            .map((client) => client.sendAsync(data));

        return Promise.allSettled(sendPromises);
    }
}

export { TcpClientService, TcpClientManager };
