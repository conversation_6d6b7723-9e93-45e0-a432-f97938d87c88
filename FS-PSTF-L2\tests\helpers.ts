import { Page, _electron as electron } from 'playwright'
import { ElectronApplication } from 'playwright-core'
import { expect } from '@playwright/test'

// delay function
function delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms))
}

export interface L2App {
    appWindow: Page
    electronApp: ElectronApplication
}

async function launchApp(): Promise<L2App> {
    // a new db file will be created automatically when app launches
    const app = await electron.launch({ args: ['out/main/index.js'] })
    // get first window the app opens
    const page = await app.firstWindow()
    // Direct Electron console to Node terminal.
    page.on('console', console.log)

    await page.waitForEvent('load')
    // wait
    await this.delay(500)

    return {
        appWindow: page,
        electronApp: app,
    } as L2App
}

// fill form
async function fillForm(window: Page, fillValue) {
    for (const field of fillValue) {
        if (field.role === 'textbox') {
            await window
                .getByTestId(field.testid)
                .getByRole('textbox')
                .first()
                .fill(field.value.toString())
        }
        if (field.role === 'radio') {
            await window
                .getByTestId(field.testid)
                .locator('label')
                .filter({ hasText: field.label })
                .click()
        }
    }
}

// verify form
async function verifyForm(window: Page, fillValue) {
    for (const field of fillValue) {
        if (field.role === 'textbox') {
            expect(
                await window
                    .getByTestId(field.testid)
                    .getByRole('textbox')
                    .first()
                    .inputValue(),
                `${field.testid} should have value ${field.value.toString()}`,
            ).toEqual(field.value.toString())
        }
        if (field.role === 'radio') {
            await expect(
                window
                    .getByTestId(field.testid)
                    .locator('label')
                    .filter({ hasText: field.label }),
                `${field.testid} ${field.value} should be checked`,
            ).toBeChecked()
        }
        if (field.role === 'text') {
            await expect(
                window.getByTestId(field.testid),
                `${field.testid} should have text ${field.value.toString()}`,
            ).toHaveText(field.value.toString())
        }
    }
}

export default {
    delay,
    launchApp,
    fillForm,
    verifyForm,
}
