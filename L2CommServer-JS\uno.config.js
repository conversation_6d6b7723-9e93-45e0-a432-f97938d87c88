import { defineConfig } from 'unocss'

export default defineConfig({
  // UnoCSS configuration for L2CommServer-JS
  theme: {
    colors: {
      primary: '#3498db',
      secondary: '#2c3e50',
      success: '#27ae60',
      warning: '#f39c12',
      danger: '#e74c3c',
      info: '#3498db',
      light: '#ecf0f1',
      dark: '#2c3e50'
    }
  },
  shortcuts: {
    // Common button styles
    'btn': 'px-4 py-2 rounded cursor-pointer border-none font-medium transition-colors',
    'btn-primary': 'btn bg-primary text-white hover:bg-blue-600',
    'btn-secondary': 'btn bg-secondary text-white hover:bg-gray-700',
    'btn-success': 'btn bg-success text-white hover:bg-green-600',
    'btn-warning': 'btn bg-warning text-white hover:bg-yellow-600',
    'btn-danger': 'btn bg-danger text-white hover:bg-red-600',
    
    // Card styles
    'card': 'bg-white rounded-lg shadow-md p-6 mb-4',
    'card-header': 'border-b border-gray-200 pb-4 mb-4',
    'card-title': 'text-xl font-semibold text-gray-800',
    
    // Layout helpers
    'flex-center': 'flex items-center justify-center',
    'flex-between': 'flex items-center justify-between',
    
    // Status indicators
    'status-running': 'bg-success',
    'status-stopped': 'bg-danger',
    'status-warning': 'bg-warning',
    
    // Log levels
    'log-error': 'text-danger',
    'log-warn': 'text-warning',
    'log-info': 'text-info',
    'log-debug': 'text-gray-500'
  }
})
