import { contextBridge } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import Store from 'electron-store'
import knexAPI from 'knex'

// Custom APIs for renderer
const api = {}

// 创建一个函数来获取或创建 Store 实例
function getStore(name: string) {
    return new Store({ name })
}
// 创建一个安全的 store API
const safeStoreAPI = {
    get: (storeName: string, key: string, defaultValue: any) =>
        getStore(storeName).get(key, defaultValue),
    set: (storeName: string, key: string, value: any) =>
        getStore(storeName).set(key, value),
    delete: (storeName: string, key: string) => getStore(storeName).delete(key),

    // 根据需要添加其他方法
}

// Use `contextBridge` APIs to expose Electron APIs to renderer
try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
    contextBridge.exposeInMainWorld('store', safeStoreAPI)
    contextBridge.exposeInMainWorld('knex', knexAPI)
} catch (error) {
    console.error(error)
}
