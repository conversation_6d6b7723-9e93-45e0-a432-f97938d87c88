import { app, shell, BrowserWindow, nativeImage } from 'electron'
import path, { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import Store from 'electron-store'
import log from 'electron-log'
import ipcHandlers from './ipc-handlers'
import icon from '@images/logo-1024.png'

Store.initRenderer()

const isDevelopment = !app.isPackaged
const isMacOS = process.platform === 'darwin'
const isLinux = process.platform === 'linux'
const isWindows = process.platform === 'win32'
const gotTheLock = app.requestSingleInstanceLock()

//process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

function createWindow(): BrowserWindow {
    // Create the browser window.
    const mainWindow = new BrowserWindow({
        width: 1440,
        height: 850,
        minWidth: 1024,
        minHeight: 768,
        show: !isWindows,
        // if there's a <title> tag in index.html loaded byloadURL()
        // this setting will be overrided
        // so make sure to check index.html
        title: '抚顺特钢实林加热炉 L2',
        icon: nativeImage.createFromDataURL(icon),
        webPreferences: {
            preload: join(__dirname, '../preload/index.js'),
            sandbox: false,
        },
        autoHideMenuBar: true,
        titleBarStyle: isLinux ? 'default' : 'hidden',
        titleBarOverlay: isWindows
            ? {
                  color: '#f3f4f6',
                  symbolColor: '#333',
                  height: 36,
              }
            : false,
        trafficLightPosition: isMacOS ? { x: 10, y: 8 } : undefined,
        backgroundColor: '#fff',
    })

    mainWindow.on('ready-to-show', () => {
        mainWindow.show()
    })

    mainWindow.webContents.setWindowOpenHandler((details) => {
        shell.openExternal(details.url)
        return { action: 'deny' }
    })

    // HMR for renderer base on electron-vite cli.
    // Load the remote URL for development or the local html file for production.
    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
        mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
    } else {
        mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
    }

    // electron-log
    log.initialize()
    log.transports.file.resolvePathFn = () =>
        path.join(app.getAppPath(), 'pstfl2.log')
    log.transports.ipc.level = 'info'
    log.transports.file.level = 'debug'
    log.transports.console.level = 'info'

    log.info('App is ready')

    return mainWindow
}

let mainWindow: BrowserWindow
if (!gotTheLock) app.quit()
else {
    // This method will be called when Electron has finished
    // initialization and is ready to create browser windows.
    // Some APIs can only be used after this event occurs.
    app.whenReady().then(() => {
        // Set app user model id for windows
        electronApp.setAppUserModelId('ca.cetus')

        // Default open or close DevTools by F12 in development
        // and ignore CommandOrControl + R in production.
        // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
        app.on('browser-window-created', (_, window) => {
            optimizer.watchWindowShortcuts(window)
        })

        // set dock image for macos
        if (isMacOS && app.dock) {
            app.dock.setIcon(nativeImage.createFromDataURL(icon))
        }

        mainWindow = createWindow()
        // Initialize ipcHandlers
        ipcHandlers(mainWindow)

        if (isDevelopment) {
            //mainWindow.webContents.openDevTools()
        }
    })

    // Quit when all windows are closed, except on macOS. There, it's common
    // for applications and their menu bar to stay active until the user quits
    // explicitly with Cmd + Q.
    app.on('window-all-closed', () => {
        if (process.platform !== 'darwin') {
            app.quit()
        }
    })

    app.on('activate', function () {
        // On macOS it's common to re-create a window in the app when the
        // dock icon is clicked and there are no other windows open.
        if (BrowserWindow.getAllWindows().length === 0) createWindow()
    })

    // In this file you can include the rest of your app"s specific main process
    // code. You can also put them in separate files and require them here.
}
