{
    "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
    "include": [
        "src/renderer/src/env.d.ts",
        "src/renderer/src/**/*",
        "src/renderer/src/**/*.vue",
        "src/preload/*.d.ts",
        "src/common/**/*",
    ],
    "compilerOptions": {
        "composite": true,
        "baseUrl": ".",
        "paths": {
            "@/*": ["src/renderer/src/*"],
            "@common/*": ["src/common/*"],
        }
    }
}
