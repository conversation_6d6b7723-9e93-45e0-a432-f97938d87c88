﻿
using System.Windows;
using Serilog;
using Serilog.Debugging;
using System.Diagnostics;
using System.Windows.Controls;
using System.Windows.Media;
using System;
using Telegrams;
using L2CommServer.Services;

namespace L2CommClient
{

    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private static readonly object _syncRoot = new object();

        private long _maxRichTextBoxLength = 100000;
        private bool AutoScroll;

        private TCPClient client;

        public ServerInfo serverInfo;
        public UICommands uiCommands;

        public MainWindow()
        {
            InitializeComponent();

            // init serverInfo
            serverInfo = new ServerInfo();
            ServerInfo.DataContext = serverInfo;
            ServerState.DataContext = serverInfo;

            // init UICommands instance
            uiCommands = new UICommands();
            // bind commands
            tbLogDebug.DataContext = uiCommands;

            // autoscroll log window
            LogRichTextBox.Loaded += (s, e) =>
            {
                var scrollViewer = VisualTreeHelper.GetChild(VisualTreeHelper.GetChild(LogRichTextBox, 0), 2) as ScrollViewer;
                scrollViewer.ScrollChanged += (scroller, eScroller) => ScrollViewer_ScrollChanged(scroller, eScroller);
            };

            SelfLog.Enable(message => Trace.WriteLine($"INTERNAL ERROR: {message}"));
            const string outputTemplate = "{Level:u4} {Timestamp:yyyy-MM-dd HH:mm:ss} [{Caller}] {Message:lj}{NewLine}{Exception}";

            // create logger
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.ControlledBy(uiCommands.LogLevelSwitch)
                .Enrich.WithCaller()
                .WriteTo.RichTextBox(LogRichTextBox,
                    theme: RichTextBoxSinkThemesWJ.Default,
                    outputTemplate: outputTemplate,
                    syncRoot: _syncRoot
                    )
                .CreateLogger();

            // Create a new TCP TCP client
            client = new TCPClient(serverInfo.IpAddress, serverInfo.Port);

            // bind event handler
            client.ConnectionChanged += UpdateConnection;
            // Connect the client
            Log.Information("Client connecting...");
            client.ConnectAsync();
            Log.Information("Done!");

        }


        /// <summary>
        /// Update Connection Count on main window
        /// </summary> 
        public void UpdateConnection(object sender, ConnEventArgs e)
        {
            if (e.OnConnect)
            {
                serverInfo.ServerStateIconColor = Brushes.Green;
                serverInfo.ServerStateIcon = "\xE8CE";  // connect
            }
            else
            {
                serverInfo.ServerStateIconColor = Brushes.Red;
                serverInfo.ServerStateIcon = "\xE8CD";  // disconnect
            }

        }


        private void btnSend_Click(object sender, RoutedEventArgs e)
        {
            ComposeSendMessage(201);
        }


        private void btnSend103_Click(object sender, RoutedEventArgs e)
        {
            ComposeSendMessage(103);
        }


        private void ComposeSendMessage(int telNo)
        {
            byte[] buffer = null;

            // prepare message
            ISendMessagePlugin plugin = ServiceLocator.Instance
                                                      .GetService<ISendMsgPluginService>()
                                                      .InitPlugin(telNo);
            if (plugin != null)
            {
                buffer = plugin.Compose();
            }
            else
            {
                Log.Error("Cannot find processor for Telegram No {0}", telNo);
            }

            if (buffer != null)
            {
                // send message
                client.SendAsync(buffer);
                // log
                Log.Information("Send telegram {0} to server.", telNo);
            }
            else
            {
                Log.Error("Failed to compose telegram {0}.", telNo);
            }
        }

        private void btnClearLog_Click(object sender, RoutedEventArgs e)
        {
            LogRichTextBox.SelectAll();
            LogRichTextBox.Selection.Text = string.Empty;
        }

        private void LogRichTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            LogRichTextBox.RemoveFirstLine(_maxRichTextBoxLength);
        }


        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            client.Dispose();
        }


        private void ScrollViewer_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            // User scroll event : set or unset autoscroll mode
            if (e.Source as ScrollViewer != null && e.ExtentHeightChange == 0)
            {   // Content unchanged : user scroll event
                if ((e.Source as ScrollViewer).VerticalOffset == (e.Source as ScrollViewer).ScrollableHeight)
                {   // Scroll bar is in bottom
                    // Set autoscroll mode
                    AutoScroll = true;
                }
                else
                {   // Scroll bar isn't in bottom
                    // Unset autoscroll mode
                    AutoScroll = false;
                }
            }

            // Content scroll event : autoscroll eventually
            if (AutoScroll && e.ExtentHeightChange != 0 && e.Source as ScrollViewer != null)
            {   // Content changed and autoscroll mode set
                // Autoscroll
                (e.Source as ScrollViewer).ScrollToVerticalOffset((e.Source as ScrollViewer).ExtentHeight);
            }
        }

    }

}
