import { ipc<PERSON>ain, BrowserWindow } from 'electron'
import Store from 'electron-store'
import { DbSettings } from '@common/interfaces/settings'
import { databaseService } from '../services/DatabaseService'
import log from 'electron-log'

const store = new Store({ name: 'settings' })

const defaultDbSettings: DbSettings = {
    serverAddress: '127.0.0.1',
    serverPort: 1433,
    database: '',
    username: 'sa',
    password: '',
}

function getDbSettings(): DbSettings {
    const storedSettings = store.get('db-settings') as Partial<DbSettings>
    return { ...defaultDbSettings, ...storedSettings }
}

export default (_mainWindow: BrowserWindow) => {
    // 初始化数据库
    const initializeDatabase = async () => {
        const dbSettings = getDbSettings()
        await databaseService.initialize(dbSettings)
    }

    // 在主进程启动时初始化数据库
    initializeDatabase().catch((error) => {
        log.error('数据库初始化失败:', error)
    })

    ipcMain.handle('get-connection-status', () => {
        return databaseService.getConnectionStatus()
    })

    ipcMain.handle('close-database', async () => {
        await databaseService.close()
        return true
    })

    ipcMain.handle(
        'execute-query',
        async (_event, query: string, params: any[]) => {
            log.debug('Execute-query', query, params)
            const knex = databaseService.getKnex()
            const upQuery = query.toUpperCase()
            const operation = upQuery.trim().split(' ')[0]

            switch (operation) {
                case 'SELECT':
                    return await knex.raw(query, params)
                case 'UPDATE':
                    // for update, cannot determin how many params belong to where clause
                    return await knex.raw(query, params)
                case 'INSERT':
                    const tableName = query.match(/INSERT INTO (\w+)/i)?.[1]
                    if (tableName) {
                        const setClause = query.split(tableName)[1]
                        const result = await knex(tableName).insert(
                            knex.raw(setClause, params),
                        )
                        return { affectedRows: result[0] }
                    }
                    break
                case 'DELETE':
                    const deleteMatch = query.match(/DELETE FROM (\w+)/i)
                    if (deleteMatch) {
                        const tableName = deleteMatch[1]
                        const whereClause = query.split('WHERE')[1]
                        const result = await knex(tableName)
                            .where(knex.raw(whereClause, params))
                            .del()
                        return { affectedRows: result }
                    }
                    break
                default:
                    return await knex.raw(query, params)
            }

            throw new Error('Invalid query')
        },
    )

    // db settings
    ipcMain.handle('get-db-settings', () => {
        return getDbSettings()
    })

    ipcMain.handle('set-db-settings', (_event, settings: DbSettings) => {
        store.set('db-settings', settings)
        // 重新初始化数据库
        initializeDatabase().catch((error) => {
            log.error('数据库重新初始化失败:', error)
        })
        return true // 添加返回值
    })
}
