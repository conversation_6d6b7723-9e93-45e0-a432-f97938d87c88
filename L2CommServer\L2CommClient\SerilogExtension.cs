﻿using System.Collections.Generic;
using System.Diagnostics;
using System.Windows.Controls;
using System.Windows.Documents;
using Serilog;
using Serilog.Configuration;
using Serilog.Core;
using Serilog.Events;
using Serilog.Sinks.RichTextBox.Themes;

namespace L2CommClient
{

    #region Custom Theme
    /// <summary>
    /// define custom theme
    /// </summary>
    public static class RichTextBoxSinkThemesWJ
    {
        public static RichTextBoxConsoleTheme Default { get; } = new RichTextBoxConsoleTheme
        (
            new Dictionary<RichTextBoxThemeStyle, RichTextBoxConsoleThemeStyle>
            {
                [RichTextBoxThemeStyle.Text] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.Black },
                [RichTextBoxThemeStyle.SecondaryText] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkGray },
                [RichTextBoxThemeStyle.TertiaryText] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkGray },
                [RichTextBoxThemeStyle.Invalid] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkYellow },
                [RichTextBoxThemeStyle.Null] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkBlue },
                [RichTextBoxThemeStyle.Name] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkGray },
                [RichTextBoxThemeStyle.String] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkCyan },
                [RichTextBoxThemeStyle.Number] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.Magenta },
                [RichTextBoxThemeStyle.Boolean] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkBlue },
                [RichTextBoxThemeStyle.Scalar] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkGreen },
                [RichTextBoxThemeStyle.LevelVerbose] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkGray, Background = ConsoleHtmlColor.White },
                [RichTextBoxThemeStyle.LevelDebug] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.Gray, Background = ConsoleHtmlColor.DarkYellow },
                [RichTextBoxThemeStyle.LevelInformation] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.Gray, Background = ConsoleHtmlColor.Blue },
                [RichTextBoxThemeStyle.LevelWarning] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.DarkGray, Background = ConsoleHtmlColor.Yellow },
                [RichTextBoxThemeStyle.LevelError] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.Gray, Background = ConsoleHtmlColor.DarkRed },
                [RichTextBoxThemeStyle.LevelFatal] = new RichTextBoxConsoleThemeStyle { Foreground = ConsoleHtmlColor.Gray, Background = ConsoleHtmlColor.Red },
            }
        );
    }

    internal static class ConsoleHtmlColor
    {
        private static readonly object _syncLock = new object();

        static ConsoleHtmlColor()
        {
            lock (_syncLock)
            {
                Black = "#000000";
                DarkBlue = "#000080";
                DarkGreen = "#008000";
                DarkCyan = "#008080";
                DarkRed = "#800000";
                DarkMagenta = "#800080";
                DarkYellow = "#808000";
                Gray = "#c0c0c0";
                DarkGray = "#808080";
                Blue = "#0000ff";
                Green = "#00ff00";
                Cyan = "#00ffff";
                Red = "#ff0000";
                Magenta = "#ff00ff";
                Yellow = "#ffff00";
                White = "#ffffff";
            }
        }

        public static string Black { get; }
        public static string DarkBlue { get; }
        public static string DarkGreen { get; }
        public static string DarkCyan { get; }
        public static string DarkRed { get; }
        public static string DarkMagenta { get; }
        public static string DarkYellow { get; }
        public static string Gray { get; }
        public static string DarkGray { get; }
        public static string Blue { get; }
        public static string Green { get; }
        public static string Cyan { get; }
        public static string Red { get; }
        public static string Magenta { get; }
        public static string Yellow { get; }
        public static string White { get; }
    }
    #endregion

    #region SeriLog extension
    class CallerEnricher : ILogEventEnricher
    {
        public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
        {
            var skip = 3;
            while (true)
            {
                var stack = new StackFrame(skip);
                if (!stack.HasMethod())
                {
                    logEvent.AddPropertyIfAbsent(new LogEventProperty("Caller", new ScalarValue("<unknown method>")));
                    return;
                }

                var method = stack.GetMethod();
                if (method.DeclaringType.Assembly != typeof(Log).Assembly)
                {
                    //var caller = $"{method.DeclaringType.FullName}.{method.Name}({string.Join(", ", method.GetParameters().Select(pi => pi.ParameterType.FullName))})";
                    var caller = $"{method.DeclaringType.FullName}";
                    logEvent.AddPropertyIfAbsent(new LogEventProperty("Caller", new ScalarValue(caller)));
                    return;
                }

                skip++;
            }
        }
    }

    static class LoggerCallerEnrichmentConfiguration
    {
        public static LoggerConfiguration WithCaller(this LoggerEnrichmentConfiguration enrichmentConfiguration)
        {
            return enrichmentConfiguration.With<CallerEnricher>();
        }
    }
    #endregion

    #region RichTextbox function
    public static class RichTextBoxExtensions
    {
        public static string GetText(this RichTextBox richTextBox)
        {
            return new TextRange(richTextBox.Document.ContentStart, richTextBox.Document.ContentEnd).Text.Replace("\r\n", string.Empty);
        }

        public static void RemoveFirstLine(this RichTextBox richTextBox, long maxTextLength)
        {

            var start = richTextBox.Document.ContentStart;
            var endPos = richTextBox.Document.ContentEnd;
            string text = new TextRange(start, endPos).Text;

            if (text.Length > maxTextLength)
            {

                long count = 1;
                int position = 0;
                while ((position = text.IndexOf('\n', position)) != -1)
                {
                    count++;
                    position++;         // Skip this occurance!
                    if (count > 100)
                    {
                        break;
                    }
                }
                text = null;
                position++;
                endPos = GetPoint(start, position);
                richTextBox.Selection.Select(start, endPos);
                richTextBox.Selection.Text = string.Empty;
            }


        }

        private static TextPointer GetPoint(TextPointer start, int x)
        {
            var ret = start;
            var i = 0;
            while (i < x && ret != null)
            {
                if (ret.GetPointerContext(LogicalDirection.Backward) == TextPointerContext.Text || ret.GetPointerContext(LogicalDirection.Backward) == TextPointerContext.None)
                    i++;
                if (ret.GetPositionAtOffset(1, LogicalDirection.Forward) == null)
                    return ret;
                ret = ret.GetPositionAtOffset(1, LogicalDirection.Forward);
            }
            return ret;
        }
    }
    #endregion
}