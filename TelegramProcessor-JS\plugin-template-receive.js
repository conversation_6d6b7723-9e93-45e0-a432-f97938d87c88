const {
    ReceivePluginBase,
    exportPlugin,
    S7DataTypes,
} = require('../plugin-base');

/**
 * Template for creating receive plugins
 * Copy this file and modify it for your specific telegram processing needs
 */
class TeleReceiveTemplate extends ReceivePluginBase {
    constructor() {
        super();

        // REQUIRED: Set these properties for your plugin
        this.telegramNo = 999; // Change to your telegram number
        this.pluginName = 'TeleReceiveTemplate'; // Change to your plugin name
        this.version = '1.0.0';
        this.description = 'Template for receive plugins';
        this.author = 'Your Name';
        this.messageLength = 0; // Set expected message length (0 = variable)

        // Add your plugin-specific properties here
        // Example:
        // this.value1 = 0;
        // this.value2 = 0.0;
        // this.status = false;
    }

    /**
     * Parse message body
     * REQUIRED: Implement this method to parse your telegram format
     * @param {Buffer} body - Message body
     * @returns {boolean} - True if parsing was successful
     */
    parse(body) {
        try {
            // Check message length if you have a fixed format
            if (this.messageLength > 0 && body.length < this.messageLength) {
                console.warn(
                    `[${this.pluginName}] Invalid message length. Expected ${this.messageLength}, got ${body.length}`
                );
                return false;
            }

            // Parse your message format here
            // Example for parsing different data types:

            // Parse 16-bit integer at offset 0
            // this.value1 = DataTypes.bufferToInt(body.slice(0, 2));

            // Parse 32-bit float at offset 2
            // this.value2 = DataTypes.bufferToReal(body.slice(2, 6));

            // Parse boolean at offset 6, bit 0
            // this.status = DataTypes.bufferToBool(body.slice(6, 7), 0);

            // Add your parsing logic here
            console.log(
                `[${this.pluginName}] Parsing telegram ${this.telegramNo} - implement your logic here`
            );

            return true;
        } catch (error) {
            console.error(`[${this.pluginName}] Parse error:`, error);
            return false;
        }
    }

    /**
     * Process the parsed message
     * REQUIRED: Implement this method to handle your business logic
     * @param {Buffer} body - Message body
     */
    async process(body) {
        try {
            console.log(
                `[${this.pluginName}] Start processing telegram ${this.telegramNo}`
            );

            // Add your processing logic here
            // Examples of what you can do:

            // 1. Validate data ranges
            // if (this.value1 < 0 || this.value1 > 100) {
            //     console.warn(`[${this.pluginName}] Value1 out of range: ${this.value1}`);
            // }

            // 2. Send response telegrams
            // if (this.status) {
            //     await this.sendResponseTelegram();
            // }

            // 3. Update system state
            // this.updateSystemState();

            // 4. Log important events
            // console.log(`[${this.pluginName}] Processed values: ${this.value1}, ${this.value2}`);

            console.log(
                `[${this.pluginName}] Process telegram ${this.telegramNo} finished`
            );
        } catch (error) {
            console.error(`[${this.pluginName}] Process error:`, error);
        }
    }

    /**
     * Debug output for the plugin
     * OPTIONAL: Override this method to provide debug information
     */
    debugOutput() {
        console.log(`[${this.pluginName}] Debug Info:`);
        // Add your debug output here
        // Example:
        // console.log(`  Value1: ${this.value1}`);
        // console.log(`  Value2: ${this.value2.toFixed(2)}`);
        // console.log(`  Status: ${this.status}`);
    }

    /**
     * Save data to database
     * OPTIONAL: Override this method if you need custom database operations
     * @returns {number} - Number of affected rows
     */
    async saveToDb() {
        try {
            // Implement your database save logic here
            console.log(`[${this.pluginName}] Saving data to database`);

            // Example database operation (replace with actual implementation):
            // const result = await database.insert('your_table', {
            //     telegram_no: this.telegramNo,
            //     value1: this.value1,
            //     value2: this.value2,
            //     status: this.status,
            //     timestamp: new Date()
            // });
            // return result.affectedRows;

            return 1; // Return number of affected rows
        } catch (error) {
            console.error(`[${this.pluginName}] Database save error:`, error);
            return 0;
        }
    }

    /**
     * Example method for sending response telegrams
     * OPTIONAL: Add helper methods as needed
     */
    async sendResponseTelegram() {
        try {
            // Compose response telegram
            const responseBody = Buffer.alloc(8);

            // Example: Echo back the received values
            // DataTypes.intToBuffer(this.value1).copy(responseBody, 0);
            // DataTypes.realToBuffer(this.value2).copy(responseBody, 2);

            // Create complete telegram with header (response telegram number: original + 100)
            const responseTelegramNo = this.telegramNo + 100;
            const responseTelegram = this.composeResponseTelegram(
                responseTelegramNo,
                responseBody
            );

            // Send the response
            this.sendRequest(responseTelegram);

            console.log(
                `[${this.pluginName}] Response telegram ${responseTelegramNo} sent`
            );
        } catch (error) {
            console.error(
                `[${this.pluginName}] Failed to send response telegram:`,
                error
            );
        }
    }

    /**
     * Helper method to compose response telegram with header
     * UTILITY: You can use this method to create response telegrams
     */
    composeResponseTelegram(responseTelemNo, responseBody) {
        const headerLength = 20;
        const totalLength = headerLength + responseBody.length;
        const telegram = Buffer.alloc(totalLength);

        // Write header
        telegram.writeUInt16LE(responseTelemNo, 0); // Telegram number
        telegram.writeUInt16LE(totalLength, 2); // Telegram length
        telegram.write('L2'.padEnd(2, '\0'), 4, 2, 'ascii'); // Sender ID
        telegram.write('PL'.padEnd(2, '\0'), 6, 2, 'ascii'); // Receiver ID

        // Write timestamp (8 bytes)
        const now = Date.now();
        telegram.writeBigUInt64LE(BigInt(now), 8);

        telegram.writeUInt16LE(0, 16); // Telegram counter
        telegram.writeUInt16LE(0, 18); // Reserved

        // Write message body
        responseBody.copy(telegram, headerLength);

        return telegram;
    }
}

// Export the plugin
module.exports = exportPlugin(TeleReceiveTemplate);
