<template>
    <n-flex
        vertical
        size="small"
        class="wh-full"
        style="height: calc(100vh - 70px)"
    >
        <suspense>
            <template #default>
                <n-card :bordered="false" style="height: calc(100vh - 120px)">
                    <n-scrollbar style="height: 100%">
                        <n-log
                            :lines="formattedLogRows"
                            :log-level="logLevel"
                            :loading="loading"
                            :line-height="1.5"
                            :rows="30"
                            trim
                            language="html"
                            style="border: 1px solid #e0e0e0; padding: 10px"
                        />
                    </n-scrollbar>
                </n-card>
            </template>
            <template #fallback>
                <div>加载中...</div>
            </template>
        </suspense>
        <n-space align="center" class="h-50">
            <span class="vertical-align-middle ml-40">选择日志级别</span>
            <n-select
                v-model:value="logLevel"
                :options="logLevelOptions"
                class="min-w-120 vertical-align-middle"
                @update:value="handleLogLevelChange"
            />
            <n-button
                @click="clearLogs"
                type="warning"
                style="vertical-align: middle; margin-left: 30px"
            >
                清除日志
            </n-button>
        </n-space>
    </n-flex>
</template>

<script setup lang="ts">
import {
    ref,
    computed,
    onMounted,
    onUnmounted,
    onActivated,
    nextTick,
} from 'vue'
import { NButton, NSpace, useMessage } from 'naive-ui'
import { LevelOption } from 'electron-log'
import eventBus from '@common/libs/eventBus'

interface LogEntry {
    level: LevelOption
    content: string
    date: Date
}

const logRows = ref<LogEntry[]>([])
const loading = ref(false)
const logLevel = ref<LevelOption>('info')
const logLevelOptions = [
    { label: 'Error', value: 'error' },
    { label: 'Warn', value: 'warn' },
    { label: 'Info', value: 'info' },
    { label: 'Verbose', value: 'verbose' },
    { label: 'Debug', value: 'debug' },
]

const message = useMessage()

// Enhanced color scheme with background colors for badges
const logLevelStyles = {
    error: {
        color: '#e74c3c',
        backgroundColor: '#e74c3c15',
        borderColor: '#e74c3c',
    },
    warn: {
        color: '#f39c12',
        backgroundColor: '#f39c1215',
        borderColor: '#f39c12',
    },
    info: {
        color: '#3498db',
        backgroundColor: '#3498db15',
        borderColor: '#3498db',
    },
    verbose: {
        color: '#27ae60',
        backgroundColor: '#27ae6015',
        borderColor: '#27ae60',
    },
    debug: {
        color: '#9b59b6',
        backgroundColor: '#9b59b615',
        borderColor: '#9b59b6',
    },
}

const formattedLogRows = computed(() => {
    return logRows.value.map((entry) => {
        const levelUpper = (entry.level as string).toUpperCase()
        const levelStyle = logLevelStyles[
            entry.level as keyof typeof logLevelStyles
        ] || {
            color: '#333333',
            backgroundColor: '#33333315',
            borderColor: '#333333',
        }
        const timestamp = entry.date.toISOString()

        // Enhanced formatting with better color coding using logLevelStyles
        const levelBadge = `<span style="color: ${levelStyle.color}; font-weight: bold; background-color: ${levelStyle.backgroundColor}; padding: 3px 8px; border-radius: 4px; margin-right: 10px; border: 1px solid ${levelStyle.borderColor}30;">[${levelUpper}]</span>`
        const timestampSpan = `<span style="color: #444">${timestamp}</span>`

        // Content color and styling based on log level
        let contentColor = '#333333'
        let contentStyle = ''

        if (entry.level === 'error') {
            contentColor = '#c0392b'
            contentStyle = 'font-weight: 600;'
        } else if (entry.level === 'warn') {
            contentColor = '#d68910'
            contentStyle = 'font-weight: 500;'
        } else if (entry.level === 'info') {
            contentColor = '#2980b9'
        } else if (entry.level === 'verbose') {
            contentColor = '#229954'
        } else if (entry.level === 'debug') {
            contentColor = '#8e44ad'
            contentStyle = 'font-style: italic;'
        }

        const contentSpan = `<span style="color: ${contentColor}; ${contentStyle}">${entry.content}</span>`

        return `<div style="display: flex; align-items: center; padding: 2px 0;">${levelBadge} ${timestampSpan} <span style="margin: 0 8px;">-</span> ${contentSpan}</div>`
    })
})

const addLogEntry = (level: LevelOption, ...args: any[]) => {
    const newEntry: LogEntry = {
        level,
        content: args
            .map((arg) =>
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg),
            )
            .join(' '),
        date: new Date(),
    }
    logRows.value.push(newEntry)
}

const initLogLevel = async () => {
    try {
        const currentLevel = (await window.electron.ipcRenderer.invoke(
            'get-log-level',
        )) as LevelOption
        if (logLevelOptions.some((option) => option.value === currentLevel)) {
            logLevel.value = currentLevel
        } else {
            console.warn(`未知的日志级别: ${currentLevel}，使用默认值 'info'`)
            logLevel.value = 'info'
        }
    } catch (error) {
        console.error('获取日志级别失败:', error)
        logLevel.value = 'info'
    }
}

const handleLogLevelChange = async (newLevel: LevelOption) => {
    try {
        await window.electron.ipcRenderer.invoke('set-log-level', newLevel)
        addLogEntry('info', `日志级别更改为: ${newLevel}`)
    } catch (error) {
        console.error('设置日志级别失败:', error)
    }
}

const clearLogs = () => {
    logRows.value = []
    message.success('日志已清除')
    addLogEntry('info', '日志已清除')
}

onMounted(async () => {
    await nextTick()
    // 初始化操作，只会在组件第一次创建时执行
    await initLogLevel()

    window.electron.ipcRenderer.on(
        '__ELECTRON_LOG_IPC__',
        (_, message: any) => {
            if (typeof message === 'object' && message.level && message.data) {
                addLogEntry(message.level as LevelOption, ...message.data)
            } else if (typeof message === 'string') {
                addLogEntry('info', message)
            } else {
                console.warn('收到未知格式的日志消息:', message)
            }
        },
    )

    await window.electron.ipcRenderer.invoke(
        'add-log',
        'verbose',
        '日志页面已加载',
    )
})

onActivated(() => {
    eventBus.emit('update-window-title', '日志')
})

onUnmounted(() => {
    // 组件被真正销毁时执行（例如，应用关闭时）
    window.electron.ipcRenderer.removeAllListeners('main-process-log')
})
</script>
