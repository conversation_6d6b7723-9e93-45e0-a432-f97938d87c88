const {
    ReceivePluginBase,
    exportPlugin,
    S7DataTypes,
} = require('../plugin-base');

/**
 * TeleReceive101 - Distance measurement telegram processor
 * Processes telegram 101 containing distance measurement data
 */
class TeleReceive101 extends ReceivePluginBase {
    constructor() {
        super();
        this.telegramNo = 101;
        this.pluginName = 'TeleReceive101';
        this.version = '1.0.0';
        this.description =
            'Process telegram 101 - Distance measurement from sensor';
        this.author = 'L2CommServer Team';
        this.messageLength = 4; // Expected message length: 4 bytes for distance (Real)

        // Plugin-specific properties
        this.distance = 0.0;
        this.lastUpdateTime = null;
        this.distanceThreshold = 100.0; // Alert threshold
    }

    /**
     * Parse message body
     * @param {Buffer} body - Message body (4 bytes containing distance as Real)
     * @returns {boolean} - True if parsing was successful
     */
    parse(body) {
        try {
            if (body.length >= this.messageLength) {
                // Parse distance value from first 4 bytes (Real/Float32)
                this.distance = S7DataTypes.bufferToReal(body.subarray(0, 4));
                this.lastUpdateTime = new Date();

                // Validate distance value
                if (isNaN(this.distance) || this.distance < 0) {
                    console.warn(
                        `[${this.pluginName}] Invalid distance value: ${this.distance}`
                    );
                    return false;
                }

                return true;
            } else {
                console.warn(
                    `[${this.pluginName}] Invalid message length. Expected ${this.messageLength}, got ${body.length}`
                );
                return false;
            }
        } catch (error) {
            console.error(`[${this.pluginName}] Parse error:`, error);
            return false;
        }
    }

    /**
     * Process the parsed message
     * @param {Buffer} body - Message body
     */
    async process(body) {
        try {
            console.log(
                `[${
                    this.pluginName
                }] Processing distance measurement: ${this.distance.toFixed(
                    2
                )} units`
            );

            // Check if distance exceeds threshold
            if (this.distance > this.distanceThreshold) {
                console.warn(
                    `[${this.pluginName}] Distance ${this.distance.toFixed(
                        2
                    )} exceeds threshold ${this.distanceThreshold}`
                );

                // Send alert telegram (example: telegram 201)
                await this.sendAlertTelegram();
            }

            // Additional processing logic can be added here:
            // - Store in database
            // - Update system state
            // - Trigger other actions

            console.log(
                `[${this.pluginName}] Distance processing completed successfully`
            );
        } catch (error) {
            console.error(`[${this.pluginName}] Process error:`, error);
        }
    }

    /**
     * Send alert telegram when distance threshold is exceeded
     */
    async sendAlertTelegram() {
        try {
            // Compose alert telegram (example format)
            const alertBody = Buffer.alloc(8);

            // Alert code (4 bytes) - 1001 = distance alert
            S7DataTypes.dIntToBuffer(1001).copy(alertBody, 0);

            // Distance value (4 bytes)
            S7DataTypes.realToBuffer(this.distance).copy(alertBody, 4);

            // Create complete telegram with header
            const alertTelegram = this.composeResponseTelegram(201, alertBody);

            // Send the alert telegram
            this.sendRequest(alertTelegram);

            console.log(
                `[${
                    this.pluginName
                }] Alert telegram 201 sent for distance ${this.distance.toFixed(
                    2
                )}`
            );
        } catch (error) {
            console.error(
                `[${this.pluginName}] Failed to send alert telegram:`,
                error
            );
        }
    }

    /**
     * Compose response telegram with header
     * @param {number} responseTelemNo - Response telegram number
     * @param {Buffer} responseBody - Response message body
     * @returns {Buffer} - Complete telegram with header
     */
    composeResponseTelegram(responseTelemNo, responseBody) {
        const headerLength = 20;
        const totalLength = headerLength + responseBody.length;
        const telegram = Buffer.alloc(totalLength);

        // Write header
        telegram.writeUInt16LE(responseTelemNo, 0); // Telegram number
        telegram.writeUInt16LE(totalLength, 2); // Telegram length
        telegram.write('L2'.padEnd(2, '\0'), 4, 2, 'ascii'); // Sender ID
        telegram.write('PL'.padEnd(2, '\0'), 6, 2, 'ascii'); // Receiver ID

        // Write timestamp (8 bytes)
        const now = Date.now();
        telegram.writeBigUInt64LE(BigInt(now), 8);

        telegram.writeUInt16LE(0, 16); // Telegram counter
        telegram.writeUInt16LE(0, 18); // Reserved

        // Write message body
        responseBody.copy(telegram, headerLength);

        return telegram;
    }

    /**
     * Debug output for the plugin
     */
    debugOutput() {
        const timestamp = this.lastUpdateTime
            ? this.lastUpdateTime.toISOString()
            : 'Never';
        console.log(`[${this.pluginName}] Debug Info:`);
        console.log(`  Distance: ${this.distance.toFixed(2)} units`);
        console.log(`  Last Update: ${timestamp}`);
        console.log(`  Threshold: ${this.distanceThreshold}`);
        console.log(
            `  Status: ${
                this.distance > this.distanceThreshold ? 'ALERT' : 'NORMAL'
            }`
        );
    }

    /**
     * Save data to database (override default implementation)
     * @returns {number} - Number of affected rows
     */
    async saveToDb() {
        try {
            // This would typically save to a database
            // For now, just log the operation
            console.log(
                `[${this.pluginName}] Saving distance ${this.distance} to database`
            );

            // Simulate database operation
            return 1;
        } catch (error) {
            console.error(`[${this.pluginName}] Database save error:`, error);
            return 0;
        }
    }

    /**
     * Get current distance value
     * @returns {number} - Current distance
     */
    getDistance() {
        return this.distance;
    }

    /**
     * Set distance threshold
     * @param {number} threshold - New threshold value
     */
    setDistanceThreshold(threshold) {
        this.distanceThreshold = threshold;
        console.log(
            `[${this.pluginName}] Distance threshold set to ${threshold}`
        );
    }

    /**
     * Get plugin status information
     * @returns {Object} - Status information
     */
    getStatus() {
        return {
            distance: this.distance,
            lastUpdate: this.lastUpdateTime,
            threshold: this.distanceThreshold,
            isAlert: this.distance > this.distanceThreshold,
        };
    }
}

// Export the plugin
module.exports = exportPlugin(TeleReceive101);
