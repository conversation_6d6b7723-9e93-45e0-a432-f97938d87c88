USE [FSPSTF-L2]
GO

/****** Object:  Table [dbo].[Tele121_Fnc_Temp]]    Script Date: 2024/9/7 16:46:21 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Tele121_Fnc_Temp](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[z1_temp_sp] [decimal](10,2) NOT NULL,
	[z2_temp_sp] [decimal](10,2) NOT NULL,
	[z3_temp_sp] [decimal](10,2) NOT NULL,
	[z4_temp_sp] [decimal](10,2) NOT NULL,
	[z5_temp_sp] [decimal](10,2) NOT NULL,
	[z6_temp_sp] [decimal](10,2) NOT NULL,
	[z1_temp_av] [decimal](10,2) NOT NULL,
	[z2_temp_av] [decimal](10,2) NOT NULL,
	[z3_temp_av] [decimal](10,2) NOT NULL,
	[z4_temp_av] [decimal](10,2) NOT NULL,
	[z5_temp_av] [decimal](10,2) NOT NULL,
	[z6_temp_av] [decimal](10,2) NOT NULL,
	[ng_consumption] [decimal](10,2) NOT NULL,
	[record_time] [datetime] NOT NULL,
 CONSTRAINT [PK_Tele121_Fnc_Temp] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Tele121_Fnc_Temp] ADD  CONSTRAINT [DF_Tele121_Fnc_Temp_create_time]  DEFAULT (getdate()) FOR [record_time]
GO


