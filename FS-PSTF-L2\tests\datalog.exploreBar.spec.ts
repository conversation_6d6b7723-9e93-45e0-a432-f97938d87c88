import * as fs from 'fs'
import { Page } from 'playwright'
import { ElectronApplication } from 'playwright-core'
import { test, expect } from '@playwright/test'
import helpers from './helpers'
import { mainMenu } from './constants'

let appWindow: Page
let electronApp: ElectronApplication

test.beforeAll(async () => {
    // a new db file will be created automatically when app launches
    // launch app
    const app = await helpers.launchApp()
    appWindow = app.appWindow
    electronApp = app.electronApp
})

// check datalogs page
test('open datalogs page', async () => {
    await appWindow
        .getByRole('menuitem', { name: mainMenu.Datalog.label })
        .click()
    // page header
    await expect(
        appWindow.locator('.workspace-explorebar-title'),
        'expect page heading hasa 数据记录 ',
    ).toHaveText(mainMenu.Datalog.label)
})

// check menu
test('Default should have 2 nodes', async () => {
    // has one node
    await expect(
        appWindow.locator('.n-tree-node-wrapper'),
        'expect only 2 node',
    ).toHaveCount(2)
    // node lable should be Cetus
    await expect(
        appWindow.getByText('报文历史'),
        '1 node label should be 报文历史',
    ).toHaveCount(1)
})

test.afterAll(async () => {
    // await helpers.delay(10000)
    await electronApp.close()
})
