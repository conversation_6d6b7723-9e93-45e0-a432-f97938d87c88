{"runtimeTarget": {"name": ".NETCoreApp,Version=v5.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v5.0": {"Dapper/2.0.123": {"runtime": {"lib/net5.0/Dapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.123.33578"}}}, "FontAwesome6.Fonts/1.0.1": {"runtime": {"lib/net5.0-windows7.0/FontAwesome6.Core.dll": {"assemblyVersion": "1.0.1.0", "fileVersion": "1.0.1.0"}, "lib/net5.0-windows7.0/FontAwesome6.Fonts.Net.dll": {"assemblyVersion": "1.0.1.0", "fileVersion": "1.0.1.0"}, "lib/net5.0-windows7.0/FontAwesome6.Shared.Net.dll": {"assemblyVersion": "1.0.1.0", "fileVersion": "1.0.1.0"}}}, "HandyControl/3.3.0": {"runtime": {"lib/net5.0/HandyControl.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "Microsoft.Composition/1.0.31": {"dependencies": {"System.Composition": "1.0.31"}}, "Microsoft.Data.Sqlite/6.0.4": {"dependencies": {"Microsoft.Data.Sqlite.Core": "6.0.4", "SQLitePCLRaw.bundle_e_sqlite3": "2.0.6"}}, "Microsoft.Data.Sqlite.Core/6.0.4": {"dependencies": {"SQLitePCLRaw.core": "2.0.6"}, "runtime": {"lib/netstandard2.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "6.0.4.0", "fileVersion": "6.0.422.16106"}}}, "Microsoft.NETCore.Platforms/3.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Windows.SDK.Contracts/10.0.18362.2005": {"dependencies": {"System.Runtime.WindowsRuntime": "4.6.0", "System.Runtime.WindowsRuntime.UI.Xaml": "4.6.0"}}, "ModernWpfUI/0.9.4": {"dependencies": {"Microsoft.Windows.SDK.Contracts": "10.0.18362.2005"}, "runtime": {"lib/netcoreapp3.0/ModernWpf.Controls.dll": {"assemblyVersion": "0.9.4.0", "fileVersion": "0.9.4.0"}, "lib/netcoreapp3.0/ModernWpf.dll": {"assemblyVersion": "0.9.4.0", "fileVersion": "0.9.4.0"}}, "resources": {"lib/netcoreapp3.0/af-ZA/ModernWpf.Controls.resources.dll": {"locale": "af-ZA"}, "lib/netcoreapp3.0/af-ZA/ModernWpf.resources.dll": {"locale": "af-ZA"}, "lib/netcoreapp3.0/am-ET/ModernWpf.Controls.resources.dll": {"locale": "am-ET"}, "lib/netcoreapp3.0/am-ET/ModernWpf.resources.dll": {"locale": "am-ET"}, "lib/netcoreapp3.0/ar-SA/ModernWpf.Controls.resources.dll": {"locale": "ar-SA"}, "lib/netcoreapp3.0/ar-SA/ModernWpf.resources.dll": {"locale": "ar-SA"}, "lib/netcoreapp3.0/az-Latn-AZ/ModernWpf.Controls.resources.dll": {"locale": "az-Latn-AZ"}, "lib/netcoreapp3.0/az-Latn-AZ/ModernWpf.resources.dll": {"locale": "az-Latn-AZ"}, "lib/netcoreapp3.0/be-BY/ModernWpf.Controls.resources.dll": {"locale": "be-BY"}, "lib/netcoreapp3.0/be-BY/ModernWpf.resources.dll": {"locale": "be-BY"}, "lib/netcoreapp3.0/bg-BG/ModernWpf.Controls.resources.dll": {"locale": "bg-BG"}, "lib/netcoreapp3.0/bg-BG/ModernWpf.resources.dll": {"locale": "bg-BG"}, "lib/netcoreapp3.0/bn-BD/ModernWpf.Controls.resources.dll": {"locale": "bn-BD"}, "lib/netcoreapp3.0/bn-BD/ModernWpf.resources.dll": {"locale": "bn-BD"}, "lib/netcoreapp3.0/bs-Latn-BA/ModernWpf.Controls.resources.dll": {"locale": "bs-Latn-BA"}, "lib/netcoreapp3.0/bs-Latn-BA/ModernWpf.resources.dll": {"locale": "bs-Latn-BA"}, "lib/netcoreapp3.0/ca-ES/ModernWpf.Controls.resources.dll": {"locale": "ca-ES"}, "lib/netcoreapp3.0/ca-ES/ModernWpf.resources.dll": {"locale": "ca-ES"}, "lib/netcoreapp3.0/cs-CZ/ModernWpf.Controls.resources.dll": {"locale": "cs-CZ"}, "lib/netcoreapp3.0/cs-CZ/ModernWpf.resources.dll": {"locale": "cs-CZ"}, "lib/netcoreapp3.0/da-DK/ModernWpf.Controls.resources.dll": {"locale": "da-DK"}, "lib/netcoreapp3.0/da-DK/ModernWpf.resources.dll": {"locale": "da-DK"}, "lib/netcoreapp3.0/de-DE/ModernWpf.Controls.resources.dll": {"locale": "de-DE"}, "lib/netcoreapp3.0/de-DE/ModernWpf.resources.dll": {"locale": "de-DE"}, "lib/netcoreapp3.0/el-GR/ModernWpf.Controls.resources.dll": {"locale": "el-GR"}, "lib/netcoreapp3.0/el-GR/ModernWpf.resources.dll": {"locale": "el-GR"}, "lib/netcoreapp3.0/en-GB/ModernWpf.Controls.resources.dll": {"locale": "en-GB"}, "lib/netcoreapp3.0/en-GB/ModernWpf.resources.dll": {"locale": "en-GB"}, "lib/netcoreapp3.0/es-ES/ModernWpf.Controls.resources.dll": {"locale": "es-ES"}, "lib/netcoreapp3.0/es-ES/ModernWpf.resources.dll": {"locale": "es-ES"}, "lib/netcoreapp3.0/es-MX/ModernWpf.Controls.resources.dll": {"locale": "es-MX"}, "lib/netcoreapp3.0/es-MX/ModernWpf.resources.dll": {"locale": "es-MX"}, "lib/netcoreapp3.0/et-EE/ModernWpf.Controls.resources.dll": {"locale": "et-EE"}, "lib/netcoreapp3.0/et-EE/ModernWpf.resources.dll": {"locale": "et-EE"}, "lib/netcoreapp3.0/eu-ES/ModernWpf.Controls.resources.dll": {"locale": "eu-ES"}, "lib/netcoreapp3.0/eu-ES/ModernWpf.resources.dll": {"locale": "eu-ES"}, "lib/netcoreapp3.0/fa-IR/ModernWpf.Controls.resources.dll": {"locale": "fa-IR"}, "lib/netcoreapp3.0/fa-IR/ModernWpf.resources.dll": {"locale": "fa-IR"}, "lib/netcoreapp3.0/fi-FI/ModernWpf.Controls.resources.dll": {"locale": "fi-FI"}, "lib/netcoreapp3.0/fi-FI/ModernWpf.resources.dll": {"locale": "fi-FI"}, "lib/netcoreapp3.0/fr-CA/ModernWpf.Controls.resources.dll": {"locale": "fr-CA"}, "lib/netcoreapp3.0/fr-CA/ModernWpf.resources.dll": {"locale": "fr-CA"}, "lib/netcoreapp3.0/fr-FR/ModernWpf.Controls.resources.dll": {"locale": "fr-FR"}, "lib/netcoreapp3.0/fr-FR/ModernWpf.resources.dll": {"locale": "fr-FR"}, "lib/netcoreapp3.0/gl-ES/ModernWpf.Controls.resources.dll": {"locale": "gl-ES"}, "lib/netcoreapp3.0/gl-ES/ModernWpf.resources.dll": {"locale": "gl-ES"}, "lib/netcoreapp3.0/ha-Latn-NG/ModernWpf.Controls.resources.dll": {"locale": "ha-Latn-NG"}, "lib/netcoreapp3.0/ha-Latn-NG/ModernWpf.resources.dll": {"locale": "ha-Latn-NG"}, "lib/netcoreapp3.0/he-IL/ModernWpf.Controls.resources.dll": {"locale": "he-IL"}, "lib/netcoreapp3.0/he-IL/ModernWpf.resources.dll": {"locale": "he-IL"}, "lib/netcoreapp3.0/hi-IN/ModernWpf.Controls.resources.dll": {"locale": "hi-IN"}, "lib/netcoreapp3.0/hi-IN/ModernWpf.resources.dll": {"locale": "hi-IN"}, "lib/netcoreapp3.0/hr-HR/ModernWpf.Controls.resources.dll": {"locale": "hr-HR"}, "lib/netcoreapp3.0/hr-HR/ModernWpf.resources.dll": {"locale": "hr-HR"}, "lib/netcoreapp3.0/hu-HU/ModernWpf.Controls.resources.dll": {"locale": "hu-HU"}, "lib/netcoreapp3.0/hu-HU/ModernWpf.resources.dll": {"locale": "hu-HU"}, "lib/netcoreapp3.0/id-ID/ModernWpf.Controls.resources.dll": {"locale": "id-ID"}, "lib/netcoreapp3.0/id-ID/ModernWpf.resources.dll": {"locale": "id-ID"}, "lib/netcoreapp3.0/is-IS/ModernWpf.Controls.resources.dll": {"locale": "is-IS"}, "lib/netcoreapp3.0/is-IS/ModernWpf.resources.dll": {"locale": "is-IS"}, "lib/netcoreapp3.0/it-IT/ModernWpf.Controls.resources.dll": {"locale": "it-IT"}, "lib/netcoreapp3.0/it-IT/ModernWpf.resources.dll": {"locale": "it-IT"}, "lib/netcoreapp3.0/ja-JP/ModernWpf.Controls.resources.dll": {"locale": "ja-<PERSON>"}, "lib/netcoreapp3.0/ja-JP/ModernWpf.resources.dll": {"locale": "ja-<PERSON>"}, "lib/netcoreapp3.0/ka-GE/ModernWpf.Controls.resources.dll": {"locale": "ka-GE"}, "lib/netcoreapp3.0/ka-GE/ModernWpf.resources.dll": {"locale": "ka-GE"}, "lib/netcoreapp3.0/kk-KZ/ModernWpf.Controls.resources.dll": {"locale": "kk-KZ"}, "lib/netcoreapp3.0/kk-KZ/ModernWpf.resources.dll": {"locale": "kk-KZ"}, "lib/netcoreapp3.0/km-KH/ModernWpf.Controls.resources.dll": {"locale": "km-KH"}, "lib/netcoreapp3.0/km-KH/ModernWpf.resources.dll": {"locale": "km-KH"}, "lib/netcoreapp3.0/kn-IN/ModernWpf.Controls.resources.dll": {"locale": "kn-IN"}, "lib/netcoreapp3.0/kn-IN/ModernWpf.resources.dll": {"locale": "kn-IN"}, "lib/netcoreapp3.0/ko-KR/ModernWpf.Controls.resources.dll": {"locale": "ko-KR"}, "lib/netcoreapp3.0/ko-KR/ModernWpf.resources.dll": {"locale": "ko-KR"}, "lib/netcoreapp3.0/lo-LA/ModernWpf.Controls.resources.dll": {"locale": "lo-LA"}, "lib/netcoreapp3.0/lo-LA/ModernWpf.resources.dll": {"locale": "lo-LA"}, "lib/netcoreapp3.0/lt-LT/ModernWpf.Controls.resources.dll": {"locale": "lt-LT"}, "lib/netcoreapp3.0/lt-LT/ModernWpf.resources.dll": {"locale": "lt-LT"}, "lib/netcoreapp3.0/lv-LV/ModernWpf.Controls.resources.dll": {"locale": "lv-LV"}, "lib/netcoreapp3.0/lv-LV/ModernWpf.resources.dll": {"locale": "lv-LV"}, "lib/netcoreapp3.0/mk-MK/ModernWpf.Controls.resources.dll": {"locale": "mk-MK"}, "lib/netcoreapp3.0/mk-MK/ModernWpf.resources.dll": {"locale": "mk-MK"}, "lib/netcoreapp3.0/ml-IN/ModernWpf.Controls.resources.dll": {"locale": "ml-IN"}, "lib/netcoreapp3.0/ml-IN/ModernWpf.resources.dll": {"locale": "ml-IN"}, "lib/netcoreapp3.0/ms-MY/ModernWpf.Controls.resources.dll": {"locale": "ms-MY"}, "lib/netcoreapp3.0/ms-MY/ModernWpf.resources.dll": {"locale": "ms-MY"}, "lib/netcoreapp3.0/nb-NO/ModernWpf.Controls.resources.dll": {"locale": "nb-NO"}, "lib/netcoreapp3.0/nb-NO/ModernWpf.resources.dll": {"locale": "nb-NO"}, "lib/netcoreapp3.0/nl-NL/ModernWpf.Controls.resources.dll": {"locale": "nl-NL"}, "lib/netcoreapp3.0/nl-NL/ModernWpf.resources.dll": {"locale": "nl-NL"}, "lib/netcoreapp3.0/nn-NO/ModernWpf.Controls.resources.dll": {"locale": "nn-NO"}, "lib/netcoreapp3.0/nn-NO/ModernWpf.resources.dll": {"locale": "nn-NO"}, "lib/netcoreapp3.0/pl-PL/ModernWpf.Controls.resources.dll": {"locale": "pl-PL"}, "lib/netcoreapp3.0/pl-PL/ModernWpf.resources.dll": {"locale": "pl-PL"}, "lib/netcoreapp3.0/pt-BR/ModernWpf.Controls.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.0/pt-BR/ModernWpf.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.0/pt-PT/ModernWpf.Controls.resources.dll": {"locale": "pt-PT"}, "lib/netcoreapp3.0/pt-PT/ModernWpf.resources.dll": {"locale": "pt-PT"}, "lib/netcoreapp3.0/ro-RO/ModernWpf.Controls.resources.dll": {"locale": "ro-RO"}, "lib/netcoreapp3.0/ro-RO/ModernWpf.resources.dll": {"locale": "ro-RO"}, "lib/netcoreapp3.0/ru-RU/ModernWpf.Controls.resources.dll": {"locale": "ru-RU"}, "lib/netcoreapp3.0/ru-RU/ModernWpf.resources.dll": {"locale": "ru-RU"}, "lib/netcoreapp3.0/sk-SK/ModernWpf.Controls.resources.dll": {"locale": "sk-SK"}, "lib/netcoreapp3.0/sk-SK/ModernWpf.resources.dll": {"locale": "sk-SK"}, "lib/netcoreapp3.0/sl-SI/ModernWpf.Controls.resources.dll": {"locale": "sl-SI"}, "lib/netcoreapp3.0/sl-SI/ModernWpf.resources.dll": {"locale": "sl-SI"}, "lib/netcoreapp3.0/sq-AL/ModernWpf.Controls.resources.dll": {"locale": "sq-AL"}, "lib/netcoreapp3.0/sq-AL/ModernWpf.resources.dll": {"locale": "sq-AL"}, "lib/netcoreapp3.0/sr-Latn-RS/ModernWpf.Controls.resources.dll": {"locale": "sr-Latn-RS"}, "lib/netcoreapp3.0/sr-Latn-RS/ModernWpf.resources.dll": {"locale": "sr-Latn-RS"}, "lib/netcoreapp3.0/sv-SE/ModernWpf.Controls.resources.dll": {"locale": "sv-SE"}, "lib/netcoreapp3.0/sv-SE/ModernWpf.resources.dll": {"locale": "sv-SE"}, "lib/netcoreapp3.0/sw-KE/ModernWpf.Controls.resources.dll": {"locale": "sw-KE"}, "lib/netcoreapp3.0/sw-KE/ModernWpf.resources.dll": {"locale": "sw-KE"}, "lib/netcoreapp3.0/ta-IN/ModernWpf.Controls.resources.dll": {"locale": "ta-IN"}, "lib/netcoreapp3.0/ta-IN/ModernWpf.resources.dll": {"locale": "ta-IN"}, "lib/netcoreapp3.0/te-IN/ModernWpf.Controls.resources.dll": {"locale": "te-IN"}, "lib/netcoreapp3.0/te-IN/ModernWpf.resources.dll": {"locale": "te-IN"}, "lib/netcoreapp3.0/th-TH/ModernWpf.Controls.resources.dll": {"locale": "th-TH"}, "lib/netcoreapp3.0/th-TH/ModernWpf.resources.dll": {"locale": "th-TH"}, "lib/netcoreapp3.0/tr-TR/ModernWpf.Controls.resources.dll": {"locale": "tr-TR"}, "lib/netcoreapp3.0/tr-TR/ModernWpf.resources.dll": {"locale": "tr-TR"}, "lib/netcoreapp3.0/uk-UA/ModernWpf.Controls.resources.dll": {"locale": "uk-UA"}, "lib/netcoreapp3.0/uk-UA/ModernWpf.resources.dll": {"locale": "uk-UA"}, "lib/netcoreapp3.0/uz-Latn-UZ/ModernWpf.Controls.resources.dll": {"locale": "uz-Latn-UZ"}, "lib/netcoreapp3.0/uz-Latn-UZ/ModernWpf.resources.dll": {"locale": "uz-Latn-UZ"}, "lib/netcoreapp3.0/vi-VN/ModernWpf.Controls.resources.dll": {"locale": "vi-VN"}, "lib/netcoreapp3.0/vi-VN/ModernWpf.resources.dll": {"locale": "vi-VN"}, "lib/netcoreapp3.0/zh-CN/ModernWpf.Controls.resources.dll": {"locale": "zh-CN"}, "lib/netcoreapp3.0/zh-CN/ModernWpf.resources.dll": {"locale": "zh-CN"}, "lib/netcoreapp3.0/zh-TW/ModernWpf.Controls.resources.dll": {"locale": "zh-TW"}, "lib/netcoreapp3.0/zh-TW/ModernWpf.resources.dll": {"locale": "zh-TW"}}}, "NetCoreServer/5.1.0": {"runtime": {"lib/net5.0/NetCoreServer.dll": {"assemblyVersion": "5.1.0.0", "fileVersion": "5.1.0.0"}}}, "Serilog/2.10.0": {"runtime": {"lib/netstandard2.1/Serilog.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.10.0.0"}}}, "Serilog.Sinks.RichTextBox.Wpf/1.1.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/net5.0-windows7.0/Serilog.Sinks.RichTextBox.Wpf.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.1.0.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.6": {"dependencies": {"SQLitePCLRaw.core": "2.0.6", "SQLitePCLRaw.lib.e_sqlite3": "2.0.6", "SQLitePCLRaw.provider.e_sqlite3": "2.0.6"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.0.6.1341", "fileVersion": "2.0.6.1341"}}}, "SQLitePCLRaw.core/2.0.6": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.0.6.1341", "fileVersion": "2.0.6.1341"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.0.6": {"runtimeTargets": {"runtimes/alpine-x64/native/libe_sqlite3.so": {"rid": "alpine-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.0.6": {"dependencies": {"SQLitePCLRaw.core": "2.0.6"}, "runtime": {"lib/net5.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.0.6.1341", "fileVersion": "2.0.6.1341"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Composition/1.0.31": {"dependencies": {"System.Composition.AttributedModel": "1.0.31", "System.Composition.Convention": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Composition.TypedParts": "1.0.31"}}, "System.Composition.AttributedModel/1.0.31": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Convention/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Convention.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Hosting/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Hosting.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Runtime/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.TypedParts/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.TypedParts.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Memory/4.5.3": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {"assemblyVersion": "4.0.13.0", "fileVersion": "4.6.24705.1"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {"assemblyVersion": "4.1.1.0", "fileVersion": "4.6.24705.1"}}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.WindowsRuntime/4.6.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.WindowsRuntime.dll": {"assemblyVersion": "4.0.14.0", "fileVersion": "4.700.19.46214"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.14.0", "fileVersion": "4.700.19.46214"}}}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "System.Runtime.WindowsRuntime": "4.6.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll": {"assemblyVersion": "4.0.4.0", "fileVersion": "4.700.19.46214"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.4.0", "fileVersion": "4.700.19.46214"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {"assemblyVersion": "4.0.12.0", "fileVersion": "4.6.24705.1"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}}}, "libraries": {"Dapper/2.0.123": {"type": "package", "serviceable": true, "sha512": "sha512-RDFF4rBLLmbpi6pwkY7q/M6UXHRJEOerplDGE5jwEkP/JGJnBauAClYavNKJPW1yOTWRPIyfj4is3EaJxQXILQ==", "path": "dapper/2.0.123", "hashPath": "dapper.2.0.123.nupkg.sha512"}, "FontAwesome6.Fonts/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-38A7uCztGD8TqieSadqtYymEb9B9JBvzICj7pD5GPgpul1tYHM6zR7RMz30dfp1J+N1DJOiZeQkDQ9v10N0CGw==", "path": "fontawesome6.fonts/1.0.1", "hashPath": "fontawesome6.fonts.1.0.1.nupkg.sha512"}, "HandyControl/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LY38yByPYXOP2kfQkneedgSE+Ps6XWsXId9hBoEgvef4/mdToqWNSueA6kg1u/nKCV1E8zdpI+ZvevDnlyF10Q==", "path": "handycontrol/3.3.0", "hashPath": "handycontrol.3.3.0.nupkg.sha512"}, "Microsoft.Composition/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-R8V1rw4ldOoKIg0QzDY033V8uKrNR0VRKuKVuA1wzuIVeBLwYGghF0y+WbmPI245xSnjRh5eMxxBaxDX9DYZmA==", "path": "microsoft.composition/1.0.31", "hashPath": "microsoft.composition.1.0.31.nupkg.sha512"}, "Microsoft.Data.Sqlite/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-9jhBB4WYRXMIB9AwIRz4omdxNqZSFFj8ESilpH2hF9x+btVXzbrNCk/yK7Km4UQTr2QUoRlZrjFBmhecIwgCCQ==", "path": "microsoft.data.sqlite/6.0.4", "hashPath": "microsoft.data.sqlite.6.0.4.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-3TZX7R2aX1TX5m4A5Kj+SY633NJDeHDP6JiDRCwUnJGKC3IrHgnO8p+oT2hRZpN168qx4Ixe4T9C+xZdZc26gw==", "path": "microsoft.data.sqlite.core/6.0.4", "hashPath": "microsoft.data.sqlite.core.6.0.4.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TsETIgVJb/AKoYfSP+iCxkuly5d3inZjTdx/ItZLk2CxY85v8083OBS3uai84kK3/baLnS5/b5XGs6zR7SuuHQ==", "path": "microsoft.netcore.platforms/3.0.0", "hashPath": "microsoft.netcore.platforms.3.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Windows.SDK.Contracts/10.0.18362.2005": {"type": "package", "serviceable": true, "sha512": "sha512-3R3uDDGRSdeRdHpor1SyN3qg/VmseKjcKFIAQJ0iRnzoCPvnlCcnSGaNsjqVSzHQHbBSBtSKVBxBS2GyEw8qhw==", "path": "microsoft.windows.sdk.contracts/10.0.18362.2005", "hashPath": "microsoft.windows.sdk.contracts.10.0.18362.2005.nupkg.sha512"}, "ModernWpfUI/0.9.4": {"type": "package", "serviceable": true, "sha512": "sha512-HJ07Be9KOiGKGcMLz/AwY+84h3yGHRPuYpYXCE6h1yPtaFwGMWfanZ70jX7W5XWx8+Qk1vGox+WGKgxxsy6EHw==", "path": "modernwpfui/0.9.4", "hashPath": "modernwpfui.0.9.4.nupkg.sha512"}, "NetCoreServer/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-eZZEe/nslw5MO/DL2RUHmypJruerACyYleRILJT0g6/c9BiUO2LM0RuOWiUTx/U0wBa7FrkxsyPU8/Q/M4q+EQ==", "path": "netcoreserver/5.1.0", "hashPath": "netcoreserver.5.1.0.nupkg.sha512"}, "Serilog/2.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "path": "serilog/2.10.0", "hashPath": "serilog.2.10.0.nupkg.sha512"}, "Serilog.Sinks.RichTextBox.Wpf/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-E/ngRTlTHqKXz1+LuebDU8eCIL4sNlxphrVx7Z/KjVxzqajLGxABJgtTD7tF+AQ6iXU2IPwgvLpJxEVb85tc3A==", "path": "serilog.sinks.richtextbox.wpf/1.1.0", "hashPath": "serilog.sinks.richtextbox.wpf.1.1.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-zssYqiaucyGArZfg74rJuzK0ewgZiidsRVrZTmP7JLNvK806gXg6PGA46XzoJGpNPPA5uRcumwvVp6YTYxtQ5w==", "path": "sqlitepclraw.bundle_e_sqlite3/2.0.6", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.0.6.nupkg.sha512"}, "SQLitePCLRaw.core/2.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Vh8n0dTvwXkCGur2WqQTITvk4BUO8i8h9ucSx3wwuaej3s2S6ZC0R7vqCTf9TfS/I4QkXO6g3W2YQIRFkOcijA==", "path": "sqlitepclraw.core/2.0.6", "hashPath": "sqlitepclraw.core.2.0.6.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-xlstskMKalKQl0H2uLNe0viBM6fvAGLWqKZUQ3twX5y1tSOZKe0+EbXopQKYdbjJytNGI6y5WSKjpI+kVr2Ckg==", "path": "sqlitepclraw.lib.e_sqlite3/2.0.6", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.0.6.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-peXLJbhU+0clVBIPirihM1NoTBqw8ouBpcUsVMlcZ4k6fcL2hwgkctVB2Nt5VsbnOJcPspQL5xQK7QvLpxkMgg==", "path": "sqlitepclraw.provider.e_sqlite3/2.0.6", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.0.6.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Composition/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-I+D26qpYdoklyAVUdqwUBrEIckMNjAYnuPJy/h9dsQItpQwVREkDFs4b4tkBza0kT2Yk48Lcfsv2QQ9hWsh9Iw==", "path": "system.composition/1.0.31", "hashPath": "system.composition.1.0.31.nupkg.sha512"}, "System.Composition.AttributedModel/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-NHWhkM3ZkspmA0XJEsKdtTt1ViDYuojgSND3yHhTzwxepiwqZf+BCWuvCbjUt4fe0NxxQhUDGJ5km6sLjo9qnQ==", "path": "system.composition.attributedmodel/1.0.31", "hashPath": "system.composition.attributedmodel.1.0.31.nupkg.sha512"}, "System.Composition.Convention/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-GLjh2Ju71k6C0qxMMtl4efHa68NmWeIUYh4fkUI8xbjQrEBvFmRwMDFcylT8/PR9SQbeeL48IkFxU/+gd0nYEQ==", "path": "system.composition.convention/1.0.31", "hashPath": "system.composition.convention.1.0.31.nupkg.sha512"}, "System.Composition.Hosting/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-fN1bT4RX4vUqjbgoyuJFVUizAl2mYF5VAb+bVIxIYZSSc0BdnX+yGAxcavxJuDDCQ1K+/mdpgyEFc8e9ikjvrg==", "path": "system.composition.hosting/1.0.31", "hashPath": "system.composition.hosting.1.0.31.nupkg.sha512"}, "System.Composition.Runtime/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0LEJN+2NVM89CE4SekDrrk5tHV5LeATltkp+9WNYrR+Huiyt0vaCqHbbHtVAjPyeLWIc8dOz/3kthRBj32wGQg==", "path": "system.composition.runtime/1.0.31", "hashPath": "system.composition.runtime.1.0.31.nupkg.sha512"}, "System.Composition.TypedParts/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0Zae/FtzeFgDBBuILeIbC/T9HMYbW4olAmi8XqqAGosSOWvXfiQLfARZEhiGd0LVXaYgXr0NhxiU1LldRP1fpQ==", "path": "system.composition.typedparts/1.0.31", "hashPath": "system.composition.typedparts.1.0.31.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.WindowsRuntime/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWrs1TmbxP65ZZjIglNyvDkFNoV5q2Pofg5WO7I8RKQOpLdFprQSh3xesOoClBqR4JHr4nEB1Xk1MqLPW1jPuQ==", "path": "system.runtime.windowsruntime/4.6.0", "hashPath": "system.runtime.windowsruntime.4.6.0.nupkg.sha512"}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-r4tNw5v5kqRJ9HikWpcyNf3suGw7DjX93svj9iBjtdeLqL8jt9Z+7f+s4wrKZJr84u8IMsrIjt8K6jYvkRqMSg==", "path": "system.runtime.windowsruntime.ui.xaml/4.6.0", "hashPath": "system.runtime.windowsruntime.ui.xaml.4.6.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}}}