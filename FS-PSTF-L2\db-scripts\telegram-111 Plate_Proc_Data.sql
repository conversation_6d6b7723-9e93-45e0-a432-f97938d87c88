USE [FSPSTF-L2]
GO

/****** Object:  Table [dbo].[Tele111_Plate_Proc_Data]    Script Date: 2025/7/16 10:53:24 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Tele111_Plate_Proc_Data](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[mat_no] [char](20) NULL,
	[shift_no] [tinyint] NOT NULL,
	[shift_group] [tinyint] NOT NULL,
	[create_time] [datetime] NOT NULL,
	[fnc_z1_time] [datetime] NOT NULL,
	[fnc_z2_time] [datetime] NOT NULL,
	[fnc_z3_time] [datetime] NOT NULL,
	[fnc_z4_time] [datetime] NOT NULL,
	[fnc_z5_time] [datetime] NOT NULL,
	[fnc_z6_time] [datetime] NOT NULL,
	[discharge_time] [datetime] NOT NULL,
	[fnc_min_temp_z1] [smallint] NOT NULL,
	[fnc_max_temp_z1] [smallint] NOT NULL,
	[fnc_min_temp_z2] [smallint] NOT NULL,
	[fnc_max_temp_z2] [smallint] NOT NULL,
	[fnc_min_temp_z3] [smallint] NOT NULL,
	[fnc_max_temp_z3] [smallint] NOT NULL,
	[fnc_min_temp_z4] [smallint] NOT NULL,
	[fnc_max_temp_z4] [smallint] NOT NULL,
	[fnc_min_temp_z5] [smallint] NOT NULL,
	[fnc_max_temp_z5] [smallint] NOT NULL,
	[fnc_min_temp_z6] [smallint] NOT NULL,
	[fnc_max_temp_z6] [smallint] NOT NULL,
	[total_heat_time] [int] NOT NULL,
	[record_time] [datetime] NOT NULL,
 CONSTRAINT [PK_Tele111_Plate_Proc_Data] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Tele111_Plate_Proc_Data] ADD  CONSTRAINT [DF_Tele111_Plate_Proc_Data_record_time]  DEFAULT (getdate()) FOR [record_time]
GO
