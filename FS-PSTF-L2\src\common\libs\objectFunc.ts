export function removeProperties<T extends object>(
    obj: T,
    ...props: (keyof T)[]
): Partial<T> {
    const newObj = { ...obj }
    for (const prop of props) {
        delete newObj[prop]
    }
    return newObj
}

export function getEnumKeyByEnumValue<
    TEnumKey extends string,
    TEnumVal extends string | number,
>(myEnum: { [key in TEnumKey]: TEnumVal }, enumValue: TEnumVal): string {
    const keys = (Object.keys(myEnum) as TEnumKey[]).filter(
        (x) => myEnum[x] === enumValue,
    )
    return keys.length > 0 ? keys[0] : ''
}
