import * as fs from 'fs'
import { Page } from 'playwright'
import { ElectronApplication } from 'playwright-core'
import { test, expect } from '@playwright/test'
import helpers from './helpers'
import dbHelpers from './dbHelpers'
import { mainMenu } from './constants'

let appWindow: Page
let electronApp: ElectronApplication

// db connection
const knex = dbHelpers.knexConnect()
// mock up data
// 102: 2 record; 103: 3 records
const seeds = [
    {
        direction: 'R',
        tel_no: 101,
        tel_len: 80,
        send_id: 'BC',
        rec_id: 'L2',
        tel_counter: 1,
        create_time: new Date('2023-06-01 12:00:00'),
    },
    {
        direction: 'R',
        tel_no: 101,
        tel_len: 80,
        send_id: 'BC',
        rec_id: 'L2',
        tel_counter: 2,
        create_time: new Date('2023-06-01 12:01:00'),
    },
    {
        direction: 'R',
        tel_no: 103,
        tel_len: 80,
        send_id: 'TC',
        rec_id: 'L2',
        tel_counter: 1,
        create_time: new Date('2023-06-01 12:11:00'),
    },
    {
        direction: 'R',
        tel_no: 101,
        tel_len: 80,
        send_id: 'BC',
        rec_id: 'L2',
        tel_counter: 3,
        create_time: new Date('2023-06-02 13:01:00'),
    },
    {
        direction: 'R',
        tel_no: 103,
        tel_len: 80,
        send_id: 'TC',
        rec_id: 'L2',
        tel_counter: 2,
        create_time: new Date('2023-06-02 14:01:00'),
    },
]

test.beforeAll(async () => {
    // clear table TelegramHistory and repopulate with new records

    // purge table TelegramHistory
    await dbHelpers.purgeTable(knex, 'TelegramHistory')
    await dbHelpers.populateTable(knex, 'TelegramHistory', seeds)
    // launch app
    const app = await helpers.launchApp()
    appWindow = app.appWindow
    electronApp = app.electronApp
})

test('Check tab open', async () => {
    // open workspace
    await appWindow
        .getByRole('menuitem', { name: mainMenu.Datalog.label })
        .click()
    // open tab: fabrics
    await appWindow.getByText('报文历史').click()
    // 报文历史 should be opened
    await expect(
        appWindow.locator('.n-tabs-tab-wrapper').nth(0),
        'expect tab 报文历史 to be opened',
    ).toHaveText('报文历史')
})

test('Check filter', async () => {
    // fill filter, tel_no
    await appWindow.getByPlaceholder('报文号').fill('101')
    // check rows
    await expect(
        appWindow.locator('tbody tr'),
        'expected filtered rows (tel 101): 3',
    ).toHaveCount(3)
    // clear filter
    await appWindow.getByPlaceholder('报文号').fill('')
    // check rows
    await expect(
        appWindow.locator('tbody tr'),
        'expected all rows: 5',
    ).toHaveCount(5)
    // fill filter, date
    await appWindow.getByPlaceholder('日期').fill('2023-06-01')
    // check rows
    await expect(
        appWindow.locator('tbody tr'),
        'expected filtered rows (date): 3',
    ).toHaveCount(3)
    // combined filter
    await appWindow.getByPlaceholder('报文号').fill('103')
    // check rows
    await expect(
        appWindow.locator('tbody tr'),
        'expected filtered rows (tel 103 & date): 1',
    ).toHaveCount(1)
})

test('Check auto refresh switch', async () => {
    // refresh button should be disabled by default
    await expect(
        appWindow.getByRole('button', { name: '刷新' }),
        '刷新 button should be disabled',
    ).toBeDisabled()
    // switch
    await appWindow.getByTestId('auto-refresh').click()
    // refresh button should be disabled by default
    await expect(
        appWindow.getByRole('button', { name: '刷新' }),
        '刷新 button should be Enabled now',
    ).toBeEnabled()
})

test('Check auto refresh function', async () => {
    // clear filter
    await appWindow.getByPlaceholder('日期').fill('')
    await appWindow.getByPlaceholder('报文号').fill('')
    // enable auto refresh
    await appWindow.getByTestId('auto-refresh').click()
    // change to 5s
    await appWindow.getByTestId('refresh-interval').locator('svg').click()
    await appWindow.locator('div').filter({ hasText: /^5秒$/ }).first().click()
    // new record
    const newRecord = [
        {
            direction: 'R',
            tel_no: 999,
            tel_len: 80,
            send_id: 'TC',
            rec_id: 'L2',
            tel_counter: 2,
            create_time: new Date('2023-06-03 14:01:00'),
        },
    ]
    // add new record
    dbHelpers.populateTable(knex, 'TelegramHistory', newRecord)

    // wait for refresh
    await helpers.delay(5000)
    // check rows
    await expect(
        appWindow.locator('tbody tr'),
        'expected filtered rows (tel 103 & date): 6',
    ).toHaveCount(6)
})

test.afterAll(async () => {
    // await helpers.delay(10000)
    await electronApp.close()
})
